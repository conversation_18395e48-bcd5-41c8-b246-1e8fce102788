/** 体系管理 */
import { RouteRecordRaw } from "vue-router";
import { Layout } from "@/routers/constant";

const systemRouter: Array<RouteRecordRaw> = [
	{
		path: "/system-module",
		name: "SystemModule",
		component: Layout,
		meta: {
			title: "体系管理"
		},
		redirect: "/system-module/my-apply",
		children: [
			{
				path: "/system-module/my-apply",
				name: "MyApply",
				meta: {
					title: "我的申请",
					key: "my-apply"
				},
				component: () => import("@/views/system-module/my-apply/index.vue")
			},
			{
				path: "/system-module/my-apply/apply-form",
				name: "ApplyForm",
				meta: {
					title: "发起申请",
					key: "apply-form"
				},
				component: () => import("@/views/system-module/my-apply/apply-form/index.vue")
			},
			{
				path: "/system-module/my-apply/detail",
				name: "ApplyDetail",
				meta: {
					title: "申请详情",
					key: "apply-deatil"
				},
				component: () => import("@/views/system-module/approval-detail/index.vue")
			},
			{
				path: "/system-module/approval-mgr",
				name: "ApprovalMgr",
				meta: {
					title: "审批抄送",
					key: "approval-mgr"
				},
				component: () => import("@/views/system-module/approval-mgr/index.vue")
			},
			// {
			// 	path: "/system-module/approval-mgr/apply",
			// 	name: "ApprovalForm",
			// 	meta: {
			// 		title: "流程审批",
			// 		key: "approval-form"
			// 	},
			// 	component: () => import("@/views/system-module/approval-mgr/approval-form/index.vue")
			// },
			{
				path: "/system-module/approval-record",
				name: "ApprovalRecordList",
				meta: {
					title: "体系台账",
					key: "approval-record"
				},
				component: () => import("@/views/system-module/approval-record/index.vue")
			},
			{
				path: "/system-module/system-file",
				name: "SystemFileList",
				meta: {
					title: "体系文件",
					key: "system-file"
				},
				component: () => import("@/views/system-module/system-file/index.vue")
			}
		]
	}
];

export default systemRouter;
