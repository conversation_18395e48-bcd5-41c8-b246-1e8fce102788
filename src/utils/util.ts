import { isArray } from "@/utils/is";
import { AuthStore } from "@/store/modules/auth";
import router from "@/routers/index";
import { YN } from "@/enums/global-enums";

/**
 * 当前路由页面是否存在该权限
 * @returns
 */
export function hasAuth(authKey: string, routePath?: string): boolean {
	if (import.meta.env.VITE_IGNORE_AUTH !== "need_auth") return true;
	const path = routePath || router.currentRoute.value.path;
	return AuthStore().authButtons[path] && AuthStore().authButtons[path][authKey];
}
/**
 * @description 获取localStorage
 * @param {String} key Storage名称
 * @return string
 */
export function localGet(key: string) {
	const value = window.localStorage.getItem(key);
	try {
		return JSON.parse(window.localStorage.getItem(key) as string);
	} catch (error) {
		return value;
	}
}

/**
 * @description 存储localStorage
 * @param {String} key Storage名称
 * @param {Any} value Storage值
 * @return void
 */
export function localSet(key: string, value: any) {
	window.localStorage.setItem(key, JSON.stringify(value));
}

/**
 * @description 清除localStorage
 * @param {String} key Storage名称
 * @return void
 */
export function localRemove(key: string) {
	window.localStorage.removeItem(key);
}

/**
 * @description 清除所有localStorage
 * @return void
 */
export function localClear() {
	window.localStorage.clear();
}

/**
 * @description 对象数组深克隆
 * @param {Object} obj 源对象
 * @return object
 */
export function deepCopy<T>(obj: any): T {
	let newObj: any;
	try {
		newObj = obj.push ? [] : {};
	} catch (error) {
		newObj = {};
	}
	for (let attr in obj) {
		if (typeof obj[attr] === "object") {
			newObj[attr] = deepCopy(obj[attr]);
		} else {
			newObj[attr] = obj[attr];
		}
	}
	return newObj;
}

/**
 * @description 判断数据类型
 * @param {Any} val 需要判断类型的数据
 * @return string
 */
export function isType(val: any) {
	if (val === null) return "null";
	if (typeof val !== "object") return typeof val;
	else return Object.prototype.toString.call(val).slice(8, -1).toLocaleLowerCase();
}

/**
 * @description 生成随机数
 * @param {Number} min 最小值
 * @param {Number} max 最大值
 * @return number
 */
export function randomNum(min: number, max: number): number {
	let num = Math.floor(Math.random() * (min - max) + max);
	return num;
}

/**
 * @description 获取当前时间
 * @return string
 */
export function getTimeState() {
	// 获取当前时间
	let timeNow = new Date();
	// 获取当前小时
	let hours = timeNow.getHours();
	// 判断当前时间段
	if (hours >= 6 && hours <= 10) return `早上好 ⛅`;
	if (hours >= 10 && hours <= 14) return `中午好 🌞`;
	if (hours >= 14 && hours <= 18) return `下午好 🌞`;
	if (hours >= 18 && hours <= 24) return `晚上好 🌛`;
	if (hours >= 0 && hours <= 6) return `凌晨好 🌛`;
}

/**
 * @description 获取浏览器默认语言
 * @return string
 */
export function getBrowserLang() {
	let browserLang = navigator.language ? navigator.language : navigator.browserLanguage;
	let defaultBrowserLang = "";
	if (browserLang.toLowerCase() === "cn" || browserLang.toLowerCase() === "zh" || browserLang.toLowerCase() === "zh-cn") {
		defaultBrowserLang = "zh";
	} else {
		defaultBrowserLang = "en";
	}
	return defaultBrowserLang;
}

/**
 * @description 递归查询当前路由所对应的路由
 * @param {Array} menuList 菜单列表
 * @param {String} path 当前地址
 * @return array
 */
export function getTabPane<T, U>(menuList: any[], path: U): T {
	let result: any;
	for (let item of menuList || []) {
		if (item.path === path) result = item;
		const res = getTabPane(item.children, path);
		if (res) result = res;
	}
	return result;
}

/**
 * @description 使用递归处理路由菜单，生成一维数组。
 * @param {Array} menuList 所有菜单列表
 * @param {Array} newArr 菜单的一维数组
 * @return array
 */
export function handleRouter(routerList: Menu.MenuOptions[], newArr: string[] = []) {
	if (!routerList || routerList.length === 0) return newArr;
	routerList.forEach((item: Menu.MenuOptions) => {
		typeof item === "object" && item.path && newArr.push(item.path);
		item.children && item.children.length && handleRouter(item.children, newArr);
	});
	return newArr;
}

/**
 * @description 使用递归处理路由菜单，获取开启页面缓存的页面路由名称,生成一维数组。
 * @param {Array} menuList 所有菜单列表
 * @param {Array} newArr 菜单的一维数组
 * @return array
 */
export function handleKeepAliveRouter(routerList: Menu.MenuOptions[], newArr: string[] = []) {
	if (!routerList || routerList.length === 0) return newArr;
	routerList.forEach((item: Menu.MenuOptions) => {
		typeof item === "object" && item.name && item.keepAlive === YN.Yes && newArr.push(item.name);
		item.children && item.children.length && handleKeepAliveRouter(item.children, newArr);
	});
	return newArr;
}

/**
 * @description 使用递归处理路由菜单，生成一维数组。
 * @param {Array} menuList 所有菜单列表
 * @param {Array} newArr 菜单的一维数组
 * @return array
 */
export function handleMenu(menuList: Menu.MenuOptions[], newArr: Menu.MenuOptions[] = []) {
	if (!menuList || menuList.length === 0) return newArr;
	menuList.forEach((item: Menu.MenuOptions) => {
		item.hidden !== 1 && newArr.push(item);
		item.children && item.children.length && (item.children = handleMenu(item.children));
	});
	return newArr;
}

/**
 * 读取菜单中的btns并记录。通过记录的btns来判定是否有对应权限
 * @param menuList 所有菜单列表
 * @param resObject 重组返回的结构体
 * @returns
 */
export function hanldeAuthButtons(menuList: Menu.MenuOptions[], resObject: { [key: string]: any } = {}) {
	if (!menuList || menuList.length === 0) return resObject;
	menuList.forEach((item: Menu.MenuOptions) => {
		if (!item.btns) return;
		item.btns.forEach((btn: Menu.MenuBtn) => {
			resObject[item.path] ?? (resObject[item.path] = {});
			resObject[item.path][btn.key!] = true;
		});
	});
	return resObject;
}

/**
 * @description 扁平化数组对象
 * @param {Array} arr 数组对象
 * @return array
 */
export function getFlatArr(arr: any) {
	return arr.reduce((pre: any, current: any) => {
		let flatArr = [...pre, current];
		if (current.children) flatArr = [...flatArr, ...getFlatArr(current.children)];
		return flatArr;
	}, []);
}

/**
 * @description 格式化表格单元格默认值
 * @param {Number} row 行
 * @param {Number} col 列
 * @param {String} callValue 当前单元格值
 * @return string
 * */
export function defaultFormat(row: number, col: number, callValue: any) {
	// 如果当前值为数组,使用 / 拼接（根据需求自定义）
	if (isArray(callValue)) return callValue.length ? callValue.join(" / ") : "--";
	return callValue ?? "--";
}

/**
 * @description 处理无数据情况
 * @param {String} callValue 需要处理的值
 * @return string
 * */
export function formatValue(callValue: any) {
	// 如果当前值为数组,使用 / 拼接（根据需求自定义）
	if (isArray(callValue)) return callValue.length ? callValue.join(" / ") : "--";
	return callValue ?? "--";
}

/**
 * @description 根据枚举列表查询当需要的数据（如果指定了 label 和 value 的 key值，会自动识别格式化）
 * @param {String} callValue 当前单元格值
 * @param {Array} enumData 枚举列表
 * @param {String} type 过滤类型（目前只有 tag）
 * @return string
 * */
export function filterEnum(callValue: any, enumData: any, searchProps?: { [key: string]: any }, type?: string): string {
	const value = searchProps?.value ?? "value";
	const label = searchProps?.label ?? "label";
	let filterData: any = {};
	if (Array.isArray(enumData)) filterData = enumData.find((item: any) => item[value] === callValue);
	if (type == "tag") return filterData?.tagType ? filterData.tagType : "";
	return filterData ? filterData[label] : "--";
}

/**
 * 经纬度格式转换
 * @param lat 纬度
 * @param lng  经度
 * @param fmt1 当前格式 DDD
 * @param fmt2 需要转换的格式 DMS
 * @returns
 */
export function LatlngCnvTool(lat: number | string, lng: number | string, fmt1: string = "DDD", fmt2: string = "DMS") {
	let lngFormat = "";
	let latFormat = "";
	let latSymbol = "N";
	let lngSymbol = "E";
	if (typeof lat === "string") {
		lat = Number(lat);
		if (lat < 0) latSymbol = "S";
		if (lat === 0) latSymbol = "";
		lat = Math.abs(lat);
	}
	if (typeof lng === "string") {
		lng = Number(lng);
		if (lng < 0) lngSymbol = "W";
		if (lng === 0) lngSymbol = "";
		lng = Math.abs(lng);
	}
	//按格式进行转化
	if (fmt1 == "DDD") {
		if (fmt2 == "DMS") {
			lngFormat = DDDToDMS(lng);
			latFormat = DDDToDMS(lat);
		}
		// else if (fmt2 == "DMM") {
		// 	lngFormat = DDDToDMM(lng);
		// 	latFormat = DDDToDMM(lat);
		// } else if (fmt2 == "DDD") {
		// 	lngFormat = DDDToDDD(lng.toString());
		// 	latFormat = DDDToDDD(lat.toString());
		// }
	} else {
		return "";
	}
	// else if (fmt1 == "DMS") {
	// 	if (fmt2 == "DDD") {
	// 		lng = DMSToDDD(lng.toString());
	// 		lat = DMSToDDD(lat.toString());
	// 	} else {
	// 		return "";
	// 	}
	// } else if (fmt1 == "DMM") {
	// 	if (fmt2 == "DDD") {
	// 		lng = DMMToDDD(lng.toString());
	// 		lat = DMMToDDD(lat.toString());
	// 	} else {
	// 		return "";
	// 	}
	// }
	return latFormat + latSymbol + "," + lngFormat + lngSymbol;
}
//DDD转DMS °、′、″ 这里为用 '代替了′ " 代替 ″ 8°14'2.8428"
export function DDDToDMS(_data: number) {
	let value = parseFloat(_data + "");
	let _d = Math.floor(value); //度
	let _m = Math.floor((value - _d) * 60); //分
	//let _s = Math.round((value - _d) * 3600 % 60);//秒 保留整数
	let _s = parseFloat((((value - _d) * 3600) % 60).toString()).toFixed(2); //精确小数点后面两位
	return _d + "°" + _m + "′" + _s + '"';
}

/**
 * 分片获取所有数据
 * @param api 请求数据的接口
 * @param params 入参
 * @param length 分片的长度
 * @returns res {list: [] , total: number}
 */
export async function getListByFragmentation(api: (params: any) => Promise<any>, params: any, length: number = 100) {
	const res = {
		list: [] as Array<any>,
		total: 0
	};
	let offset = 0;
	// 这个length用来获取total的
	let startLength = 1;
	// 需要发多少次请求
	let n = 0;
	// 用来接收获取到的数据
	// const list = [] as Array<any>;
	// 先发一次获取total的请求 offset 0 跟 length 1
	const { code, data } = await api({ ...params, offset, length: startLength });
	if (code === 0) {
		res.total = data.total;
		n = Math.ceil(res.total / length);
	}
	const promises = [];
	for (let i = 0; i < n; i++) {
		offset = i * length;
		promises.push(api({ ...params, offset, length }));
	}
	// 获取所有数据
	await Promise.all(promises).then(result => {
		for (let item of result) {
			if (item.code === 0 && item.data?.list) {
				res.list = [...res.list, ...item.data.list];
			}
		}
	});
	return res;
}

/**
 * 打印调试信息
 * @description 该函数使用 Function 构造器创建动态函数，避免被静态分析删除
 * @description 确保在生产环境中仍然能够打印调试信息
 * @param args log参数
 * @returns
 */
export function debugLog(...args: any[]) {
	new Function("args", "console.log.apply(console, args)")(args);
}

/**
 * @description 判断是否是图片附件
 * @param url 附件地址
 */
export function isPic(url: any) {
	if (!url) return false;
	let pattern = ".(jpg|jpeg|gif|png|webp)$";
	let result = url.toLowerCase().search(pattern, url);
	return result === -1 ? false : true;
}

/**
 * @description 下载文件
 * @param url 附件地址
 * @param fileName 下载的文件名
 */
export function downFile(url: string, fileName: string) {
	const x = new XMLHttpRequest();
	x.open("GET", url, true);
	x.responseType = "blob";
	x.onload = function () {
		const url = window.URL.createObjectURL(x.response);
		const a = document.createElement("a");
		a.href = url;
		a.download = fileName;
		a.click();
	};
	x.send();
}
