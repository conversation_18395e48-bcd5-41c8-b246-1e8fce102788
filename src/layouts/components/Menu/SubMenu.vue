<template>
	<template v-for="subItem in menuList" :key="subItem.path">
		<el-sub-menu v-if="subItem.children && subItem.children.length > 0" :index="subItem.path">
			<template #title>
				<SvgIcon class="menu-icon" v-if="subItem.icon && isCustomIcon(subItem.icon)" :name="subItem.icon"></SvgIcon>
				<el-icon v-else-if="subItem.icon">
					<component :is="subItem.icon"></component>
				</el-icon>
				<div v-else class="split"></div>
				<span>{{ subItem.title }}</span>
				<RedPoint :count="subItem.badgeCount ?? 0" :is-mini="subItem.isMini ?? false"></RedPoint>
			</template>
			<SubMenu :menuList="subItem.children" />
		</el-sub-menu>
		<el-menu-item v-else :index="subItem.path" @click="handleClickMenu(subItem)">
			<SvgIcon class="menu-icon" v-if="subItem.icon && isCustomIcon(subItem.icon)" :name="subItem.icon"></SvgIcon>
			<el-icon v-else-if="subItem.icon">
				<component :is="subItem.icon"></component>
			</el-icon>
			<div v-else class="split"></div>
			<template #title>
				<span>{{ subItem.title }}</span>
				<RedPoint :count="subItem.badgeCount ?? 0" :is-mini="subItem.isMini ?? false"></RedPoint>
			</template>
		</el-menu-item>
	</template>
</template>

<script setup lang="ts">
import { YN } from "@/enums/global-enums";
import { useRouter } from "vue-router";
import SvgIcon from "@/components/SvgIcon/index.vue";
import { isCustomIcon } from "../../utils";
import RedPoint from "./RedPoint.vue";

defineProps<{ menuList: Menu.MenuOptions[] }>();

const router = useRouter();
const handleClickMenu = (subItem: Menu.MenuOptions) => {
	//菜单直接跳转网页而非系统内部路由时的处理
	if (subItem.openNewPage === YN.Yes) {
		const routerUrl = router.resolve({ path: subItem.path });
		window.open(routerUrl.href, "_blank");
		return;
	}
	router.push(subItem.path);
};
</script>

<style lang="scss" scoped>
.menu-icon {
	width: 24px !important;
	height: 18px !important;
	margin-right: 5px;
	font-size: 18px;
}
.split {
	width: 29px;
}
</style>
