<!-- Menu红点组件 支持小红点/数字两种形式 -->
<template>
	<div v-if="!isMini && (isShowZero ? true : count !== 0)" class="red-point">{{ showCount }}</div>
	<div v-else-if="isMini && count !== 0" class="small-point"></div>
</template>

<script lang="ts" setup>
import { computed } from "vue";

const props = defineProps({
	count: { type: Number, required: true },
	max: { type: Number, default: 99 },
	isShowZero: { type: Boolean, default: false }, //为0时是否显示
	isMini: { type: Boolean, default: false } //是否以小圆点形式显示
});

const showCount = computed(() => {
	return Math.min(props.count, props.max) + (props.count > props.max ? "+" : "");
});
</script>

<style lang="scss" scoped>
.red-point {
	box-sizing: border-box;
	min-width: 18px;
	height: 18px;
	padding: 0 4px;
	margin-left: 8px;
	font-size: 12px;
	line-height: 15px;
	color: $color-blank;
	text-align: center;
	background-color: var(--el-color-error);
	border: 1px solid $color-blank;
	border-radius: 50px;
}
.small-point {
	box-sizing: border-box;
	width: 8px;
	height: 8px;
	padding: 0 4px;
	margin-left: 8px;
	background-color: var(--el-color-error);
	border-radius: 4px;
	transform: translate(-4px, -4px);
}
</style>
