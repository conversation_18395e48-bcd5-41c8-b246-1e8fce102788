{"code": 200, "menus": [{"id": 64, "uuid": "5a4db55e-70a8-4ffc-8295-b70634a37654", "createdAt": "", "updatedAt": "", "deletedAt": "", "platformId": 1, "endpointId": 1, "parentId": 0, "parentTitle": "", "path": "/dashboard/index", "name": "Dashboard", "hidden": 2, "component": "@/views/dashboard/index.vue", "sortNum": 100, "keepAlive": 2, "title": "首页", "icon": "home", "closeTab": 2, "openNewPage": 2, "params": null, "btns": [{"id": 956, "createdAt": "", "updatedAt": "", "menuId": 64, "key": "船舶数量统计", "api": {"id": 214, "uuid": "b47b92c4-57cd-4133-88cf-9004a8a7e8ca", "createdAt": "2023-04-18 09:32:55", "updatedAt": "2023-04-18 10:01:01", "deletedAt": "", "path": "/ship-api/v1/ship/count", "description": "船舶数量统计", "apiGroup": "ship/ship", "method": "GET", "necessary": 1}}, {"id": 961, "createdAt": "", "updatedAt": "", "menuId": 64, "key": "调度进度列表", "api": {"id": 220, "uuid": "ac2df47c-f1f7-42c1-bc9f-d9f43f17fa62", "createdAt": "2023-04-18 09:58:14", "updatedAt": "2023-08-11 17:41:57", "deletedAt": "", "path": "/ship-api/v1/dispatch/static/progress", "description": "调度统计(进行中)", "apiGroup": "ship/dispatch", "method": "GET", "necessary": 2}}, {"id": 966, "createdAt": "", "updatedAt": "", "menuId": 64, "key": "船员保险统计", "api": {"id": 103, "uuid": "9850cb42-cf3d-4e17-93e1-90a186ec1a6a", "createdAt": "2023-02-20 14:01:14", "updatedAt": "2023-08-11 17:41:57", "deletedAt": "", "path": "/user-api/v1/shipman/insurance/statisic", "description": "船员保险统计", "apiGroup": "user/shipman_insurance", "method": "GET", "necessary": 2}}, {"id": 962, "createdAt": "", "updatedAt": "", "menuId": 64, "key": "海事告警列表", "api": {"id": 212, "uuid": "7686821f-7042-49f4-8a2a-fc8a4b8d1e72", "createdAt": "2023-04-18 09:31:16", "updatedAt": "2023-06-29 10:04:13", "deletedAt": "", "path": "/ship-api/v1/msa/announce/list/{offset}/{length}", "description": "警告/通告列表", "apiGroup": "msa/announce", "method": "POST", "necessary": 2}}, {"id": 957, "createdAt": "", "updatedAt": "", "menuId": 64, "key": "船员数量统计", "api": {"id": 94, "uuid": "4cf8dc82-58dd-40fe-a38e-42d54a7d3761", "createdAt": "2023-02-20 13:58:23", "updatedAt": "2023-08-10 17:09:00", "deletedAt": "", "path": "/user-api/v1/shipman/list/{offset}/{length}", "description": "船员列表（船员库）", "apiGroup": "user/shipman", "method": "POST", "necessary": 2}}, {"id": 958, "createdAt": "", "updatedAt": "", "menuId": 64, "key": "成员数量统计", "api": {"id": 29, "uuid": "67dd05d9-1a93-44df-a73d-5ef4e349a082", "createdAt": "2022-12-28 14:14:10", "updatedAt": "2023-04-18 09:58:52", "deletedAt": "", "path": "/user-api/v1/user/corp/depart/list/{offset}/{length}", "description": "成员列表（按部门查询）", "apiGroup": "user/user", "method": "POST", "necessary": 2}}, {"id": 963, "createdAt": "", "updatedAt": "", "menuId": 64, "key": "船舶证书统计", "api": {"id": 114, "uuid": "3e5f0ef5-c8eb-4d1f-9bc0-33d1d9f28f1e", "createdAt": "2023-02-22 11:02:52", "updatedAt": "2023-08-11 17:41:57", "deletedAt": "", "path": "/ship-api/v1/cert/expire/statisic", "description": "船舶证书统计", "apiGroup": "ship/cert", "method": "GET", "necessary": 2}}, {"id": 959, "createdAt": "", "updatedAt": "", "menuId": 64, "key": "调度数量统计", "api": {"id": 126, "uuid": "71e33807-d069-4271-9acb-8ec275a87f21", "createdAt": "2023-02-28 16:36:48", "updatedAt": "2023-07-13 18:15:11", "deletedAt": "", "path": "/ship-api/v1/dispatch/list/{offset}/{length}", "description": "调度列表", "apiGroup": "dispatch/record", "method": "POST", "necessary": 2}}, {"id": 964, "createdAt": "", "updatedAt": "", "menuId": 64, "key": "船舶保险统计", "api": {"id": 113, "uuid": "57def7e1-351c-446c-9ff8-65bf5183186a", "createdAt": "2023-02-22 11:02:37", "updatedAt": "2023-08-11 17:41:57", "deletedAt": "", "path": "/ship-api/v1/insurance/expire/statisic", "description": "船舶保险统计", "apiGroup": "ship/insurance", "method": "GET", "necessary": 2}}, {"id": 960, "createdAt": "", "updatedAt": "", "menuId": 64, "key": "存油量统计", "api": {"id": 219, "uuid": "04e4231b-aa82-42e7-a7d0-e9c3517b0f84", "createdAt": "2023-04-18 09:57:13", "updatedAt": "2023-08-11 17:41:57", "deletedAt": "", "path": "/ship-api/v1/device/oil/static", "description": "存油统计", "apiGroup": "ship/device", "method": "POST", "necessary": 2}}, {"id": 965, "createdAt": "", "updatedAt": "", "menuId": 64, "key": "船员证书统计", "api": {"id": 92, "uuid": "d902d729-d9c6-463a-a0e6-c7702ec2d9d6", "createdAt": "2023-02-20 13:54:10", "updatedAt": "2023-08-11 17:41:57", "deletedAt": "", "path": "/user-api/v1/shipman/cert/statisic", "description": "船员证书统计\n", "apiGroup": "user/shipman_cert", "method": "GET", "necessary": 2}}], "children": null}, {"id": 12, "uuid": "3b600296-ff0e-409f-b7f8-97f18c45cca9", "createdAt": "", "updatedAt": "", "deletedAt": "", "platformId": 1, "endpointId": 1, "parentId": 0, "parentTitle": "", "path": "/ship-mgr/ship-mgr", "name": "ShipMgr", "hidden": 2, "component": "@/views/ship-module/ship-mgr/index.vue", "sortNum": 90, "keepAlive": 2, "title": "船舶管理", "icon": "ship", "closeTab": 2, "openNewPage": 2, "params": null, "btns": null, "children": [{"id": 3, "uuid": "44c82cdf-6554-4732-81f1-6d1c54ad50ff", "createdAt": "", "updatedAt": "", "deletedAt": "", "platformId": 1, "endpointId": 1, "parentId": 12, "parentTitle": "", "path": "/ship-mgr/ship-mgr", "name": "ShipMgr", "hidden": 2, "component": " @/views/ship-module/ship-mgr/index.vue", "sortNum": 100, "keepAlive": 1, "title": "船舶库", "icon": "", "closeTab": 2, "openNewPage": 2, "params": null, "btns": [{"id": 19, "createdAt": "", "updatedAt": "", "menuId": 3, "key": "船舶列表", "api": {"id": 54, "uuid": "1ec74063-728b-43d4-a538-f519d3801bed", "createdAt": "2023-01-06 09:26:26", "updatedAt": "2023-08-10 17:09:00", "deletedAt": "", "path": "/ship-api/v1/ship/list/{offset}/{length}", "description": "船舶列表", "apiGroup": "ship/ship", "method": "POST", "necessary": 1}}, {"id": 24, "createdAt": "", "updatedAt": "", "menuId": 3, "key": "设备信息", "api": {"id": 58, "uuid": "d4a8a4c1-61ca-4034-9443-963bda9030cb", "createdAt": "2023-01-06 17:18:02", "updatedAt": "2023-08-10 17:09:00", "deletedAt": "", "path": "/ship-api/v1/ship/device/list", "description": "船舶设备信息", "apiGroup": "ship/ship", "method": "GET", "necessary": 2}}, {"id": 29, "createdAt": "", "updatedAt": "", "menuId": 3, "key": "油柜更新", "api": {"id": 62, "uuid": "f445724b-c925-4130-897c-ed022ce5fe99", "createdAt": "2023-01-09 10:29:51", "updatedAt": "2023-08-10 17:09:00", "deletedAt": "", "path": "/ship-api/v1/device/oil/update", "description": "油柜更新", "apiGroup": "ship/device", "method": "POST", "necessary": 2}}, {"id": 33, "createdAt": "", "updatedAt": "", "menuId": 3, "key": "发电设备新建", "api": {"id": 65, "uuid": "57b7d272-4b98-456a-9c22-0777647a58f9", "createdAt": "2023-01-09 11:10:46", "updatedAt": "2023-08-10 17:09:00", "deletedAt": "", "path": "/ship-api/v1/device/power/new", "description": "发电设备新建", "apiGroup": "ship/device", "method": "POST", "necessary": 2}}, {"id": 37, "createdAt": "", "updatedAt": "", "menuId": 3, "key": "推进设备新建", "api": {"id": 69, "uuid": "32e0bc79-a6f3-4b8b-a797-868ff26eb084", "createdAt": "2023-01-09 11:11:40", "updatedAt": "2023-08-10 17:09:00", "deletedAt": "", "path": "/ship-api/v1/device/push/new", "description": "主机推进设备新建", "apiGroup": "ship/device", "method": "POST", "necessary": 2}}, {"id": 42, "createdAt": "", "updatedAt": "", "menuId": 3, "key": "保险添加", "api": {"id": 74, "uuid": "1333dadc-bedb-4842-bc1b-5965faccd485", "createdAt": "2023-01-09 14:25:33", "updatedAt": "2023-08-10 17:09:00", "deletedAt": "", "path": "/ship-api/v1/insurance/new", "description": "保险添加", "apiGroup": "ship/insurance", "method": "POST", "necessary": 2}}, {"id": 47, "createdAt": "", "updatedAt": "", "menuId": 3, "key": "证书编辑", "api": {"id": 81, "uuid": "95fec21b-8dd0-4b57-884e-36c11a43c127", "createdAt": "2023-01-09 16:02:30", "updatedAt": "2023-08-11 08:49:30", "deletedAt": "", "path": "/ship-api/v1/cert/update", "description": "证书更新", "apiGroup": "ship/cert", "method": "POST", "necessary": 2}}, {"id": 51, "createdAt": "", "updatedAt": "", "menuId": 3, "key": "维护记录更新", "api": {"id": 85, "uuid": "f6087729-7de6-41f0-8d8e-8650340a3d6a", "createdAt": "2023-01-09 17:11:13", "updatedAt": "2023-08-10 17:09:00", "deletedAt": "", "path": "/ship-api/v1/repair/update", "description": "维护记录更新", "apiGroup": "ship/repair", "method": "POST", "necessary": 2}}, {"id": 48, "createdAt": "", "updatedAt": "", "menuId": 3, "key": "证书添加", "api": {"id": 80, "uuid": "8b0c7c15-fe4b-43db-b076-920cc0baebbe", "createdAt": "2023-01-09 16:02:18", "updatedAt": "2023-08-11 08:49:30", "deletedAt": "", "path": "/ship-api/v1/cert/new", "description": "证书添加", "apiGroup": "ship/cert", "method": "POST", "necessary": 2}}, {"id": 43, "createdAt": "", "updatedAt": "", "menuId": 3, "key": "保险编辑", "api": {"id": 75, "uuid": "29af9726-9b44-458c-8a2f-05e3cbd13a28", "createdAt": "2023-01-09 14:25:48", "updatedAt": "2023-08-10 17:09:00", "deletedAt": "", "path": "/ship-api/v1/insurance/update", "description": "保险更新", "apiGroup": "ship/insurance", "method": "POST", "necessary": 2}}, {"id": 38, "createdAt": "", "updatedAt": "", "menuId": 3, "key": "推进设备更新", "api": {"id": 70, "uuid": "02daa441-beb8-47f4-91ad-2396813bebca", "createdAt": "2023-01-09 11:11:53", "updatedAt": "2023-08-10 17:09:00", "deletedAt": "", "path": "/ship-api/v1/device/push/update", "description": "主机推进设备更新", "apiGroup": "ship/device", "method": "POST", "necessary": 2}}, {"id": 34, "createdAt": "", "updatedAt": "", "menuId": 3, "key": "发电设备更新", "api": {"id": 66, "uuid": "eeda858d-fb46-40eb-8798-904fb8ae0818", "createdAt": "2023-01-09 11:10:59", "updatedAt": "2023-08-10 17:09:00", "deletedAt": "", "path": "/ship-api/v1/device/power/update", "description": "发电设备更新", "apiGroup": "ship/device", "method": "POST", "necessary": 2}}, {"id": 30, "createdAt": "", "updatedAt": "", "menuId": 3, "key": "油柜新建", "api": {"id": 61, "uuid": "5fdfcf0f-ffff-43ee-99b3-7e461c3f7316", "createdAt": "2023-01-09 10:29:34", "updatedAt": "2023-08-10 17:09:00", "deletedAt": "", "path": "/ship-api/v1/device/oil/new", "description": "油柜新建\n", "apiGroup": "ship/device", "method": "POST", "necessary": 2}}, {"id": 25, "createdAt": "", "updatedAt": "", "menuId": 3, "key": "证书保险", "api": {"id": 16, "uuid": "8187fd84-f169-4b9f-b418-414b9e645709", "createdAt": "2022-12-28 14:03:24", "updatedAt": "2023-08-11 17:41:57", "deletedAt": "", "path": "/", "description": "权限控制用，无对应接口", "apiGroup": "", "method": "POST", "necessary": 2}}, {"id": 20, "createdAt": "", "updatedAt": "", "menuId": 3, "key": "添加船舶", "api": {"id": 21, "uuid": "04f0958f-1d73-4e25-891c-f04660d4972b", "createdAt": "2022-12-28 14:07:57", "updatedAt": "2023-08-10 17:09:00", "deletedAt": "", "path": "/ship-api/v1/ship/new", "description": "船舶新建", "apiGroup": "ship/ship", "method": "POST", "necessary": 2}}, {"id": 21, "createdAt": "", "updatedAt": "", "menuId": 3, "key": "船舶信息更新", "api": {"id": 57, "uuid": "44093c73-770b-4c0b-9f97-62c66fbabfa0", "createdAt": "2023-01-06 14:28:21", "updatedAt": "2023-08-10 17:09:00", "deletedAt": "", "path": "/ship-api/v1/ship/update", "description": "船舶信息更新", "apiGroup": "ship/ship", "method": "POST", "necessary": 2}}, {"id": 26, "createdAt": "", "updatedAt": "", "menuId": 3, "key": "维护记录", "api": {"id": 83, "uuid": "f3718dbf-a1bf-460e-91b2-3343cdb40f37", "createdAt": "2023-01-09 17:10:49", "updatedAt": "2023-08-10 17:09:00", "deletedAt": "", "path": "/ship-api/v1/repair/list/{offset}/{length}", "description": "维护记录列表", "apiGroup": "ship/repair", "method": "POST", "necessary": 2}}, {"id": 31, "createdAt": "", "updatedAt": "", "menuId": 3, "key": "发电设备删除", "api": {"id": 63, "uuid": "735f87f0-cd57-441a-9240-670bf30158e4", "createdAt": "2023-01-09 11:10:22", "updatedAt": "2023-08-10 17:09:00", "deletedAt": "", "path": "/ship-api/v1/device/power/del", "description": "发电设备删除", "apiGroup": "ship/device", "method": "GET", "necessary": 2}}, {"id": 35, "createdAt": "", "updatedAt": "", "menuId": 3, "key": "推进设备删除", "api": {"id": 67, "uuid": "2f97b7a0-0c10-46a5-99df-81a1ac4fc566", "createdAt": "2023-01-09 11:11:11", "updatedAt": "2023-08-10 17:09:00", "deletedAt": "", "path": "/ship-api/v1/device/push/del", "description": "主机推进设备删除", "apiGroup": "ship/device", "method": "GET", "necessary": 2}}, {"id": 44, "createdAt": "", "updatedAt": "", "menuId": 3, "key": "证书列表", "api": {"id": 76, "uuid": "fdd8798c-7d9b-4175-884a-52f8f49e4c7f", "createdAt": "2023-01-09 15:59:50", "updatedAt": "2023-08-11 08:49:30", "deletedAt": "", "path": "/ship-api/v1/cert/list/{offset}/{length}", "description": "证书列表", "apiGroup": "ship/cert", "method": "POST", "necessary": 2}}, {"id": 49, "createdAt": "", "updatedAt": "", "menuId": 3, "key": "维护记录详情", "api": {"id": 82, "uuid": "5b893387-17ab-42cd-b0d5-5dc0bc0240fd", "createdAt": "2023-01-09 17:10:38", "updatedAt": "2023-08-10 17:09:00", "deletedAt": "", "path": "/ship-api/v1/repair/detail", "description": "维护记录详情", "apiGroup": "ship/repair", "method": "GET", "necessary": 2}}, {"id": 39, "createdAt": "", "updatedAt": "", "menuId": 3, "key": "保险删除", "api": {"id": 71, "uuid": "3eb18e2f-19bc-4f70-ac34-679c0a9826fb", "createdAt": "2023-01-09 14:24:51", "updatedAt": "2023-08-10 17:09:00", "deletedAt": "", "path": "/ship-api/v1/insurance/del", "description": "保险删除", "apiGroup": "ship/insurance", "method": "GET", "necessary": 2}}, {"id": 45, "createdAt": "", "updatedAt": "", "menuId": 3, "key": "证书删除", "api": {"id": 77, "uuid": "839afd11-35c9-403e-b142-50afe2e38c73", "createdAt": "2023-01-09 16:00:05", "updatedAt": "2023-08-11 08:49:30", "deletedAt": "", "path": "/ship-api/v1/cert/del", "description": "证书删除", "apiGroup": "ship/cert", "method": "GET", "necessary": 2}}, {"id": 50, "createdAt": "", "updatedAt": "", "menuId": 3, "key": "维护记录新建", "api": {"id": 84, "uuid": "dedca99c-c648-4e15-9354-1f07f4baf9e8", "createdAt": "2023-01-09 17:11:02", "updatedAt": "2023-08-10 17:09:00", "deletedAt": "", "path": "/ship-api/v1/repair/new", "description": "维护记录新建", "apiGroup": "ship/repair", "method": "POST", "necessary": 2}}, {"id": 46, "createdAt": "", "updatedAt": "", "menuId": 3, "key": "证书详情", "api": {"id": 78, "uuid": "*************-43c9-a2d1-************", "createdAt": "2023-01-09 16:00:17", "updatedAt": "2023-08-11 08:49:30", "deletedAt": "", "path": "/ship-api/v1/cert/detail", "description": "证书详情", "apiGroup": "ship/cert", "method": "GET", "necessary": 2}}, {"id": 40, "createdAt": "", "updatedAt": "", "menuId": 3, "key": "保险详情", "api": {"id": 72, "uuid": "58473dbf-6873-4c59-8260-6f527a160f47", "createdAt": "2023-01-09 14:25:03", "updatedAt": "2023-08-10 17:09:00", "deletedAt": "", "path": "/ship-api/v1/insurance/detail", "description": "保险详情", "apiGroup": "ship/insurance", "method": "GET", "necessary": 2}}, {"id": 41, "createdAt": "", "updatedAt": "", "menuId": 3, "key": "保险列表", "api": {"id": 73, "uuid": "f098e656-5577-4ca9-b7d4-c7634a2165c9", "createdAt": "2023-01-09 14:25:18", "updatedAt": "2023-08-10 17:09:00", "deletedAt": "", "path": "/ship-api/v1/insurance/list/{offset}/{length}", "description": "保险列表", "apiGroup": "ship/insurance", "method": "POST", "necessary": 2}}, {"id": 36, "createdAt": "", "updatedAt": "", "menuId": 3, "key": "推进设备详情", "api": {"id": 68, "uuid": "4a3d0096-0c3c-4d03-ba1a-ef03d5d39742", "createdAt": "2023-01-09 11:11:24", "updatedAt": "2023-08-10 17:09:00", "deletedAt": "", "path": "/ship-api/v1/device/push/detail", "description": "主机推进设备详情", "apiGroup": "ship/device", "method": "GET", "necessary": 2}}, {"id": 32, "createdAt": "", "updatedAt": "", "menuId": 3, "key": "发电设备详情", "api": {"id": 64, "uuid": "0e1d25ae-afe4-4a38-aaf0-eef1719a0a87", "createdAt": "2023-01-09 11:10:34", "updatedAt": "2023-08-10 17:09:00", "deletedAt": "", "path": "/ship-api/v1/device/power/detail", "description": "发电设备详情", "apiGroup": "ship/device", "method": "GET", "necessary": 2}}, {"id": 27, "createdAt": "", "updatedAt": "", "menuId": 3, "key": "油柜删除", "api": {"id": 59, "uuid": "bb510fb3-48a1-4af5-9bfc-ce0c13878bcf", "createdAt": "2023-01-09 10:29:05", "updatedAt": "2023-08-10 17:09:00", "deletedAt": "", "path": "/ship-api/v1/device/oil/del", "description": "油柜删除", "apiGroup": "ship/device", "method": "GET", "necessary": 2}}, {"id": 28, "createdAt": "", "updatedAt": "", "menuId": 3, "key": "油柜详情", "api": {"id": 60, "uuid": "ec15053c-b6b1-44a4-99b4-2fa6d3b6a37c", "createdAt": "2023-01-09 10:29:22", "updatedAt": "2023-08-10 17:09:00", "deletedAt": "", "path": "/ship-api/v1/device/oil/detail", "description": "油柜详情", "apiGroup": "ship/device", "method": "GET", "necessary": 2}}, {"id": 22, "createdAt": "", "updatedAt": "", "menuId": 3, "key": "船舶详情", "api": {"id": 55, "uuid": "faffaf56-5e70-4edc-865c-c9cdd3ae2916", "createdAt": "2023-01-06 11:02:55", "updatedAt": "2023-08-10 17:15:22", "deletedAt": "", "path": "/ship-api/v1/ship/brief", "description": "船舶简要信息", "apiGroup": "ship/ship", "method": "GET", "necessary": 2}}, {"id": 53, "createdAt": "", "updatedAt": "", "menuId": 3, "key": "在船船员", "api": {"id": 96, "uuid": "2fab46d1-0f2c-481f-8ba6-524ee72680a1", "createdAt": "2023-02-20 13:58:52", "updatedAt": "2023-08-10 17:09:00", "deletedAt": "", "path": "/user-api/v1/shipman/on/list", "description": "在船船员", "apiGroup": "user/shipman", "method": "POST", "necessary": 2}}, {"id": 90, "createdAt": "", "updatedAt": "", "menuId": 3, "key": "船员库", "api": {"id": 94, "uuid": "4cf8dc82-58dd-40fe-a38e-42d54a7d3761", "createdAt": "2023-02-20 13:58:23", "updatedAt": "2023-08-10 17:09:00", "deletedAt": "", "path": "/user-api/v1/shipman/list/{offset}/{length}", "description": "船员列表（船员库）", "apiGroup": "user/shipman", "method": "POST", "necessary": 2}}, {"id": 56, "createdAt": "", "updatedAt": "", "menuId": 3, "key": "下船", "api": {"id": 107, "uuid": "2a07a759-2690-4e09-ba58-b4171730df0f", "createdAt": "2023-02-20 14:39:34", "updatedAt": "2023-08-10 17:09:00", "deletedAt": "", "path": "/ship-api/v1/shipman/off/ship", "description": "船员下船", "apiGroup": "ship/shipman", "method": "POST", "necessary": 2}}, {"id": 55, "createdAt": "", "updatedAt": "", "menuId": 3, "key": "上船", "api": {"id": 108, "uuid": "cf141657-4a78-4e30-b9ff-0400c7c3b647", "createdAt": "2023-02-20 14:39:52", "updatedAt": "2023-08-10 17:09:00", "deletedAt": "", "path": "/ship-api/v1/shipman/on/ship", "description": "船员上船", "apiGroup": "ship/shipman", "method": "POST", "necessary": 2}}, {"id": 54, "createdAt": "", "updatedAt": "", "menuId": 3, "key": "在船船员统计", "api": {"id": 95, "uuid": "77ebc687-e3d1-4cd7-b41e-62213f864da4", "createdAt": "2023-02-20 13:58:40", "updatedAt": "2023-08-10 17:09:00", "deletedAt": "", "path": "/user-api/v1/shipman/on/statisic", "description": "在船船员相关统计", "apiGroup": "user/shipman", "method": "GET", "necessary": 2}}, {"id": 218, "createdAt": "", "updatedAt": "", "menuId": 3, "key": "船员档案", "api": {"id": 16, "uuid": "8187fd84-f169-4b9f-b418-414b9e645709", "createdAt": "2022-12-28 14:03:24", "updatedAt": "2023-08-11 17:41:57", "deletedAt": "", "path": "/", "description": "权限控制用，无对应接口", "apiGroup": "", "method": "POST", "necessary": 2}}, {"id": 257, "createdAt": "", "updatedAt": "", "menuId": 3, "key": "保险统计", "api": {"id": 113, "uuid": "57def7e1-351c-446c-9ff8-65bf5183186a", "createdAt": "2023-02-22 11:02:37", "updatedAt": "2023-08-11 17:41:57", "deletedAt": "", "path": "/ship-api/v1/insurance/expire/statisic", "description": "船舶保险统计", "apiGroup": "ship/insurance", "method": "GET", "necessary": 2}}, {"id": 258, "createdAt": "", "updatedAt": "", "menuId": 3, "key": "证书统计", "api": {"id": 114, "uuid": "3e5f0ef5-c8eb-4d1f-9bc0-33d1d9f28f1e", "createdAt": "2023-02-22 11:02:52", "updatedAt": "2023-08-11 17:41:57", "deletedAt": "", "path": "/ship-api/v1/cert/expire/statisic", "description": "船舶证书统计", "apiGroup": "ship/cert", "method": "GET", "necessary": 2}}, {"id": 330, "createdAt": "", "updatedAt": "", "menuId": 3, "key": "备件记录更新", "api": {"id": 118, "uuid": "c6725477-029e-492a-9373-1c906e37439e", "createdAt": "2023-02-23 17:28:08", "updatedAt": "2023-08-10 17:09:00", "deletedAt": "", "path": "/ship-api/v1/material/update", "description": "备件记录更新", "apiGroup": "ship/material", "method": "POST", "necessary": 2}}, {"id": 329, "createdAt": "", "updatedAt": "", "menuId": 3, "key": "备件记录新建", "api": {"id": 117, "uuid": "74fa5cfd-b42e-4b75-8905-32e1913549e2", "createdAt": "2023-02-23 17:27:01", "updatedAt": "2023-08-10 17:09:00", "deletedAt": "", "path": "/ship-api/v1/material/new", "description": "备件记录新建", "apiGroup": "ship/material", "method": "POST", "necessary": 2}}, {"id": 328, "createdAt": "", "updatedAt": "", "menuId": 3, "key": "备件详情", "api": {"id": 115, "uuid": "84aec1b0-5666-40d8-b6ce-f3f7ab308a63", "createdAt": "2023-02-23 17:23:32", "updatedAt": "2023-08-10 17:09:00", "deletedAt": "", "path": "/ship-api/v1/material/detail", "description": "备件记录详情", "apiGroup": "ship/material", "method": "GET", "necessary": 2}}, {"id": 327, "createdAt": "", "updatedAt": "", "menuId": 3, "key": "备件记录", "api": {"id": 116, "uuid": "902783e6-28d7-4ed0-89f2-6b568b49aa04", "createdAt": "2023-02-23 17:23:47", "updatedAt": "2023-08-10 17:09:00", "deletedAt": "", "path": "/ship-api/v1/material/list/{offset}/{length}", "description": "备件记录列表", "apiGroup": "ship/material", "method": "POST", "necessary": 2}}, {"id": 433, "createdAt": "", "updatedAt": "", "menuId": 3, "key": "调度信息", "api": {"id": 16, "uuid": "8187fd84-f169-4b9f-b418-414b9e645709", "createdAt": "2022-12-28 14:03:24", "updatedAt": "2023-08-11 17:41:57", "deletedAt": "", "path": "/", "description": "权限控制用，无对应接口", "apiGroup": "", "method": "POST", "necessary": 2}}, {"id": 527, "createdAt": "", "updatedAt": "", "menuId": 3, "key": "获取 ais token", "api": {"id": 136, "uuid": "aa2e8331-fb9b-4d20-8af9-63e2a8e42cae", "createdAt": "2023-02-28 16:44:09", "updatedAt": "2023-08-10 17:09:00", "deletedAt": "", "path": "/ship-api/v1/ais/token/get", "description": "获取AIS TOKEN", "apiGroup": "ais", "method": "GET", "necessary": 2}}, {"id": 479, "createdAt": "", "updatedAt": "", "menuId": 3, "key": "船舶调度当前任务信息", "api": {"id": 128, "uuid": "3a35f0cb-55ca-475a-85c4-7ebd336a3339", "createdAt": "2023-02-28 16:39:36", "updatedAt": "2023-08-10 17:09:00", "deletedAt": "", "path": "/ship-api/v1/dispatch/ship/detail", "description": "船舶调度当前任务信息", "apiGroup": "ship/dispatch", "method": "POST", "necessary": 2}}, {"id": 480, "createdAt": "", "updatedAt": "", "menuId": 3, "key": "船舶调度预计任务列表", "api": {"id": 129, "uuid": "5c7f6aa0-aa09-4cb4-b16a-5f2d35bd2b28", "createdAt": "2023-02-28 16:39:59", "updatedAt": "2023-08-10 17:09:00", "deletedAt": "", "path": "/ship-api/v1/dispatch/ship/plan/list", "description": "船舶调度预计任务列表", "apiGroup": "ship/dispatch", "method": "POST", "necessary": 2}}, {"id": 23, "createdAt": "", "updatedAt": "", "menuId": 3, "key": "船舶信息", "api": {"id": 56, "uuid": "30bba3f5-6356-49bc-9a21-2b0c2030933a", "createdAt": "2023-01-06 11:03:10", "updatedAt": "2023-08-10 17:09:00", "deletedAt": "", "path": "/ship-api/v1/ship/detail", "description": "船舶基本信息", "apiGroup": "ship/ship", "method": "GET", "necessary": 2}}, {"id": 1114, "createdAt": "", "updatedAt": "", "menuId": 3, "key": "海事预警", "api": {"id": 213, "uuid": "b6d1d7cd-14e3-4fca-8ee7-a72bc2b110e0", "createdAt": "2023-04-18 09:32:02", "updatedAt": "2023-08-10 17:09:00", "deletedAt": "", "path": "/ship-api/v1/msa/announce/part/list", "description": "海事局列表", "apiGroup": "msa/announce", "method": "POST", "necessary": 2}}, {"id": 1186, "createdAt": "", "updatedAt": "", "menuId": 3, "key": "电子证书保存", "api": {"id": 245, "uuid": "d8ba50f6-d750-48c9-9473-0477134bc66e", "createdAt": "2023-05-15 16:38:16", "updatedAt": "2023-08-10 17:09:00", "deletedAt": "", "path": "/ship-api/v1/digital/cert/update", "description": "电子证书保存", "apiGroup": "ship/digital_cert", "method": "POST", "necessary": 2}}, {"id": 1187, "createdAt": "", "updatedAt": "", "menuId": 3, "key": "电子证书删除", "api": {"id": 246, "uuid": "f32fc11f-95fd-4319-bcee-672448e207d7", "createdAt": "2023-05-15 16:38:46", "updatedAt": "2023-08-10 17:09:00", "deletedAt": "", "path": "/ship-api/v1/digital/cert/del", "description": "电子证书删除", "apiGroup": "ship/digital_cert", "method": "POST", "necessary": 2}}, {"id": 1188, "createdAt": "", "updatedAt": "", "menuId": 3, "key": "电子证书详情", "api": {"id": 244, "uuid": "42987122-0c5a-4f9d-aacd-e4f76874c51f", "createdAt": "2023-05-15 16:37:49", "updatedAt": "2023-08-10 17:09:00", "deletedAt": "", "path": "/ship-api/v1/digital/cert/detail", "description": "电子证书详情", "apiGroup": " ship/digital_cert", "method": "GET", "necessary": 2}}, {"id": 1189, "createdAt": "", "updatedAt": "", "menuId": 3, "key": "电子证书生成", "api": {"id": 243, "uuid": "336faa3b-5d5f-4f9a-924e-75e25a6fbc41", "createdAt": "2023-05-15 16:37:06", "updatedAt": "2023-08-10 17:09:00", "deletedAt": "", "path": "/ship-api/v1/digital/cert/new", "description": "生成电子证书", "apiGroup": "ship/digital_cert", "method": "POST", "necessary": 2}}, {"id": 1247, "createdAt": "", "updatedAt": "", "menuId": 3, "key": "变更记录新增", "api": {"id": 238, "uuid": "2109765d-4e83-4db7-9673-e1e09f090b68", "createdAt": "2023-05-15 16:21:14", "updatedAt": "2023-08-10 17:09:00", "deletedAt": "", "path": "/ship-api/v1/ship/update/record/new", "description": "新增船舶变更记录", "apiGroup": "ship/ship", "method": "POST", "necessary": 2}}, {"id": 1248, "createdAt": "", "updatedAt": "", "menuId": 3, "key": "变更记录列表", "api": {"id": 239, "uuid": "b6e60c04-0428-4059-8508-7f47e1a443c7", "createdAt": "2023-05-15 16:22:44", "updatedAt": "2023-08-10 17:09:00", "deletedAt": "", "path": "/ship-api/v1/ship/update/record/list/{offset}/{length}", "description": "船舶变动记录列表", "apiGroup": "ship/ship", "method": "POST", "necessary": 2}}, {"id": 1303, "createdAt": "", "updatedAt": "", "menuId": 3, "key": "船舶位置", "api": {"id": 237, "uuid": "3d017a98-4a73-4ed6-95bc-c7cd3aaa84b6", "createdAt": "2023-05-15 16:19:14", "updatedAt": "2023-08-10 17:09:00", "deletedAt": "", "path": "/ship-api/v1/sea/ship/location", "description": "船舶位置", "apiGroup": "ship/seamap", "method": "POST", "necessary": 2}}, {"id": 1542, "createdAt": "", "updatedAt": "", "menuId": 3, "key": "变更记录", "api": {"id": 16, "uuid": "8187fd84-f169-4b9f-b418-414b9e645709", "createdAt": "2022-12-28 14:03:24", "updatedAt": "2023-08-11 17:41:57", "deletedAt": "", "path": "/", "description": "权限控制用，无对应接口", "apiGroup": "", "method": "POST", "necessary": 2}}, {"id": 1484, "createdAt": "", "updatedAt": "", "menuId": 3, "key": "电子证书", "api": {"id": 16, "uuid": "8187fd84-f169-4b9f-b418-414b9e645709", "createdAt": "2022-12-28 14:03:24", "updatedAt": "2023-08-11 17:41:57", "deletedAt": "", "path": "/", "description": "权限控制用，无对应接口", "apiGroup": "", "method": "POST", "necessary": 2}}, {"id": 1859, "createdAt": "", "updatedAt": "", "menuId": 3, "key": "保险续期", "api": {"id": 271, "uuid": "c034bf83-7834-4946-82c4-61d19acc0c63", "createdAt": "2023-06-01 09:30:05", "updatedAt": "2023-08-10 17:09:00", "deletedAt": "", "path": "/ship-api/v1/insurance/renewal", "description": "船舶保险续期", "apiGroup": "ship/insurance", "method": "POST", "necessary": 2}}, {"id": 1860, "createdAt": "", "updatedAt": "", "menuId": 3, "key": "保险续期详情", "api": {"id": 270, "uuid": "81acecf5-af46-4ef6-baea-e2c53c311dce", "createdAt": "2023-06-01 09:29:00", "updatedAt": "2023-08-10 17:09:00", "deletedAt": "", "path": "/user-api/v1/shipman/insurance/renewal/detail", "description": "船员保险续期详情", "apiGroup": "user/shipman_insurance", "method": "GET", "necessary": 2}}, {"id": 1861, "createdAt": "", "updatedAt": "", "menuId": 3, "key": "证书续期", "api": {"id": 279, "uuid": "ee080baf-cfdb-4662-8c9d-dc170d9549c7", "createdAt": "2023-06-01 09:48:33", "updatedAt": "2023-08-11 08:49:30", "deletedAt": "", "path": "/ship-api/v1/cert/renewal", "description": "船舶证书续期\n", "apiGroup": "ship/cert", "method": "POST", "necessary": 2}}, {"id": 1862, "createdAt": "", "updatedAt": "", "menuId": 3, "key": "证书续期详情", "api": {"id": 280, "uuid": "ee915cd4-b475-44ab-ba33-3e7ae8772a62", "createdAt": "2023-06-01 09:48:59", "updatedAt": "2023-08-11 08:49:30", "deletedAt": "", "path": "/ship-api/v1/cert/renewal/detail", "description": "船舶证书续期详情", "apiGroup": "ship/cert", "method": "GET", "necessary": 2}}, {"id": 1922, "createdAt": "", "updatedAt": "", "menuId": 3, "key": "检验登记", "api": {"id": 273, "uuid": "197371c4-8da5-4bc8-8653-3d1da3ac9ca6", "createdAt": "2023-06-01 09:31:37", "updatedAt": "2023-08-11 08:49:30", "deletedAt": "", "path": "/ship-api/v1/cert/check", "description": "证书检验登记", "apiGroup": "ship/cert", "method": "POST", "necessary": 2}}, {"id": 1923, "createdAt": "", "updatedAt": "", "menuId": 3, "key": "检验登记详情", "api": {"id": 274, "uuid": "e65f8b35-175e-4b4f-b87e-8a0bfcc1c1a1", "createdAt": "2023-06-01 09:43:20", "updatedAt": "2023-08-11 08:49:30", "deletedAt": "", "path": "/ship-api/v1/cert/check/detail", "description": "证书检验登记详情\n", "apiGroup": "ship/cert", "method": "GET", "necessary": 2}}, {"id": 2300, "createdAt": "", "updatedAt": "", "menuId": 3, "key": "附件资料", "api": {"id": 306, "uuid": "302f5d82-c3a7-4c26-ac4b-6116070e1ec6", "createdAt": "2023-07-13 15:12:05", "updatedAt": "2023-08-10 17:09:00", "deletedAt": "", "path": "/ship-api/v1/attach/list/{offset}/{length}", "description": "船舶附件列表\n", "apiGroup": "ship/attach", "method": "POST", "necessary": 2}}, {"id": 2301, "createdAt": "", "updatedAt": "", "menuId": 3, "key": "管理附件", "api": {"id": 307, "uuid": "efed39bb-57fe-492c-8d75-34d545bdb376", "createdAt": "2023-07-13 15:12:42", "updatedAt": "2023-08-10 17:09:00", "deletedAt": "", "path": "/ship-api/v1/attach/update", "description": "船舶附件更新\n", "apiGroup": "ship/attach", "method": "POST", "necessary": 2}}, {"id": 2388, "createdAt": "", "updatedAt": "", "menuId": 3, "key": "证书配置详情", "api": {"id": 308, "uuid": "cfe680fa-bacc-4f4a-9339-3a5440993530", "createdAt": "2023-07-13 15:13:26", "updatedAt": "2023-08-10 17:09:00", "deletedAt": "", "path": "/ship-api/v1/digital/cert/options/detail", "description": "证书配置详情\n", "apiGroup": "ship/digital_cert", "method": "GET", "necessary": 2}}, {"id": 2562, "createdAt": "", "updatedAt": "", "menuId": 3, "key": "关注异动", "api": {"id": 311, "uuid": "13e7df8f-9d11-4701-9e6d-8570ca1cdd03", "createdAt": "2023-08-10 16:46:18", "updatedAt": "2023-08-10 17:15:22", "deletedAt": "", "path": "/ship-api/v1/location/subs/new", "description": "位置订阅新建", "apiGroup": "ship/seamap", "method": "POST", "necessary": 2}}, {"id": 2563, "createdAt": "", "updatedAt": "", "menuId": 3, "key": "取关异动", "api": {"id": 309, "uuid": "7a527569-c407-4c31-90f6-312ec898a475", "createdAt": "2023-08-10 16:44:24", "updatedAt": "2023-08-10 17:15:22", "deletedAt": "", "path": "/ship-api/v1/location/subs/del", "description": "位置订阅删除", "apiGroup": "ship/seamap", "method": "GET", "necessary": 2}}, {"id": 2564, "createdAt": "", "updatedAt": "", "menuId": 3, "key": "更新位置", "api": {"id": 312, "uuid": "d64ca0d0-99ee-4f08-a466-c197b9c9c562", "createdAt": "2023-08-10 16:47:30", "updatedAt": "2023-08-10 17:15:22", "deletedAt": "", "path": "/ship-api/v1/location/subs/update", "description": "位置订阅更新", "apiGroup": "ship/seamap", "method": "POST", "necessary": 2}}, {"id": 2565, "createdAt": "", "updatedAt": "", "menuId": 3, "key": "位置订阅详情", "api": {"id": 310, "uuid": "8df274f0-d63b-4b15-b51e-79871ed54950", "createdAt": "2023-08-10 16:45:40", "updatedAt": "2023-08-10 17:15:22", "deletedAt": "", "path": "/ship-api/v1/location/subs/detail", "description": "位置订阅详情", "apiGroup": "ship/seamap", "method": "GET", "necessary": 2}}], "children": [{"id": 4, "uuid": "6f1c7c5d-8be5-4c0e-a0f9-571d973dd5ec", "createdAt": "", "updatedAt": "", "deletedAt": "", "platformId": 1, "endpointId": 1, "parentId": 3, "parentTitle": "", "path": "/ship-mgr/edit-ship", "name": "EditShip", "hidden": 1, "component": "@/views/ship-module/edit-ship/index.vue", "sortNum": 0, "keepAlive": 2, "title": "添加船舶", "icon": "", "closeTab": 1, "openNewPage": 2, "params": null, "btns": [{"id": 52, "createdAt": "", "updatedAt": "", "menuId": 4, "key": " 添加船舶", "api": {"id": 21, "uuid": "04f0958f-1d73-4e25-891c-f04660d4972b", "createdAt": "2022-12-28 14:07:57", "updatedAt": "2023-08-10 17:09:00", "deletedAt": "", "path": "/ship-api/v1/ship/new", "description": "船舶新建", "apiGroup": "ship/ship", "method": "POST", "necessary": 2}}, {"id": 791, "createdAt": "", "updatedAt": "", "menuId": 4, "key": "设备状态", "api": {"id": 165, "uuid": "43dbbc46-6549-4a70-9dc6-e34aac830070", "createdAt": "2023-03-22 13:42:32", "updatedAt": "2023-03-23 11:18:37", "deletedAt": "", "path": "/ship-api/v1/daily/device/state", "description": "汇报设置设备选项状态", "apiGroup": "ship/report", "method": "GET", "necessary": 2}}], "children": null}]}, {"id": 13, "uuid": "4cc4221e-b6f5-415f-9c25-2d19b0bde18c", "createdAt": "", "updatedAt": "", "deletedAt": "", "platformId": 1, "endpointId": 1, "parentId": 12, "parentTitle": "", "path": "/ship-mgr/ship-cert", "name": "ShipCert", "hidden": 2, "component": "@/views/ship-module/ship-cert/index.vue", "sortNum": 10, "keepAlive": 2, "title": "船舶证书", "icon": "", "closeTab": 2, "openNewPage": 2, "params": null, "btns": [{"id": 298, "createdAt": "", "updatedAt": "", "menuId": 13, "key": "证书列表", "api": {"id": 76, "uuid": "fdd8798c-7d9b-4175-884a-52f8f49e4c7f", "createdAt": "2023-01-09 15:59:50", "updatedAt": "2023-08-11 08:49:30", "deletedAt": "", "path": "/ship-api/v1/cert/list/{offset}/{length}", "description": "证书列表", "apiGroup": "ship/cert", "method": "POST", "necessary": 2}}, {"id": 302, "createdAt": "", "updatedAt": "", "menuId": 13, "key": "证书删除", "api": {"id": 77, "uuid": "839afd11-35c9-403e-b142-50afe2e38c73", "createdAt": "2023-01-09 16:00:05", "updatedAt": "2023-08-11 08:49:30", "deletedAt": "", "path": "/ship-api/v1/cert/del", "description": "证书删除", "apiGroup": "ship/cert", "method": "GET", "necessary": 2}}, {"id": 300, "createdAt": "", "updatedAt": "", "menuId": 13, "key": "证书编辑", "api": {"id": 81, "uuid": "95fec21b-8dd0-4b57-884e-36c11a43c127", "createdAt": "2023-01-09 16:02:30", "updatedAt": "2023-08-11 08:49:30", "deletedAt": "", "path": "/ship-api/v1/cert/update", "description": "证书更新", "apiGroup": "ship/cert", "method": "POST", "necessary": 2}}, {"id": 301, "createdAt": "", "updatedAt": "", "menuId": 13, "key": "证书详情", "api": {"id": 78, "uuid": "*************-43c9-a2d1-************", "createdAt": "2023-01-09 16:00:17", "updatedAt": "2023-08-11 08:49:30", "deletedAt": "", "path": "/ship-api/v1/cert/detail", "description": "证书详情", "apiGroup": "ship/cert", "method": "GET", "necessary": 2}}, {"id": 315, "createdAt": "", "updatedAt": "", "menuId": 13, "key": "证书统计", "api": {"id": 114, "uuid": "3e5f0ef5-c8eb-4d1f-9bc0-33d1d9f28f1e", "createdAt": "2023-02-22 11:02:52", "updatedAt": "2023-08-11 17:41:57", "deletedAt": "", "path": "/ship-api/v1/cert/expire/statisic", "description": "船舶证书统计", "apiGroup": "ship/cert", "method": "GET", "necessary": 2}}, {"id": 1751, "createdAt": "", "updatedAt": "", "menuId": 13, "key": "数据校验", "api": {"id": 262, "uuid": "f7a8007f-5088-4c0c-bdc1-a6e31f571bf2", "createdAt": "2023-06-01 09:21:02", "updatedAt": "2023-08-11 17:28:23", "deletedAt": "", "path": "/power-api/v1/imports/validate", "description": "校验数据\n", "apiGroup": "power/import", "method": "POST", "necessary": 2}}, {"id": 1752, "createdAt": "", "updatedAt": "", "menuId": 13, "key": "校验列表", "api": {"id": 263, "uuid": "8ac79724-131e-42e4-8ead-0fdf79c516b9", "createdAt": "2023-06-01 09:21:35", "updatedAt": "2023-08-11 17:28:23", "deletedAt": "", "path": "/power-api/v1/imports/validate/list/{offset}/{length}", "description": "校验列表", "apiGroup": "power/import", "method": "POST", "necessary": 2}}, {"id": 1753, "createdAt": "", "updatedAt": "", "menuId": 13, "key": "开始导入", "api": {"id": 264, "uuid": "bec6fb51-6383-4ad1-a655-81de80de272e", "createdAt": "2023-06-01 09:22:04", "updatedAt": "2023-08-11 17:28:23", "deletedAt": "", "path": "/power-api/v1/imports/exec", "description": "开始导入", "apiGroup": "power/import", "method": "POST", "necessary": 2}}, {"id": 1754, "createdAt": "", "updatedAt": "", "menuId": 13, "key": "导入确认完成", "api": {"id": 265, "uuid": "b2d289bf-4e6f-488d-86c5-8415d508d616", "createdAt": "2023-06-01 09:23:16", "updatedAt": "2023-08-11 17:28:23", "deletedAt": "", "path": "/power-api/v1/imports/complete", "description": "导入确认完成", "apiGroup": "power/import", "method": "POST", "necessary": 2}}, {"id": 1717, "createdAt": "", "updatedAt": "", "menuId": 13, "key": "证书续期", "api": {"id": 279, "uuid": "ee080baf-cfdb-4662-8c9d-dc170d9549c7", "createdAt": "2023-06-01 09:48:33", "updatedAt": "2023-08-11 08:49:30", "deletedAt": "", "path": "/ship-api/v1/cert/renewal", "description": "船舶证书续期\n", "apiGroup": "ship/cert", "method": "POST", "necessary": 2}}, {"id": 1716, "createdAt": "", "updatedAt": "", "menuId": 13, "key": "检验登记", "api": {"id": 273, "uuid": "197371c4-8da5-4bc8-8653-3d1da3ac9ca6", "createdAt": "2023-06-01 09:31:37", "updatedAt": "2023-08-11 08:49:30", "deletedAt": "", "path": "/ship-api/v1/cert/check", "description": "证书检验登记", "apiGroup": "ship/cert", "method": "POST", "necessary": 2}}, {"id": 1718, "createdAt": "", "updatedAt": "", "menuId": 13, "key": "证书续期详情", "api": {"id": 280, "uuid": "ee915cd4-b475-44ab-ba33-3e7ae8772a62", "createdAt": "2023-06-01 09:48:59", "updatedAt": "2023-08-11 08:49:30", "deletedAt": "", "path": "/ship-api/v1/cert/renewal/detail", "description": "船舶证书续期详情", "apiGroup": "ship/cert", "method": "GET", "necessary": 2}}, {"id": 1830, "createdAt": "", "updatedAt": "", "menuId": 13, "key": "检验登记详情", "api": {"id": 274, "uuid": "e65f8b35-175e-4b4f-b87e-8a0bfcc1c1a1", "createdAt": "2023-06-01 09:43:20", "updatedAt": "2023-08-11 08:49:30", "deletedAt": "", "path": "/ship-api/v1/cert/check/detail", "description": "证书检验登记详情\n", "apiGroup": "ship/cert", "method": "GET", "necessary": 2}}, {"id": 299, "createdAt": "", "updatedAt": "", "menuId": 13, "key": "证书新建", "api": {"id": 80, "uuid": "8b0c7c15-fe4b-43db-b076-920cc0baebbe", "createdAt": "2023-01-09 16:02:18", "updatedAt": "2023-08-11 08:49:30", "deletedAt": "", "path": "/ship-api/v1/cert/new", "description": "证书添加", "apiGroup": "ship/cert", "method": "POST", "necessary": 2}}, {"id": 1994, "createdAt": "", "updatedAt": "", "menuId": 13, "key": "证书保险规则列表", "api": {"id": 282, "uuid": "d555fcb9-288c-4fe3-9707-ee3c942c54dd", "createdAt": "2023-06-05 19:09:45", "updatedAt": "2023-08-11 08:49:30", "deletedAt": "", "path": "/power-api/v1/rule/cert/insurance/list/{offset}/{length}", "description": "证书保险规则列表", "apiGroup": "power/cert_ins", "method": "POST", "necessary": 2}}, {"id": 2024, "createdAt": "", "updatedAt": "", "menuId": 13, "key": "导入结果列表", "api": {"id": 283, "uuid": "3a252f85-5894-41ad-ac30-dcadb5be4b3f", "createdAt": "2023-06-06 15:54:17", "updatedAt": "2023-08-11 08:49:30", "deletedAt": "", "path": "/power-api/v1/imports/import/result/list/{offset}/{length}", "description": "导入结果列表", "apiGroup": "power/import", "method": "POST", "necessary": 2}}], "children": null}, {"id": 14, "uuid": "fe8ff9bb-fcf7-40d7-8b8e-0b1a7e875ee0", "createdAt": "", "updatedAt": "", "deletedAt": "", "platformId": 1, "endpointId": 1, "parentId": 12, "parentTitle": "", "path": "/ship-mgr/ship-insurance", "name": "ShipInsurance", "hidden": 2, "component": "@/views/ship-module/ship-insurance/index.vue", "sortNum": 9, "keepAlive": 2, "title": "船舶保险", "icon": "", "closeTab": 2, "openNewPage": 2, "params": null, "btns": [{"id": 303, "createdAt": "", "updatedAt": "", "menuId": 14, "key": "保险列表", "api": {"id": 73, "uuid": "f098e656-5577-4ca9-b7d4-c7634a2165c9", "createdAt": "2023-01-09 14:25:18", "updatedAt": "2023-08-10 17:09:00", "deletedAt": "", "path": "/ship-api/v1/insurance/list/{offset}/{length}", "description": "保险列表", "apiGroup": "ship/insurance", "method": "POST", "necessary": 2}}, {"id": 307, "createdAt": "", "updatedAt": "", "menuId": 14, "key": "保险删除", "api": {"id": 71, "uuid": "3eb18e2f-19bc-4f70-ac34-679c0a9826fb", "createdAt": "2023-01-09 14:24:51", "updatedAt": "2023-08-10 17:09:00", "deletedAt": "", "path": "/ship-api/v1/insurance/del", "description": "保险删除", "apiGroup": "ship/insurance", "method": "GET", "necessary": 2}}, {"id": 304, "createdAt": "", "updatedAt": "", "menuId": 14, "key": "保险新建", "api": {"id": 74, "uuid": "1333dadc-bedb-4842-bc1b-5965faccd485", "createdAt": "2023-01-09 14:25:33", "updatedAt": "2023-08-10 17:09:00", "deletedAt": "", "path": "/ship-api/v1/insurance/new", "description": "保险添加", "apiGroup": "ship/insurance", "method": "POST", "necessary": 2}}, {"id": 305, "createdAt": "", "updatedAt": "", "menuId": 14, "key": "保险编辑", "api": {"id": 75, "uuid": "29af9726-9b44-458c-8a2f-05e3cbd13a28", "createdAt": "2023-01-09 14:25:48", "updatedAt": "2023-08-10 17:09:00", "deletedAt": "", "path": "/ship-api/v1/insurance/update", "description": "保险更新", "apiGroup": "ship/insurance", "method": "POST", "necessary": 2}}, {"id": 306, "createdAt": "", "updatedAt": "", "menuId": 14, "key": "保险详情", "api": {"id": 72, "uuid": "58473dbf-6873-4c59-8260-6f527a160f47", "createdAt": "2023-01-09 14:25:03", "updatedAt": "2023-08-10 17:09:00", "deletedAt": "", "path": "/ship-api/v1/insurance/detail", "description": "保险详情", "apiGroup": "ship/insurance", "method": "GET", "necessary": 2}}, {"id": 321, "createdAt": "", "updatedAt": "", "menuId": 14, "key": "保险统计", "api": {"id": 113, "uuid": "57def7e1-351c-446c-9ff8-65bf5183186a", "createdAt": "2023-02-22 11:02:37", "updatedAt": "2023-08-11 17:41:57", "deletedAt": "", "path": "/ship-api/v1/insurance/expire/statisic", "description": "船舶保险统计", "apiGroup": "ship/insurance", "method": "GET", "necessary": 2}}, {"id": 1725, "createdAt": "", "updatedAt": "", "menuId": 14, "key": "保险续期", "api": {"id": 271, "uuid": "c034bf83-7834-4946-82c4-61d19acc0c63", "createdAt": "2023-06-01 09:30:05", "updatedAt": "2023-08-10 17:09:00", "deletedAt": "", "path": "/ship-api/v1/insurance/renewal", "description": "船舶保险续期", "apiGroup": "ship/insurance", "method": "POST", "necessary": 2}}, {"id": 1767, "createdAt": "", "updatedAt": "", "menuId": 14, "key": "导入确认完成", "api": {"id": 265, "uuid": "b2d289bf-4e6f-488d-86c5-8415d508d616", "createdAt": "2023-06-01 09:23:16", "updatedAt": "2023-08-11 17:28:23", "deletedAt": "", "path": "/power-api/v1/imports/complete", "description": "导入确认完成", "apiGroup": "power/import", "method": "POST", "necessary": 2}}, {"id": 1726, "createdAt": "", "updatedAt": "", "menuId": 14, "key": "保险续期详情", "api": {"id": 272, "uuid": "5a47cb09-9fac-4af3-9253-332cbf0d0cf2", "createdAt": "2023-06-01 09:30:38", "updatedAt": "2023-06-06 15:55:24", "deletedAt": "", "path": "/ship-api/v1/insurance/renewal/detail", "description": "船舶保险续期详情\n", "apiGroup": "ship/insurance", "method": "GET", "necessary": 2}}, {"id": 1764, "createdAt": "", "updatedAt": "", "menuId": 14, "key": "数据校验", "api": {"id": 262, "uuid": "f7a8007f-5088-4c0c-bdc1-a6e31f571bf2", "createdAt": "2023-06-01 09:21:02", "updatedAt": "2023-08-11 17:28:23", "deletedAt": "", "path": "/power-api/v1/imports/validate", "description": "校验数据\n", "apiGroup": "power/import", "method": "POST", "necessary": 2}}, {"id": 1765, "createdAt": "", "updatedAt": "", "menuId": 14, "key": "校验列表", "api": {"id": 263, "uuid": "8ac79724-131e-42e4-8ead-0fdf79c516b9", "createdAt": "2023-06-01 09:21:35", "updatedAt": "2023-08-11 17:28:23", "deletedAt": "", "path": "/power-api/v1/imports/validate/list/{offset}/{length}", "description": "校验列表", "apiGroup": "power/import", "method": "POST", "necessary": 2}}, {"id": 1766, "createdAt": "", "updatedAt": "", "menuId": 14, "key": "开始导入", "api": {"id": 264, "uuid": "bec6fb51-6383-4ad1-a655-81de80de272e", "createdAt": "2023-06-01 09:22:04", "updatedAt": "2023-08-11 17:28:23", "deletedAt": "", "path": "/power-api/v1/imports/exec", "description": "开始导入", "apiGroup": "power/import", "method": "POST", "necessary": 2}}, {"id": 2040, "createdAt": "", "updatedAt": "", "menuId": 14, "key": "导入结果列表", "api": {"id": 283, "uuid": "3a252f85-5894-41ad-ac30-dcadb5be4b3f", "createdAt": "2023-06-06 15:54:17", "updatedAt": "2023-08-11 08:49:30", "deletedAt": "", "path": "/power-api/v1/imports/import/result/list/{offset}/{length}", "description": "导入结果列表", "apiGroup": "power/import", "method": "POST", "necessary": 2}}], "children": null}, {"id": 15, "uuid": "38b39ecc-a12e-4ff9-88c7-b95b1026d668", "createdAt": "", "updatedAt": "", "deletedAt": "", "platformId": 1, "endpointId": 1, "parentId": 12, "parentTitle": "", "path": "/ship-mgr/ship-repair", "name": "ShipRepair", "hidden": 2, "component": "@/views/ship-module/ship-repair/index.vue", "sortNum": 8, "keepAlive": 2, "title": "维护记录", "icon": "", "closeTab": 2, "openNewPage": 2, "params": null, "btns": [{"id": 313, "createdAt": "", "updatedAt": "", "menuId": 15, "key": "维护记录更新", "api": {"id": 85, "uuid": "f6087729-7de6-41f0-8d8e-8650340a3d6a", "createdAt": "2023-01-09 17:11:13", "updatedAt": "2023-08-10 17:09:00", "deletedAt": "", "path": "/ship-api/v1/repair/update", "description": "维护记录更新", "apiGroup": "ship/repair", "method": "POST", "necessary": 2}}, {"id": 312, "createdAt": "", "updatedAt": "", "menuId": 15, "key": "维护记录新建", "api": {"id": 84, "uuid": "dedca99c-c648-4e15-9354-1f07f4baf9e8", "createdAt": "2023-01-09 17:11:02", "updatedAt": "2023-08-10 17:09:00", "deletedAt": "", "path": "/ship-api/v1/repair/new", "description": "维护记录新建", "apiGroup": "ship/repair", "method": "POST", "necessary": 2}}, {"id": 311, "createdAt": "", "updatedAt": "", "menuId": 15, "key": "维护记录列表", "api": {"id": 83, "uuid": "f3718dbf-a1bf-460e-91b2-3343cdb40f37", "createdAt": "2023-01-09 17:10:49", "updatedAt": "2023-08-10 17:09:00", "deletedAt": "", "path": "/ship-api/v1/repair/list/{offset}/{length}", "description": "维护记录列表", "apiGroup": "ship/repair", "method": "POST", "necessary": 2}}, {"id": 314, "createdAt": "", "updatedAt": "", "menuId": 15, "key": "维护记录详情", "api": {"id": 82, "uuid": "5b893387-17ab-42cd-b0d5-5dc0bc0240fd", "createdAt": "2023-01-09 17:10:38", "updatedAt": "2023-08-10 17:09:00", "deletedAt": "", "path": "/ship-api/v1/repair/detail", "description": "维护记录详情", "apiGroup": "ship/repair", "method": "GET", "necessary": 2}}], "children": null}, {"id": 112, "uuid": "8315bfcf-912a-4d5f-a2c4-eb15be7ee634", "createdAt": "", "updatedAt": "", "deletedAt": "", "platformId": 1, "endpointId": 1, "parentId": 12, "parentTitle": "", "path": "/ship-mgr/ship-material", "name": "ShipMaterial", "hidden": 2, "component": "@/views/ship-module/ship-material/index.vue", "sortNum": 7, "keepAlive": 2, "title": "备件记录", "icon": "", "closeTab": 2, "openNewPage": 2, "params": null, "btns": [{"id": 2369, "createdAt": "", "updatedAt": "", "menuId": 112, "key": "备件记录", "api": {"id": 116, "uuid": "902783e6-28d7-4ed0-89f2-6b568b49aa04", "createdAt": "2023-02-23 17:23:47", "updatedAt": "2023-08-10 17:09:00", "deletedAt": "", "path": "/ship-api/v1/material/list/{offset}/{length}", "description": "备件记录列表", "apiGroup": "ship/material", "method": "POST", "necessary": 2}}, {"id": 2370, "createdAt": "", "updatedAt": "", "menuId": 112, "key": "备件记录详情", "api": {"id": 115, "uuid": "84aec1b0-5666-40d8-b6ce-f3f7ab308a63", "createdAt": "2023-02-23 17:23:32", "updatedAt": "2023-08-10 17:09:00", "deletedAt": "", "path": "/ship-api/v1/material/detail", "description": "备件记录详情", "apiGroup": "ship/material", "method": "GET", "necessary": 2}}, {"id": 2371, "createdAt": "", "updatedAt": "", "menuId": 112, "key": "备件记录新建", "api": {"id": 117, "uuid": "74fa5cfd-b42e-4b75-8905-32e1913549e2", "createdAt": "2023-02-23 17:27:01", "updatedAt": "2023-08-10 17:09:00", "deletedAt": "", "path": "/ship-api/v1/material/new", "description": "备件记录新建", "apiGroup": "ship/material", "method": "POST", "necessary": 2}}, {"id": 2372, "createdAt": "", "updatedAt": "", "menuId": 112, "key": "备件记录更新", "api": {"id": 118, "uuid": "c6725477-029e-492a-9373-1c906e37439e", "createdAt": "2023-02-23 17:28:08", "updatedAt": "2023-08-10 17:09:00", "deletedAt": "", "path": "/ship-api/v1/material/update", "description": "备件记录更新", "apiGroup": "ship/material", "method": "POST", "necessary": 2}}], "children": null}, {"id": 107, "uuid": "97a16aaa-d8ab-443a-9c4f-52b66b36a11c", "createdAt": "", "updatedAt": "", "deletedAt": "", "platformId": 1, "endpointId": 1, "parentId": 12, "parentTitle": "", "path": "/oil-mgr/oil-mgr", "name": "OilMgr", "hidden": 2, "component": "@/views/oil-module/oil-mgr/index.vue", "sortNum": 0, "keepAlive": 2, "title": "油料管理", "icon": "", "closeTab": 2, "openNewPage": 2, "params": null, "btns": [{"id": 2140, "createdAt": "", "updatedAt": "", "menuId": 107, "key": "存有统计", "api": {"id": 219, "uuid": "04e4231b-aa82-42e7-a7d0-e9c3517b0f84", "createdAt": "2023-04-18 09:57:13", "updatedAt": "2023-08-11 17:41:57", "deletedAt": "", "path": "/ship-api/v1/device/oil/static", "description": "存油统计", "apiGroup": "ship/device", "method": "POST", "necessary": 2}}, {"id": 2141, "createdAt": "", "updatedAt": "", "menuId": 107, "key": "油料变化统计", "api": {"id": 284, "uuid": "683412bc-896a-440c-91e3-2dc51251493e", "createdAt": "2023-06-29 09:29:25", "updatedAt": "2023-07-03 13:11:28", "deletedAt": "", "path": "/ship-api/v1/oil/record/static", "description": "油料变化记录统计", "apiGroup": "ship/oil", "method": "POST", "necessary": 2}}, {"id": 2142, "createdAt": "", "updatedAt": "", "menuId": 107, "key": "油料登记导出", "api": {"id": 16, "uuid": "8187fd84-f169-4b9f-b418-414b9e645709", "createdAt": "2022-12-28 14:03:24", "updatedAt": "2023-08-11 17:41:57", "deletedAt": "", "path": "/", "description": "权限控制用，无对应接口", "apiGroup": "", "method": "POST", "necessary": 2}}, {"id": 2143, "createdAt": "", "updatedAt": "", "menuId": 107, "key": "船舶系数配置", "api": {"id": 16, "uuid": "8187fd84-f169-4b9f-b418-414b9e645709", "createdAt": "2022-12-28 14:03:24", "updatedAt": "2023-08-11 17:41:57", "deletedAt": "", "path": "/", "description": "权限控制用，无对应接口", "apiGroup": "", "method": "POST", "necessary": 2}}, {"id": 2144, "createdAt": "", "updatedAt": "", "menuId": 107, "key": "油料登记", "api": {"id": 287, "uuid": "4e400ff1-bc67-460c-9067-c7b8f8ae15da", "createdAt": "2023-06-29 09:31:46", "updatedAt": "2023-07-03 13:11:28", "deletedAt": "", "path": "/ship-api/v1/oil/new", "description": "油料登记", "apiGroup": "ship/oil", "method": "POST", "necessary": 2}}, {"id": 2145, "createdAt": "", "updatedAt": "", "menuId": 107, "key": "船舶油况列表", "api": {"id": 293, "uuid": "048c5cf8-0d1a-4991-b831-d4a36fe8affb", "createdAt": "2023-06-29 09:48:50", "updatedAt": "2023-07-03 13:11:28", "deletedAt": "", "path": "/ship-api/v1/oil/ship/list/{offset}/{length}", "description": "船舶油况列表\n", "apiGroup": "ship/oil", "method": "POST", "necessary": 2}}, {"id": 2146, "createdAt": "", "updatedAt": "", "menuId": 107, "key": "油料登记列表", "api": {"id": 286, "uuid": "a582bc47-7054-419e-b611-8054b5d89759", "createdAt": "2023-06-29 09:31:11", "updatedAt": "2023-07-13 15:56:13", "deletedAt": "", "path": "/ship-api/v1/oil/list/{office}/{length}", "description": "油料记录列表", "apiGroup": "ship/oil", "method": "POST", "necessary": 2}}, {"id": 2147, "createdAt": "", "updatedAt": "", "menuId": 107, "key": "油料编辑", "api": {"id": 288, "uuid": "a87340cc-bc27-4c9e-8136-19fa2baf07c5", "createdAt": "2023-06-29 09:32:50", "updatedAt": "2023-07-03 13:11:28", "deletedAt": "", "path": "/ship-api/v1/oil/update", "description": "油料编辑", "apiGroup": "ship/oil", "method": "POST", "necessary": 2}}, {"id": 2148, "createdAt": "", "updatedAt": "", "menuId": 107, "key": "日报详情", "api": {"id": 16, "uuid": "8187fd84-f169-4b9f-b418-414b9e645709", "createdAt": "2022-12-28 14:03:24", "updatedAt": "2023-08-11 17:41:57", "deletedAt": "", "path": "/", "description": "权限控制用，无对应接口", "apiGroup": "", "method": "POST", "necessary": 2}}, {"id": 2149, "createdAt": "", "updatedAt": "", "menuId": 107, "key": "油料删除", "api": {"id": 290, "uuid": "80ce5ee7-596c-439b-a6c8-a74f2ed7ff6d", "createdAt": "2023-06-29 09:36:17", "updatedAt": "2023-07-03 13:11:28", "deletedAt": "", "path": "/ship-api/v1/oil/del", "description": "油料删除", "apiGroup": "ship/oil", "method": "GET", "necessary": 2}}, {"id": 2150, "createdAt": "", "updatedAt": "", "menuId": 107, "key": "设备运行时长", "api": {"id": 295, "uuid": "c7911a43-50de-4349-b7cc-8928ae9ee5fd", "createdAt": "2023-06-29 09:53:05", "updatedAt": "2023-07-03 13:11:28", "deletedAt": "", "path": "/ship-api/v1/daily/device/static", "description": "日报设备运行时间统计\n", "apiGroup": "ship/daily", "method": "POST", "necessary": 2}}, {"id": 2211, "createdAt": "", "updatedAt": "", "menuId": 107, "key": "查看记录", "api": {"id": 16, "uuid": "8187fd84-f169-4b9f-b418-414b9e645709", "createdAt": "2022-12-28 14:03:24", "updatedAt": "2023-08-11 17:41:57", "deletedAt": "", "path": "/", "description": "权限控制用，无对应接口", "apiGroup": "", "method": "POST", "necessary": 2}}, {"id": 2248, "createdAt": "", "updatedAt": "", "menuId": 107, "key": "油料详情", "api": {"id": 289, "uuid": "ab4ea70c-88f2-4055-b55d-9c0fb373cb81", "createdAt": "2023-06-29 09:35:12", "updatedAt": "2023-07-03 13:11:28", "deletedAt": "", "path": "/ship-api/v1/oil/detail", "description": "油料详情", "apiGroup": "ship/oil", "method": "GET", "necessary": 2}}], "children": null}, {"id": 110, "uuid": "e75972d0-426f-4aaa-aa99-e70622faf0bc", "createdAt": "", "updatedAt": "", "deletedAt": "", "platformId": 1, "endpointId": 1, "parentId": 12, "parentTitle": "", "path": "/ship-mgr/ship-attachs", "name": "ShipAttachs", "hidden": 1, "component": "@/views/ship-module/ship-attachs/index.vue", "sortNum": 0, "keepAlive": 2, "title": "管理附件", "icon": "", "closeTab": 1, "openNewPage": 2, "params": null, "btns": [{"id": 2367, "createdAt": "", "updatedAt": "", "menuId": 110, "key": "附件列表", "api": {"id": 306, "uuid": "302f5d82-c3a7-4c26-ac4b-6116070e1ec6", "createdAt": "2023-07-13 15:12:05", "updatedAt": "2023-08-10 17:09:00", "deletedAt": "", "path": "/ship-api/v1/attach/list/{offset}/{length}", "description": "船舶附件列表\n", "apiGroup": "ship/attach", "method": "POST", "necessary": 2}}, {"id": 2368, "createdAt": "", "updatedAt": "", "menuId": 110, "key": "附件更新", "api": {"id": 307, "uuid": "efed39bb-57fe-492c-8d75-34d545bdb376", "createdAt": "2023-07-13 15:12:42", "updatedAt": "2023-08-10 17:09:00", "deletedAt": "", "path": "/ship-api/v1/attach/update", "description": "船舶附件更新\n", "apiGroup": "ship/attach", "method": "POST", "necessary": 2}}], "children": null}, {"id": 109, "uuid": "97f52439-84d4-43ed-b04f-7ccf7a6674bd", "createdAt": "", "updatedAt": "", "deletedAt": "", "platformId": 1, "endpointId": 1, "parentId": 12, "parentTitle": "", "path": "/oil-setting/oil-setting", "name": "OilSetting", "hidden": 1, "component": "@/views/oil-module/oil-setting/index.vue", "sortNum": 0, "keepAlive": 2, "title": "船舶系数配置", "icon": "", "closeTab": 1, "openNewPage": 2, "params": null, "btns": [{"id": 2154, "createdAt": "", "updatedAt": "", "menuId": 109, "key": "船舶系数列表", "api": {"id": 292, "uuid": "e5844c9d-3f38-409a-b85d-3db4157dbcf6", "createdAt": "2023-06-29 09:47:49", "updatedAt": "2023-06-29 10:33:35", "deletedAt": "", "path": "/ship-api/v1/device/coefficient/list", "description": "设备系数配置列表\n", "apiGroup": "ship/device", "method": "GET", "necessary": 2}}, {"id": 2155, "createdAt": "", "updatedAt": "", "menuId": 109, "key": "船舶系数更新", "api": {"id": 294, "uuid": "020d5b4f-84df-4fb8-9a71-5c2b64700624", "createdAt": "2023-06-29 09:49:47", "updatedAt": "2023-06-29 10:33:35", "deletedAt": "", "path": "/ship-api/v1/device/coefficient/update", "description": "设备系数配置更新\n", "apiGroup": "ship/device", "method": "POST", "necessary": 2}}], "children": null}, {"id": 108, "uuid": "14f88645-566e-40b4-8291-ac8c902b29c2", "createdAt": "", "updatedAt": "", "deletedAt": "", "platformId": 1, "endpointId": 1, "parentId": 12, "parentTitle": "", "path": "/oil-reg/oil-reg", "name": "OilReg", "hidden": 1, "component": "@/views/oil-module/oil-reg/index.vue", "sortNum": 0, "keepAlive": 2, "title": "油料记录", "icon": "", "closeTab": 1, "openNewPage": 2, "params": null, "btns": [{"id": 2151, "createdAt": "", "updatedAt": "", "menuId": 108, "key": "油料登记记录", "api": {"id": 286, "uuid": "a582bc47-7054-419e-b611-8054b5d89759", "createdAt": "2023-06-29 09:31:11", "updatedAt": "2023-07-13 15:56:13", "deletedAt": "", "path": "/ship-api/v1/oil/list/{office}/{length}", "description": "油料记录列表", "apiGroup": "ship/oil", "method": "POST", "necessary": 2}}, {"id": 2152, "createdAt": "", "updatedAt": "", "menuId": 108, "key": "油料编辑", "api": {"id": 288, "uuid": "a87340cc-bc27-4c9e-8136-19fa2baf07c5", "createdAt": "2023-06-29 09:32:50", "updatedAt": "2023-07-03 13:11:28", "deletedAt": "", "path": "/ship-api/v1/oil/update", "description": "油料编辑", "apiGroup": "ship/oil", "method": "POST", "necessary": 2}}, {"id": 2153, "createdAt": "", "updatedAt": "", "menuId": 108, "key": "油料删除", "api": {"id": 290, "uuid": "80ce5ee7-596c-439b-a6c8-a74f2ed7ff6d", "createdAt": "2023-06-29 09:36:17", "updatedAt": "2023-07-03 13:11:28", "deletedAt": "", "path": "/ship-api/v1/oil/del", "description": "油料删除", "apiGroup": "ship/oil", "method": "GET", "necessary": 2}}, {"id": 2244, "createdAt": "", "updatedAt": "", "menuId": 108, "key": "油料详情", "api": {"id": 289, "uuid": "ab4ea70c-88f2-4055-b55d-9c0fb373cb81", "createdAt": "2023-06-29 09:35:12", "updatedAt": "2023-07-03 13:11:28", "deletedAt": "", "path": "/ship-api/v1/oil/detail", "description": "油料详情", "apiGroup": "ship/oil", "method": "GET", "necessary": 2}}, {"id": 2295, "createdAt": "", "updatedAt": "", "menuId": 108, "key": "日报详情", "api": {"id": 16, "uuid": "8187fd84-f169-4b9f-b418-414b9e645709", "createdAt": "2022-12-28 14:03:24", "updatedAt": "2023-08-11 17:41:57", "deletedAt": "", "path": "/", "description": "权限控制用，无对应接口", "apiGroup": "", "method": "POST", "necessary": 2}}], "children": null}, {"id": 102, "uuid": "be0233c7-869a-4754-97e9-eb1629d8198e", "createdAt": "", "updatedAt": "", "deletedAt": "", "platformId": 1, "endpointId": 1, "parentId": 12, "parentTitle": "", "path": "/ship-mgr/ship-cert-detail", "name": "ShipCertDetail", "hidden": 1, "component": "@/views/ship-module/ship-mgr/ship-detail/insurance-cert-mgr/components/CertDetail.vue", "sortNum": 0, "keepAlive": 2, "title": "船舶证书详情", "icon": "", "closeTab": 1, "openNewPage": 2, "params": null, "btns": [{"id": 1712, "createdAt": "", "updatedAt": "", "menuId": 102, "key": "证书详情", "api": {"id": 78, "uuid": "*************-43c9-a2d1-************", "createdAt": "2023-01-09 16:00:17", "updatedAt": "2023-08-11 08:49:30", "deletedAt": "", "path": "/ship-api/v1/cert/detail", "description": "证书详情", "apiGroup": "ship/cert", "method": "GET", "necessary": 2}}, {"id": 1713, "createdAt": "", "updatedAt": "", "menuId": 102, "key": "检验记录列表", "api": {"id": 275, "uuid": "c27b7759-a16f-4907-83c3-680ab6fbd3b1", "createdAt": "2023-06-01 09:44:11", "updatedAt": "2023-06-01 09:59:42", "deletedAt": "", "path": "/ship-api/v1/cert/check/list", "description": "证书检验登记列表\n", "apiGroup": "ship/cert", "method": "POST", "necessary": 2}}, {"id": 1714, "createdAt": "", "updatedAt": "", "menuId": 102, "key": "检验记录详情", "api": {"id": 277, "uuid": "09131edb-3b36-43d8-a6e4-74eadb881cc2", "createdAt": "2023-06-01 09:45:54", "updatedAt": "2023-06-01 09:59:42", "deletedAt": "", "path": "/ship-api/v1/cert/check/record/detail", "description": "检登记录详情\n", "apiGroup": "ship/cert", "method": "GET", "necessary": 2}}, {"id": 1715, "createdAt": "", "updatedAt": "", "menuId": 102, "key": "检验记录更新", "api": {"id": 278, "uuid": "0e7d442c-4c23-4743-bce0-e2c7cca9f374", "createdAt": "2023-06-01 09:46:17", "updatedAt": "2023-06-01 09:59:42", "deletedAt": "", "path": "/ship-api/v1/cert/check/record/update", "description": "检登记录更新\n", "apiGroup": "ship/cert", "method": "POST", "necessary": 2}}], "children": null}]}, {"id": 16, "uuid": "fd3d56db-6802-4c4f-9f3f-11b73fd272ce", "createdAt": "", "updatedAt": "", "deletedAt": "", "platformId": 1, "endpointId": 1, "parentId": 0, "parentTitle": "", "path": "/seamap-mgr/seamap-mgr", "name": "SeamapMgr", "hidden": 2, "component": "@/views/seamap-module/index.vue", "sortNum": 89, "keepAlive": 2, "title": "船舶海图", "icon": "seamap", "closeTab": 2, "openNewPage": 2, "params": null, "btns": [{"id": 374, "createdAt": "", "updatedAt": "", "menuId": 16, "key": "船舶历史轨迹", "api": {"id": 134, "uuid": "76d5f807-8b0a-4b14-bad9-65d736864f31", "createdAt": "2023-02-28 16:42:12", "updatedAt": "2023-08-10 17:03:58", "deletedAt": "", "path": "/ship-api/v1/sea/ship/history", "description": "船舶历史轨迹", "apiGroup": "sea", "method": "POST", "necessary": 2}}, {"id": 375, "createdAt": "", "updatedAt": "", "menuId": 16, "key": "船舶信息", "api": {"id": 133, "uuid": "b775c1c6-0cb5-456a-9d91-7e7fed8f594b", "createdAt": "2023-02-28 16:41:53", "updatedAt": "2023-08-10 17:15:22", "deletedAt": "", "path": "/ship-api/v1/sea/ship/detail", "description": "船舶信息", "apiGroup": "sea", "method": "POST", "necessary": 2}}, {"id": 376, "createdAt": "", "updatedAt": "", "menuId": 16, "key": "坐标推送", "api": {"id": 132, "uuid": "6efd3c2e-d45f-439d-ad02-5de1679edd02", "createdAt": "2023-02-28 16:41:38", "updatedAt": "2023-08-10 17:03:58", "deletedAt": "", "path": "/ship-api/v1/sea/location/push", "description": "坐标推送", "apiGroup": "sea", "method": "POST", "necessary": 2}}, {"id": 373, "createdAt": "", "updatedAt": "", "menuId": 16, "key": "海图船舶列表", "api": {"id": 135, "uuid": "6259595c-2745-4ee1-b2e8-71b4ba9c9fdc", "createdAt": "2023-02-28 16:42:38", "updatedAt": "2023-08-10 17:15:22", "deletedAt": "", "path": "/ship-api/v1/sea/ship/list", "description": "海图船舶列表\n", "apiGroup": "sea", "method": "POST", "necessary": 2}}, {"id": 372, "createdAt": "", "updatedAt": "", "menuId": 16, "key": "海域列表", "api": {"id": 131, "uuid": "9d272d5c-d87e-462c-953a-d774b185f8d9", "createdAt": "2023-02-28 16:41:23", "updatedAt": "2023-08-10 17:03:58", "deletedAt": "", "path": "/ship-api/v1/sea/list", "description": "海域下拉列表", "apiGroup": "sea", "method": "POST", "necessary": 2}}, {"id": 1240, "createdAt": "", "updatedAt": "", "menuId": 16, "key": "海浪信息", "api": {"id": 242, "uuid": "41fbb459-4afa-422d-84d6-ef06e50a2ec0", "createdAt": "2023-05-15 16:25:24", "updatedAt": "2023-08-10 17:15:22", "deletedAt": "", "path": "/ship-api/v1/weather/wave/get", "description": "获取海浪信息\n", "apiGroup": "ship/weather", "method": "GET", "necessary": 2}}, {"id": 1241, "createdAt": "", "updatedAt": "", "menuId": 16, "key": "天气信息", "api": {"id": 241, "uuid": "1ca6b9b5-3c58-4412-8617-06815270a7de", "createdAt": "2023-05-15 16:24:46", "updatedAt": "2023-08-10 17:15:22", "deletedAt": "", "path": "/ship-api/v1/weather/basic/get", "description": "获取基础信息", "apiGroup": "ship/weather", "method": "GET", "necessary": 2}}, {"id": 1601, "createdAt": "", "updatedAt": "", "menuId": 16, "key": "天气预报", "api": {"id": 16, "uuid": "8187fd84-f169-4b9f-b418-414b9e645709", "createdAt": "2022-12-28 14:03:24", "updatedAt": "2023-08-11 17:41:57", "deletedAt": "", "path": "/", "description": "权限控制用，无对应接口", "apiGroup": "", "method": "POST", "necessary": 2}}, {"id": 2550, "createdAt": "", "updatedAt": "", "menuId": 16, "key": "关注异动", "api": {"id": 311, "uuid": "13e7df8f-9d11-4701-9e6d-8570ca1cdd03", "createdAt": "2023-08-10 16:46:18", "updatedAt": "2023-08-10 17:15:22", "deletedAt": "", "path": "/ship-api/v1/location/subs/new", "description": "位置订阅新建", "apiGroup": "ship/seamap", "method": "POST", "necessary": 2}}, {"id": 2551, "createdAt": "", "updatedAt": "", "menuId": 16, "key": "取关异动", "api": {"id": 309, "uuid": "7a527569-c407-4c31-90f6-312ec898a475", "createdAt": "2023-08-10 16:44:24", "updatedAt": "2023-08-10 17:15:22", "deletedAt": "", "path": "/ship-api/v1/location/subs/del", "description": "位置订阅删除", "apiGroup": "ship/seamap", "method": "GET", "necessary": 2}}, {"id": 2552, "createdAt": "", "updatedAt": "", "menuId": 16, "key": "更新位置", "api": {"id": 312, "uuid": "d64ca0d0-99ee-4f08-a466-c197b9c9c562", "createdAt": "2023-08-10 16:47:30", "updatedAt": "2023-08-10 17:15:22", "deletedAt": "", "path": "/ship-api/v1/location/subs/update", "description": "位置订阅更新", "apiGroup": "ship/seamap", "method": "POST", "necessary": 2}}, {"id": 2553, "createdAt": "", "updatedAt": "", "menuId": 16, "key": "位置订阅详情", "api": {"id": 310, "uuid": "8df274f0-d63b-4b15-b51e-79871ed54950", "createdAt": "2023-08-10 16:45:40", "updatedAt": "2023-08-10 17:15:22", "deletedAt": "", "path": "/ship-api/v1/location/subs/detail", "description": "位置订阅详情", "apiGroup": "ship/seamap", "method": "GET", "necessary": 2}}], "children": null}, {"id": 17, "uuid": "7cdd2875-3423-41de-a11b-ddefb560e45a", "createdAt": "", "updatedAt": "", "deletedAt": "", "platformId": 1, "endpointId": 1, "parentId": 0, "parentTitle": "", "path": "/ship-dispatch/dispatch-mgr", "name": "DispatchMgr", "hidden": 2, "component": "@/views/dispatch-module/dispatch-mgr/index.vue", "sortNum": 88, "keepAlive": 2, "title": "船舶调度", "icon": "ship-dispatch", "closeTab": 2, "openNewPage": 2, "params": null, "btns": null, "children": [{"id": 19, "uuid": "dd17354d-7a9d-4d41-8809-686934784b7e", "createdAt": "", "updatedAt": "", "deletedAt": "", "platformId": 1, "endpointId": 1, "parentId": 17, "parentTitle": "", "path": "/ship-dispatch/dispatch-record", "name": "DispatchRecord", "hidden": 2, "component": "@/views/dispatch-module/dispatch-record/dispatch/index.vue", "sortNum": 0, "keepAlive": 2, "title": "调度记录", "icon": "", "closeTab": 2, "openNewPage": 2, "params": null, "btns": [{"id": 386, "createdAt": "", "updatedAt": "", "menuId": 19, "key": "调度列表", "api": {"id": 126, "uuid": "71e33807-d069-4271-9acb-8ec275a87f21", "createdAt": "2023-02-28 16:36:48", "updatedAt": "2023-07-13 18:15:11", "deletedAt": "", "path": "/ship-api/v1/dispatch/list/{offset}/{length}", "description": "调度列表", "apiGroup": "dispatch/record", "method": "POST", "necessary": 2}}, {"id": 404, "createdAt": "", "updatedAt": "", "menuId": 19, "key": "调度导出", "api": {"id": 16, "uuid": "8187fd84-f169-4b9f-b418-414b9e645709", "createdAt": "2022-12-28 14:03:24", "updatedAt": "2023-08-11 17:41:57", "deletedAt": "", "path": "/", "description": "权限控制用，无对应接口", "apiGroup": "", "method": "POST", "necessary": 2}}], "children": null}, {"id": 18, "uuid": "04be2ee6-bc13-4381-b716-a68d949593c9", "createdAt": "", "updatedAt": "", "deletedAt": "", "platformId": 1, "endpointId": 1, "parentId": 17, "parentTitle": "", "path": "/ship-dispatch/dispatch-mgr", "name": "DispatchMgr", "hidden": 2, "component": "@/views/dispatch-module/dispatch-mgr/index.vue", "sortNum": 0, "keepAlive": 2, "title": "调度管理", "icon": "", "closeTab": 2, "openNewPage": 2, "params": null, "btns": [{"id": 379, "createdAt": "", "updatedAt": "", "menuId": 18, "key": "调度详情", "api": {"id": 124, "uuid": "aebd16d5-eabc-4cd5-8b9c-c5105f8c5089", "createdAt": "2023-02-28 16:35:18", "updatedAt": "2023-06-29 13:49:15", "deletedAt": "", "path": "/ship-api/v1/dispatch/detail", "description": "调度详情", "apiGroup": "dispatch/calendar", "method": "GET", "necessary": 2}}, {"id": 380, "createdAt": "", "updatedAt": "", "menuId": 18, "key": "调度删除", "api": {"id": 123, "uuid": "5670560c-1412-4cd2-80d5-822a7d3430cd", "createdAt": "2023-02-28 16:34:26", "updatedAt": "2023-07-13 18:15:11", "deletedAt": "", "path": "/ship-api/v1/dispatch/del", "description": "调度删除", "apiGroup": "dispatch/calendar", "method": "GET", "necessary": 2}}, {"id": 381, "createdAt": "", "updatedAt": "", "menuId": 18, "key": "甘特图条形列表", "api": {"id": 125, "uuid": "fdeb8970-492d-485e-937b-76051e45a51c", "createdAt": "2023-02-28 16:35:54", "updatedAt": "2023-08-11 17:41:57", "deletedAt": "", "path": "/ship-api/v1/dispatch/gantt/schedule/list", "description": "甘特图条形列表", "apiGroup": "dispatch/gantt", "method": "POST", "necessary": 2}}, {"id": 382, "createdAt": "", "updatedAt": "", "menuId": 18, "key": "日历条形列表", "api": {"id": 122, "uuid": "e871da73-1d1f-4ad6-bfad-f6dbade28eb5", "createdAt": "2023-02-28 16:34:02", "updatedAt": "2023-06-29 13:49:15", "deletedAt": "", "path": "/ship-api/v1/dispatch/calendar/schedule/list", "description": "日历条形列表", "apiGroup": "dispatch/calendar", "method": "POST", "necessary": 2}}, {"id": 383, "createdAt": "", "updatedAt": "", "menuId": 18, "key": "日历侧边搜索列表", "api": {"id": 121, "uuid": "81766266-5e8c-49bd-9a69-8ae250c9618a", "createdAt": "2023-02-28 16:33:33", "updatedAt": "2023-06-29 13:49:15", "deletedAt": "", "path": "/ship-api/v1/dispatch/calendar/search/list", "description": "日历侧边搜索", "apiGroup": "dispatch/calendar", "method": "POST", "necessary": 2}}, {"id": 385, "createdAt": "", "updatedAt": "", "menuId": 18, "key": "船舶任务时间重叠判断", "api": {"id": 120, "uuid": "3df09c91-4b4f-42cf-835e-eb8c9bbd8b63", "createdAt": "2023-02-28 16:32:35", "updatedAt": "2023-07-13 18:15:11", "deletedAt": "", "path": "/ship-api/v1/dispatch/task/overlapping", "description": "船舶任务时间重叠判断", "apiGroup": "dispatch", "method": "POST", "necessary": 2}}, {"id": 384, "createdAt": "", "updatedAt": "", "menuId": 18, "key": "日历侧边调度列表", "api": {"id": 119, "uuid": "b9eabbc0-429b-4268-b564-545da3749377", "createdAt": "2023-02-28 16:30:17", "updatedAt": "2023-07-13 18:15:11", "deletedAt": "", "path": "/ship-api/v1/dispatch/calendar/list/{offset}/{length}", "description": "日历侧边调度列表", "apiGroup": "dispatch/calendar", "method": "POST", "necessary": 2}}, {"id": 378, "createdAt": "", "updatedAt": "", "menuId": 18, "key": "调度新建", "api": {"id": 127, "uuid": "b52284d2-6a39-403e-b052-0e46ee3b843f", "createdAt": "2023-02-28 16:37:17", "updatedAt": "2023-07-13 18:15:11", "deletedAt": "", "path": "/ship-api/v1/dispatch/new", "description": "调度新建", "apiGroup": "dispatch", "method": "POST", "necessary": 2}}, {"id": 377, "createdAt": "", "updatedAt": "", "menuId": 18, "key": "调度编辑", "api": {"id": 130, "uuid": "e1535c11-cad3-4490-9dba-fe8a9245ab47", "createdAt": "2023-02-28 16:40:25", "updatedAt": "2023-07-13 18:15:11", "deletedAt": "", "path": "/ship-api/v1/dispatch/update", "description": "调度更新", "apiGroup": "dispatch/calendar", "method": "POST", "necessary": 2}}, {"id": 407, "createdAt": "", "updatedAt": "", "menuId": 18, "key": "甘特图", "api": {"id": 16, "uuid": "8187fd84-f169-4b9f-b418-414b9e645709", "createdAt": "2022-12-28 14:03:24", "updatedAt": "2023-08-11 17:41:57", "deletedAt": "", "path": "/", "description": "权限控制用，无对应接口", "apiGroup": "", "method": "POST", "necessary": 2}}, {"id": 406, "createdAt": "", "updatedAt": "", "menuId": 18, "key": "工作日历", "api": {"id": 16, "uuid": "8187fd84-f169-4b9f-b418-414b9e645709", "createdAt": "2022-12-28 14:03:24", "updatedAt": "2023-08-11 17:41:57", "deletedAt": "", "path": "/", "description": "权限控制用，无对应接口", "apiGroup": "", "method": "POST", "necessary": 2}}, {"id": 922, "createdAt": "", "updatedAt": "", "menuId": 18, "key": "项目船舶列表", "api": {"id": 206, "uuid": "300866bd-c331-494d-8bc4-0909e52d458f", "createdAt": "2023-04-17 14:23:38", "updatedAt": "2023-08-10 18:23:34", "deletedAt": "", "path": "/ship-api/v1/project/dispatch/ship/list", "description": "项目中船舶下拉列表", "apiGroup": "project/dispatch", "method": "POST", "necessary": 2}}, {"id": 923, "createdAt": "", "updatedAt": "", "menuId": 18, "key": "已授权项目列表", "api": {"id": 144, "uuid": "78c87215-085b-4c95-a122-5b6ff7764f53", "createdAt": "2023-03-21 16:43:39", "updatedAt": "2023-07-13 18:15:11", "deletedAt": "", "path": "/ship-api/v1/project/auth/list/{offset}/{length}", "description": "船东已授权的项目列表\n", "apiGroup": "ship/project/corp", "method": "POST", "necessary": 2}}], "children": null}]}, {"id": 53, "uuid": "7cdd2875-3423-41de-a11b-ddefb560e45b", "createdAt": "", "updatedAt": "", "deletedAt": "", "platformId": 1, "endpointId": 1, "parentId": 0, "parentTitle": "", "path": "/warehouse/warehouse-mgr", "name": "Warehouse", "hidden": 2, "component": "@/views/warehouse-module/warehouse-mgr/index.vue", "sortNum": 88, "keepAlive": 2, "title": "应急仓库管理", "icon": "home", "closeTab": 2, "openNewPage": 2, "params": null, "btns": null, "children": [{"id": 54, "uuid": "dd17354d-7a9d-4d41-8809-686934784b7a", "createdAt": "", "updatedAt": "", "deletedAt": "", "platformId": 1, "endpointId": 1, "parentId": 17, "parentTitle": "", "path": "/warehouse-module/warehouse-mgr", "name": "WarehouseMgr", "hidden": 2, "component": "@/views/warehouse-module/warehouse-mgr/index.vue", "sortNum": 0, "keepAlive": 2, "title": "应急仓库一览", "icon": "home", "closeTab": 2, "openNewPage": 2, "params": null, "btns": [], "children": [{"id": 4, "uuid": "6f1c7c5d-8be5-4c0e-a0f9-571d973dd5ec", "createdAt": "", "updatedAt": "", "deletedAt": "", "platformId": 1, "endpointId": 1, "parentId": 3, "parentTitle": "", "path": "/warehouse-module/warehouse-mgr/detail", "name": "WarehouseDetail", "hidden": 1, "component": "@/views/warehouse-module/warehouse-mgr/Detail.vue", "sortNum": 0, "keepAlive": 2, "title": "查看详情", "icon": "", "closeTab": 1, "openNewPage": 2, "params": null, "btns": [], "children": null}]}, {"id": 18, "uuid": "04be2ee6-bc13-4381-b716-a68d949593c9", "createdAt": "", "updatedAt": "", "deletedAt": "", "platformId": 1, "endpointId": 1, "parentId": 17, "parentTitle": "", "path": "/warehouse-module/material-mgr", "name": "MaterialMgr", "hidden": 2, "component": "@/views/warehouse-module/material-mgr/index.vue", "sortNum": 0, "keepAlive": 2, "title": "物资管理", "icon": "material", "closeTab": 2, "openNewPage": 2, "params": null, "btns": [], "children": [{"id": 4, "uuid": "6f1c7c5d-8be5-4c0e-a0f9-571d973dd5ec", "createdAt": "", "updatedAt": "", "deletedAt": "", "platformId": 1, "endpointId": 1, "parentId": 3, "parentTitle": "", "path": "/warehouse-module/material-mgr/material-type", "name": "MaterialType", "hidden": 1, "component": "@/views//warehouse-module/material-mgr/material-type/List.vue", "sortNum": 0, "keepAlive": 2, "title": "物资类别", "icon": "", "closeTab": 1, "openNewPage": 2, "params": null, "btns": [], "children": null}]}]}, {"id": 52, "uuid": "7cdd2875-3423-41de-a11b-ddefb560e45a", "createdAt": "", "updatedAt": "", "deletedAt": "", "platformId": 1, "endpointId": 1, "parentId": 0, "parentTitle": "", "path": "/course-train/course-management", "name": "CourseTrain", "hidden": 2, "component": "@/views/course-module/course-management/index.vue", "sortNum": 88, "keepAlive": 2, "title": "培训课程", "icon": "folder-open", "closeTab": 2, "openNewPage": 2, "params": null, "btns": null, "children": [{"id": 19, "uuid": "dd17354d-7a9d-4d41-8809-686934784b7e", "createdAt": "", "updatedAt": "", "deletedAt": "", "platformId": 1, "endpointId": 1, "parentId": 17, "parentTitle": "", "path": "/course-train/course-management", "name": "CourseManagement", "hidden": 2, "component": "@/views/course-module/course-management/index.vue", "sortNum": 0, "keepAlive": 1, "title": "课程管理", "icon": "", "closeTab": 2, "openNewPage": 2, "params": null, "btns": [], "children": [{"id": 4, "uuid": "6f1c7c5d-8be5-4c0e-a0f9-571d973dd5ec", "createdAt": "", "updatedAt": "", "deletedAt": "", "platformId": 1, "endpointId": 1, "parentId": 3, "parentTitle": "", "path": "/course-train/course-edit", "name": "CourseEdit", "hidden": 1, "component": "@/views/course-module/course-management/course-edit/index.vue", "sortNum": 0, "keepAlive": 2, "title": "添加课程", "icon": "", "closeTab": 1, "openNewPage": 2, "params": null, "btns": [], "children": null}, {"id": 4, "uuid": "6f1c7c5d-8be5-4c0e-a0f9-571d973dd5ec", "createdAt": "", "updatedAt": "", "deletedAt": "", "platformId": 1, "endpointId": 1, "parentId": 3, "parentTitle": "", "path": "/course-train/course-detail", "name": "CourseDetail", "hidden": 1, "component": "@/views/course-module/course-management/course-detail/index.vue", "sortNum": 0, "keepAlive": 2, "title": "课程详情", "icon": "", "closeTab": 1, "openNewPage": 2, "params": null, "btns": [], "children": null}]}, {"id": 18, "uuid": "04be2ee6-bc13-4381-b716-a68d949593c9", "createdAt": "", "updatedAt": "", "deletedAt": "", "platformId": 1, "endpointId": 1, "parentId": 17, "parentTitle": "", "path": "/course-train/train-record", "name": "TrainRecord", "hidden": 2, "component": "@/views/course-module/train-record/index.vue", "sortNum": 0, "keepAlive": 1, "title": "培训记录", "icon": "", "closeTab": 2, "openNewPage": 2, "params": null, "btns": [], "children": null}]}, {"id": 46, "uuid": "d8ee19a1-c89a-419e-b26d-004521bcd25a", "createdAt": "", "updatedAt": "", "deletedAt": "", "platformId": 1, "endpointId": 1, "parentId": 0, "parentTitle": "", "path": "/ship-report/ship-report", "name": "shipReport", "hidden": 2, "component": "@/views/report-module/daliy-paper/index.vue", "sortNum": 87, "keepAlive": 2, "title": "船舶日报", "icon": "ship-report", "closeTab": 2, "openNewPage": 2, "params": null, "btns": [{"id": 798, "createdAt": "", "updatedAt": "", "menuId": 46, "key": "汇报详情", "api": {"id": 166, "uuid": "af1cfe97-e1c8-4201-936d-94a7d16d6271", "createdAt": "2023-03-22 13:42:59", "updatedAt": "2023-04-18 10:19:30", "deletedAt": "", "path": "/ship-api/v1/daily/detail", "description": "日报详情", "apiGroup": "ship/report", "method": "GET", "necessary": 2}}, {"id": 799, "createdAt": "", "updatedAt": "", "menuId": 46, "key": "汇报导出", "api": {"id": 16, "uuid": "8187fd84-f169-4b9f-b418-414b9e645709", "createdAt": "2022-12-28 14:03:24", "updatedAt": "2023-08-11 17:41:57", "deletedAt": "", "path": "/", "description": "权限控制用，无对应接口", "apiGroup": "", "method": "POST", "necessary": 2}}, {"id": 800, "createdAt": "", "updatedAt": "", "menuId": 46, "key": "日报列表", "api": {"id": 167, "uuid": "d891eb0c-46e5-40fe-8878-f618d64c39f7", "createdAt": "2023-03-22 13:43:16", "updatedAt": "2023-04-18 10:19:30", "deletedAt": "", "path": "/ship-api/v1/daily/list/{offset}/{length}", "description": "日报列表", "apiGroup": "ship/report", "method": "POST", "necessary": 2}}, {"id": 801, "createdAt": "", "updatedAt": "", "menuId": 46, "key": "日报统计", "api": {"id": 174, "uuid": "0b3677d4-f90e-4115-af32-91b7af493684", "createdAt": "2023-03-22 13:46:23", "updatedAt": "2023-04-18 10:19:30", "deletedAt": "", "path": "/ship-api/v1/daily/stat", "description": "日报统计", "apiGroup": "ship/report", "method": "GET", "necessary": 2}}, {"id": 802, "createdAt": "", "updatedAt": "", "menuId": 46, "key": "汇报编辑", "api": {"id": 175, "uuid": "52e24199-1425-44da-b565-ba1da8411a5b", "createdAt": "2023-03-22 13:46:49", "updatedAt": "2023-04-18 10:19:30", "deletedAt": "", "path": "/ship-api/v1/daily/update", "description": "日报更新（或补填）", "apiGroup": "ship/report", "method": "POST", "necessary": 2}}, {"id": 803, "createdAt": "", "updatedAt": "", "menuId": 46, "key": "汇报补填", "api": {"id": 175, "uuid": "52e24199-1425-44da-b565-ba1da8411a5b", "createdAt": "2023-03-22 13:46:49", "updatedAt": "2023-04-18 10:19:30", "deletedAt": "", "path": "/ship-api/v1/daily/update", "description": "日报更新（或补填）", "apiGroup": "ship/report", "method": "POST", "necessary": 2}}, {"id": 804, "createdAt": "", "updatedAt": "", "menuId": 46, "key": "汇报设置", "api": {"id": 169, "uuid": "27b0e3a8-b4c3-4661-b38f-f9151301eb03", "createdAt": "2023-03-22 13:44:04", "updatedAt": "2023-04-18 10:19:30", "deletedAt": "", "path": "/ship-api/v1/daily/options/detail", "description": "汇报设置详情", "apiGroup": "ship/report", "method": "GET", "necessary": 2}}], "children": null}, {"id": 5, "uuid": "b2dfd9fd-17c2-462c-8b6d-3dbe0555edb1", "createdAt": "", "updatedAt": "", "deletedAt": "", "platformId": 1, "endpointId": 1, "parentId": 0, "parentTitle": "", "path": "/sailor-mgr/sailor-mgr", "name": "SailorMgr", "hidden": 2, "component": "@/views/sailor-module/sailor-mgr/index.vue", "sortNum": 80, "keepAlive": 2, "title": "船员管理", "icon": "sailor", "closeTab": 2, "openNewPage": 2, "params": null, "btns": null, "children": [{"id": 6, "uuid": "fb7eb229-eb59-4586-b8b8-b3d6fc510b84", "createdAt": "", "updatedAt": "", "deletedAt": "", "platformId": 1, "endpointId": 1, "parentId": 5, "parentTitle": "", "path": "/sailor-mgr/sailor-mgr", "name": "SailorMgr", "hidden": 2, "component": "@/views/sailor-module/sailor-mgr/index.vue", "sortNum": 100, "keepAlive": 2, "title": "船员库", "icon": "", "closeTab": 2, "openNewPage": 2, "params": null, "btns": [{"id": 128, "createdAt": "", "updatedAt": "", "menuId": 6, "key": "船员库", "api": {"id": 94, "uuid": "4cf8dc82-58dd-40fe-a38e-42d54a7d3761", "createdAt": "2023-02-20 13:58:23", "updatedAt": "2023-08-10 17:09:00", "deletedAt": "", "path": "/user-api/v1/shipman/list/{offset}/{length}", "description": "船员列表（船员库）", "apiGroup": "user/shipman", "method": "POST", "necessary": 2}}, {"id": 129, "createdAt": "", "updatedAt": "", "menuId": 6, "key": "船员库统计", "api": {"id": 97, "uuid": "13a3f292-3ef6-4ef8-b9cd-dfdb8d15bf73", "createdAt": "2023-02-20 13:59:04", "updatedAt": "2023-08-02 14:06:12", "deletedAt": "", "path": "/user-api/v1/shipman/statisic", "description": "船员库相关统计", "apiGroup": "user/shipman", "method": "GET", "necessary": 2}}, {"id": 130, "createdAt": "", "updatedAt": "", "menuId": 6, "key": "船员档案", "api": {"id": 24, "uuid": "d62a1108-1eaf-42f5-8c2c-7160f1ce7bdf", "createdAt": "2022-12-28 14:10:53", "updatedAt": "2023-08-02 14:06:12", "deletedAt": "", "path": "/user-api/v1/user/corp/detail", "description": "成员详情", "apiGroup": "user/user", "method": "GET", "necessary": 2}}, {"id": 144, "createdAt": "", "updatedAt": "", "menuId": 6, "key": "上船", "api": {"id": 108, "uuid": "cf141657-4a78-4e30-b9ff-0400c7c3b647", "createdAt": "2023-02-20 14:39:52", "updatedAt": "2023-08-10 17:09:00", "deletedAt": "", "path": "/ship-api/v1/shipman/on/ship", "description": "船员上船", "apiGroup": "ship/shipman", "method": "POST", "necessary": 2}}, {"id": 145, "createdAt": "", "updatedAt": "", "menuId": 6, "key": "下船", "api": {"id": 107, "uuid": "2a07a759-2690-4e09-ba58-b4171730df0f", "createdAt": "2023-02-20 14:39:34", "updatedAt": "2023-08-10 17:09:00", "deletedAt": "", "path": "/ship-api/v1/shipman/off/ship", "description": "船员下船", "apiGroup": "ship/shipman", "method": "POST", "necessary": 2}}, {"id": 1021, "createdAt": "", "updatedAt": "", "menuId": 6, "key": "船员添加", "api": {"id": 25, "uuid": "b0dd7467-057b-47f9-8d7e-1ec9d25610e4", "createdAt": "2022-12-28 14:11:32", "updatedAt": "2023-08-02 14:06:12", "deletedAt": "", "path": "/user-api/v1/user/corp/new", "description": "成员新建", "apiGroup": "user/user", "method": "POST", "necessary": 2}}, {"id": 1027, "createdAt": "", "updatedAt": "", "menuId": 6, "key": "船员编辑", "api": {"id": 27, "uuid": "4f8c4d18-b93e-4b86-b01f-9354944d26ce", "createdAt": "2022-12-28 14:12:51", "updatedAt": "2023-08-02 14:06:12", "deletedAt": "", "path": "/user-api/v1/user/corp/update", "description": "成员更新", "apiGroup": "user/user", "method": "POST", "necessary": 2}}, {"id": 1776, "createdAt": "", "updatedAt": "", "menuId": 6, "key": "数据校验", "api": {"id": 262, "uuid": "f7a8007f-5088-4c0c-bdc1-a6e31f571bf2", "createdAt": "2023-06-01 09:21:02", "updatedAt": "2023-08-11 17:28:23", "deletedAt": "", "path": "/power-api/v1/imports/validate", "description": "校验数据\n", "apiGroup": "power/import", "method": "POST", "necessary": 2}}, {"id": 1777, "createdAt": "", "updatedAt": "", "menuId": 6, "key": "校验列表", "api": {"id": 263, "uuid": "8ac79724-131e-42e4-8ead-0fdf79c516b9", "createdAt": "2023-06-01 09:21:35", "updatedAt": "2023-08-11 17:28:23", "deletedAt": "", "path": "/power-api/v1/imports/validate/list/{offset}/{length}", "description": "校验列表", "apiGroup": "power/import", "method": "POST", "necessary": 2}}, {"id": 1778, "createdAt": "", "updatedAt": "", "menuId": 6, "key": "开始导入", "api": {"id": 264, "uuid": "bec6fb51-6383-4ad1-a655-81de80de272e", "createdAt": "2023-06-01 09:22:04", "updatedAt": "2023-08-11 17:28:23", "deletedAt": "", "path": "/power-api/v1/imports/exec", "description": "开始导入", "apiGroup": "power/import", "method": "POST", "necessary": 2}}, {"id": 1779, "createdAt": "", "updatedAt": "", "menuId": 6, "key": "导入确认完成", "api": {"id": 265, "uuid": "b2d289bf-4e6f-488d-86c5-8415d508d616", "createdAt": "2023-06-01 09:23:16", "updatedAt": "2023-08-11 17:28:23", "deletedAt": "", "path": "/power-api/v1/imports/complete", "description": "导入确认完成", "apiGroup": "power/import", "method": "POST", "necessary": 2}}, {"id": 2053, "createdAt": "", "updatedAt": "", "menuId": 6, "key": "导入结果列表", "api": {"id": 283, "uuid": "3a252f85-5894-41ad-ac30-dcadb5be4b3f", "createdAt": "2023-06-06 15:54:17", "updatedAt": "2023-08-11 08:49:30", "deletedAt": "", "path": "/power-api/v1/imports/import/result/list/{offset}/{length}", "description": "导入结果列表", "apiGroup": "power/import", "method": "POST", "necessary": 2}}, {"id": 2156, "createdAt": "", "updatedAt": "", "menuId": 6, "key": "船员简要信息", "api": {"id": 297, "uuid": "0457f16c-f85a-413c-9bfe-b216cedf34f5", "createdAt": "2023-06-29 09:56:29", "updatedAt": "2023-08-02 14:06:12", "deletedAt": "", "path": "/user-api/v1/shipman/brief", "description": "船员简要信息\n", "apiGroup": "suer/shipman", "method": "POST", "necessary": 2}}, {"id": 2157, "createdAt": "", "updatedAt": "", "menuId": 6, "key": "船员简要信息列表", "api": {"id": 298, "uuid": "e0eb1047-cd41-4453-bdc9-d7ebd9802890", "createdAt": "2023-06-29 09:56:56", "updatedAt": "2023-08-02 14:06:12", "deletedAt": "", "path": "/user-api/v1/shipman/brief/list", "description": "船员简要信息列表\n", "apiGroup": "user/shipman", "method": "POST", "necessary": 2}}, {"id": 2158, "createdAt": "", "updatedAt": "", "menuId": 6, "key": "船舶列表 ", "api": {"id": 303, "uuid": "f6c0e2a9-21e0-4a92-99b7-103f30eba32a", "createdAt": "2023-06-29 10:03:32", "updatedAt": "2023-08-02 14:06:12", "deletedAt": "", "path": "/ship-api/v1/ship/user/brief/list", "description": "船舶列表 (船员库)\n", "apiGroup": "ship/ship", "method": "POST", "necessary": 2}}, {"id": 2159, "createdAt": "", "updatedAt": "", "menuId": 6, "key": "船员下拉列表", "api": {"id": 296, "uuid": "207dd2b8-30d2-43c9-81ed-24d2ecaafa13", "createdAt": "2023-06-29 09:53:54", "updatedAt": "2023-08-11 08:49:30", "deletedAt": "", "path": "/user-api/v1/shipman/search/list", "description": "船员下拉列表\n", "apiGroup": "ship/shipman", "method": "POST", "necessary": 2}}, {"id": 2160, "createdAt": "", "updatedAt": "", "menuId": 6, "key": "证书查询", "api": {"id": 299, "uuid": "a580d769-d1f6-492e-922b-8a77fb8ca332", "createdAt": "2023-06-29 09:59:24", "updatedAt": "2023-08-11 17:28:23", "deletedAt": "", "path": "/user-api/v1/shipman/cert/msa", "description": "海事局船员证书查询\n", "apiGroup": "user/shipman_cert", "method": "GET", "necessary": 2}}, {"id": 2161, "createdAt": "", "updatedAt": "", "menuId": 6, "key": "证书校验", "api": {"id": 300, "uuid": "d25cffac-cc1b-44cd-880e-31fdead10a21", "createdAt": "2023-06-29 10:00:13", "updatedAt": "2023-08-11 17:28:23", "deletedAt": "", "path": "/user-api/v1/shipman/cert/validate", "description": "船员证书校验\n", "apiGroup": "suer/shipman_cert", "method": "POST", "necessary": 2}}, {"id": 2162, "createdAt": "", "updatedAt": "", "menuId": 6, "key": "证书导入", "api": {"id": 301, "uuid": "c1c851d4-c818-400e-94a4-98fb557e8aae", "createdAt": "2023-06-29 10:02:42", "updatedAt": "2023-08-11 17:28:23", "deletedAt": "", "path": "/user-api/v1/shipman/cert/import", "description": "船员证书导入\n", "apiGroup": "user/shipman_cert", "method": "POST", "necessary": 2}}, {"id": 2223, "createdAt": "", "updatedAt": "", "menuId": 6, "key": "证书列表", "api": {"id": 88, "uuid": "d49b1458-254a-4e77-ac47-21539e4f0e69", "createdAt": "2023-02-20 13:53:00", "updatedAt": "2023-08-02 14:06:12", "deletedAt": "", "path": "/user-api/v1/shipman/cert/all", "description": "单个船员所有证书", "apiGroup": "user/shipman_cert", "method": "GET", "necessary": 2}}, {"id": 2224, "createdAt": "", "updatedAt": "", "menuId": 6, "key": "保险列表", "api": {"id": 99, "uuid": "1ec3fcb3-23c6-498e-8d41-18694d683f42", "createdAt": "2023-02-20 14:00:07", "updatedAt": "2023-08-02 14:06:12", "deletedAt": "", "path": "/user-api/v1/shipman/insurance/all", "description": "单个船员所有保险", "apiGroup": "user/shipman_insurance", "method": "GET", "necessary": 2}}, {"id": 2261, "createdAt": "", "updatedAt": "", "menuId": 6, "key": "证书新增", "api": {"id": 91, "uuid": "8236a6b6-bada-4b88-bfb4-5034a8b225d3", "createdAt": "2023-02-20 13:53:56", "updatedAt": "2023-08-11 17:28:23", "deletedAt": "", "path": "/user-api/v1/shipman/cert/new", "description": "船员证书新建\n", "apiGroup": "user/shipman_cert", "method": "POST", "necessary": 2}}, {"id": 2262, "createdAt": "", "updatedAt": "", "menuId": 6, "key": "证书详情", "api": {"id": 86, "uuid": "c28cc403-1aa3-46e1-8cd9-70e1f8c55a75", "createdAt": "2023-02-20 13:50:30", "updatedAt": "2023-08-11 17:28:23", "deletedAt": "", "path": "/user-api/v1/shipman/cert/detail", "description": "船员证书详情", "apiGroup": "user/shipman_cert", "method": "GET", "necessary": 2}}, {"id": 2263, "createdAt": "", "updatedAt": "", "menuId": 6, "key": "证书删除", "api": {"id": 87, "uuid": "14a383ee-de94-4d1f-b7cd-19975fd83e41", "createdAt": "2023-02-20 13:50:51", "updatedAt": "2023-08-11 17:28:23", "deletedAt": "", "path": "/user-api/v1/shipman/cert/del", "description": "船员证书删除", "apiGroup": "user/shipman_cert", "method": "POST", "necessary": 2}}, {"id": 2264, "createdAt": "", "updatedAt": "", "menuId": 6, "key": "保险新增", "api": {"id": 102, "uuid": "c7ff2a68-f9c1-446f-8dfa-a1ee9f4a5827", "createdAt": "2023-02-20 14:00:54", "updatedAt": "2023-08-02 14:06:12", "deletedAt": "", "path": "/user-api/v1/shipman/insurance/new", "description": "船员保险新建", "apiGroup": "user/shipman_insurance", "method": "POST", "necessary": 2}}, {"id": 2265, "createdAt": "", "updatedAt": "", "menuId": 6, "key": "保险详情", "api": {"id": 100, "uuid": "2c468b9d-4a69-4522-b4b7-62be7e1bbc29", "createdAt": "2023-02-20 14:00:20", "updatedAt": "2023-08-02 14:06:12", "deletedAt": "", "path": "/user-api/v1/shipman/insurance/detail", "description": "船员保险详情", "apiGroup": "user/shipman_insurance", "method": "GET", "necessary": 2}}, {"id": 2266, "createdAt": "", "updatedAt": "", "menuId": 6, "key": "保险删除", "api": {"id": 98, "uuid": "766e9850-f500-4ce7-b5f6-ea237198e5e4", "createdAt": "2023-02-20 13:59:53", "updatedAt": "2023-08-02 14:06:12", "deletedAt": "", "path": "/user-api/v1/shipman/insurance/del", "description": "船员保险删除\n", "apiGroup": "user/shipman_insurance", "method": "GET", "necessary": 2}}, {"id": 2267, "createdAt": "", "updatedAt": "", "menuId": 6, "key": "变更记录", "api": {"id": 106, "uuid": "15eec463-161e-4f66-9b3a-0de4c9817544", "createdAt": "2023-02-20 14:04:55", "updatedAt": "2023-08-11 17:28:23", "deletedAt": "", "path": "/user-api/v1/user/operate/record/{offset}/{length}", "description": "用户相关更新记录列表\n", "apiGroup": "user/user", "method": "POST", "necessary": 2}}, {"id": 2268, "createdAt": "", "updatedAt": "", "menuId": 6, "key": "证书编辑", "api": {"id": 93, "uuid": "961ca34e-e5d4-49a5-8ec0-3ae9d93f7944", "createdAt": "2023-02-20 13:57:17", "updatedAt": "2023-08-11 17:28:23", "deletedAt": "", "path": "/user-api/v1/shipman/cert/update", "description": "船员证书更新\n", "apiGroup": "user/shipman_cert", "method": "POST", "necessary": 2}}, {"id": 2269, "createdAt": "", "updatedAt": "", "menuId": 6, "key": "保险编辑", "api": {"id": 104, "uuid": "2a221f0d-8aae-4601-a4fc-f9f09d4dd11e", "createdAt": "2023-02-20 14:01:27", "updatedAt": "2023-08-02 14:06:12", "deletedAt": "", "path": "/user-api/v1/shipman/insurance/update", "description": "船员保险更新", "apiGroup": "user/shipman_insurance", "method": "POST", "necessary": 2}}, {"id": 2270, "createdAt": "", "updatedAt": "", "menuId": 6, "key": "证书续期", "api": {"id": 267, "uuid": "cbc0c482-52c2-45b2-8ef7-06e32afd4d6d", "createdAt": "2023-06-01 09:26:16", "updatedAt": "2023-08-11 17:28:23", "deletedAt": "", "path": "/user-api/v1/shipman/cert/renewal", "description": "船员证书续期", "apiGroup": "user/shipman_cert", "method": "POST", "necessary": 2}}, {"id": 2271, "createdAt": "", "updatedAt": "", "menuId": 6, "key": "证书续期详情", "api": {"id": 268, "uuid": "17350d96-5785-4242-b5ed-fc4a5a4bf22d", "createdAt": "2023-06-01 09:27:12", "updatedAt": "2023-08-11 17:28:23", "deletedAt": "", "path": "/user-api/v1/shipman/cert/renewal/detail", "description": "船员证书续期详情\n", "apiGroup": "user/shipman_cert", "method": "GET", "necessary": 2}}, {"id": 2272, "createdAt": "", "updatedAt": "", "menuId": 6, "key": "保险续期", "api": {"id": 269, "uuid": "ae35240d-643b-4f04-bd50-4862fd024644", "createdAt": "2023-06-01 09:28:11", "updatedAt": "2023-08-02 14:06:12", "deletedAt": "", "path": "/user-api/v1/shipman/insurance/renewal", "description": "船员保险续期\n", "apiGroup": "user/shipman_insurance", "method": "POST", "necessary": 2}}, {"id": 2273, "createdAt": "", "updatedAt": "", "menuId": 6, "key": "保险续期详情", "api": {"id": 270, "uuid": "81acecf5-af46-4ef6-baea-e2c53c311dce", "createdAt": "2023-06-01 09:29:00", "updatedAt": "2023-08-10 17:09:00", "deletedAt": "", "path": "/user-api/v1/shipman/insurance/renewal/detail", "description": "船员保险续期详情", "apiGroup": "user/shipman_insurance", "method": "GET", "necessary": 2}}, {"id": 2488, "createdAt": "", "updatedAt": "", "menuId": 6, "key": "培训删除", "api": {"id": 16, "uuid": "8187fd84-f169-4b9f-b418-414b9e645709", "createdAt": "2022-12-28 14:03:24", "updatedAt": "2023-08-11 17:41:57", "deletedAt": "", "path": "/", "description": "权限控制用，无对应接口", "apiGroup": "", "method": "POST", "necessary": 2}}, {"id": 2489, "createdAt": "", "updatedAt": "", "menuId": 6, "key": "培训更新", "api": {"id": 16, "uuid": "8187fd84-f169-4b9f-b418-414b9e645709", "createdAt": "2022-12-28 14:03:24", "updatedAt": "2023-08-11 17:41:57", "deletedAt": "", "path": "/", "description": "权限控制用，无对应接口", "apiGroup": "", "method": "POST", "necessary": 2}}], "children": [{"id": 7, "uuid": "b9b7e37c-0952-45e3-9c22-f67a5f40e3ca", "createdAt": "", "updatedAt": "", "deletedAt": "", "platformId": 1, "endpointId": 1, "parentId": 6, "parentTitle": "", "path": "/sailor-mgr/sailor-detail", "name": "SailorDetail", "hidden": 1, "component": "@/views/sailor-module/sailor-mgr/sailor-detail/index.vue", "sortNum": 0, "keepAlive": 2, "title": "船员档案", "icon": "", "closeTab": 1, "openNewPage": 2, "params": null, "btns": [{"id": 160, "createdAt": "", "updatedAt": "", "menuId": 7, "key": "船员档案", "api": {"id": 24, "uuid": "d62a1108-1eaf-42f5-8c2c-7160f1ce7bdf", "createdAt": "2022-12-28 14:10:53", "updatedAt": "2023-08-02 14:06:12", "deletedAt": "", "path": "/user-api/v1/user/corp/detail", "description": "成员详情", "apiGroup": "user/user", "method": "GET", "necessary": 2}}, {"id": 170, "createdAt": "", "updatedAt": "", "menuId": 7, "key": "保险删除", "api": {"id": 98, "uuid": "766e9850-f500-4ce7-b5f6-ea237198e5e4", "createdAt": "2023-02-20 13:59:53", "updatedAt": "2023-08-02 14:06:12", "deletedAt": "", "path": "/user-api/v1/shipman/insurance/del", "description": "船员保险删除\n", "apiGroup": "user/shipman_insurance", "method": "GET", "necessary": 2}}, {"id": 171, "createdAt": "", "updatedAt": "", "menuId": 7, "key": "变更记录", "api": {"id": 106, "uuid": "15eec463-161e-4f66-9b3a-0de4c9817544", "createdAt": "2023-02-20 14:04:55", "updatedAt": "2023-08-11 17:28:23", "deletedAt": "", "path": "/user-api/v1/user/operate/record/{offset}/{length}", "description": "用户相关更新记录列表\n", "apiGroup": "user/user", "method": "POST", "necessary": 2}}, {"id": 166, "createdAt": "", "updatedAt": "", "menuId": 7, "key": "保险列表", "api": {"id": 99, "uuid": "1ec3fcb3-23c6-498e-8d41-18694d683f42", "createdAt": "2023-02-20 14:00:07", "updatedAt": "2023-08-02 14:06:12", "deletedAt": "", "path": "/user-api/v1/shipman/insurance/all", "description": "单个船员所有保险", "apiGroup": "user/shipman_insurance", "method": "GET", "necessary": 2}}, {"id": 161, "createdAt": "", "updatedAt": "", "menuId": 7, "key": "证书列表", "api": {"id": 88, "uuid": "d49b1458-254a-4e77-ac47-21539e4f0e69", "createdAt": "2023-02-20 13:53:00", "updatedAt": "2023-08-02 14:06:12", "deletedAt": "", "path": "/user-api/v1/shipman/cert/all", "description": "单个船员所有证书", "apiGroup": "user/shipman_cert", "method": "GET", "necessary": 2}}, {"id": 162, "createdAt": "", "updatedAt": "", "menuId": 7, "key": "证书新增", "api": {"id": 91, "uuid": "8236a6b6-bada-4b88-bfb4-5034a8b225d3", "createdAt": "2023-02-20 13:53:56", "updatedAt": "2023-08-11 17:28:23", "deletedAt": "", "path": "/user-api/v1/shipman/cert/new", "description": "船员证书新建\n", "apiGroup": "user/shipman_cert", "method": "POST", "necessary": 2}}, {"id": 167, "createdAt": "", "updatedAt": "", "menuId": 7, "key": "保险新增", "api": {"id": 102, "uuid": "c7ff2a68-f9c1-446f-8dfa-a1ee9f4a5827", "createdAt": "2023-02-20 14:00:54", "updatedAt": "2023-08-02 14:06:12", "deletedAt": "", "path": "/user-api/v1/shipman/insurance/new", "description": "船员保险新建", "apiGroup": "user/shipman_insurance", "method": "POST", "necessary": 2}}, {"id": 164, "createdAt": "", "updatedAt": "", "menuId": 7, "key": "证书详情", "api": {"id": 86, "uuid": "c28cc403-1aa3-46e1-8cd9-70e1f8c55a75", "createdAt": "2023-02-20 13:50:30", "updatedAt": "2023-08-11 17:28:23", "deletedAt": "", "path": "/user-api/v1/shipman/cert/detail", "description": "船员证书详情", "apiGroup": "user/shipman_cert", "method": "GET", "necessary": 2}}, {"id": 169, "createdAt": "", "updatedAt": "", "menuId": 7, "key": "保险详情", "api": {"id": 100, "uuid": "2c468b9d-4a69-4522-b4b7-62be7e1bbc29", "createdAt": "2023-02-20 14:00:20", "updatedAt": "2023-08-02 14:06:12", "deletedAt": "", "path": "/user-api/v1/shipman/insurance/detail", "description": "船员保险详情", "apiGroup": "user/shipman_insurance", "method": "GET", "necessary": 2}}, {"id": 165, "createdAt": "", "updatedAt": "", "menuId": 7, "key": "证书删除", "api": {"id": 87, "uuid": "14a383ee-de94-4d1f-b7cd-19975fd83e41", "createdAt": "2023-02-20 13:50:51", "updatedAt": "2023-08-11 17:28:23", "deletedAt": "", "path": "/user-api/v1/shipman/cert/del", "description": "船员证书删除", "apiGroup": "user/shipman_cert", "method": "POST", "necessary": 2}}, {"id": 205, "createdAt": "", "updatedAt": "", "menuId": 7, "key": "保险编辑", "api": {"id": 104, "uuid": "2a221f0d-8aae-4601-a4fc-f9f09d4dd11e", "createdAt": "2023-02-20 14:01:27", "updatedAt": "2023-08-02 14:06:12", "deletedAt": "", "path": "/user-api/v1/shipman/insurance/update", "description": "船员保险更新", "apiGroup": "user/shipman_insurance", "method": "POST", "necessary": 2}}, {"id": 204, "createdAt": "", "updatedAt": "", "menuId": 7, "key": "证书编辑", "api": {"id": 93, "uuid": "961ca34e-e5d4-49a5-8ec0-3ae9d93f7944", "createdAt": "2023-02-20 13:57:17", "updatedAt": "2023-08-11 17:28:23", "deletedAt": "", "path": "/user-api/v1/shipman/cert/update", "description": "船员证书更新\n", "apiGroup": "user/shipman_cert", "method": "POST", "necessary": 2}}, {"id": 1096, "createdAt": "", "updatedAt": "", "menuId": 7, "key": "证书保险", "api": {"id": 16, "uuid": "8187fd84-f169-4b9f-b418-414b9e645709", "createdAt": "2022-12-28 14:03:24", "updatedAt": "2023-08-11 17:41:57", "deletedAt": "", "path": "/", "description": "权限控制用，无对应接口", "apiGroup": "", "method": "POST", "necessary": 2}}, {"id": 1815, "createdAt": "", "updatedAt": "", "menuId": 7, "key": "保险续期", "api": {"id": 269, "uuid": "ae35240d-643b-4f04-bd50-4862fd024644", "createdAt": "2023-06-01 09:28:11", "updatedAt": "2023-08-02 14:06:12", "deletedAt": "", "path": "/user-api/v1/shipman/insurance/renewal", "description": "船员保险续期\n", "apiGroup": "user/shipman_insurance", "method": "POST", "necessary": 2}}, {"id": 1814, "createdAt": "", "updatedAt": "", "menuId": 7, "key": "证书续期详情", "api": {"id": 268, "uuid": "17350d96-5785-4242-b5ed-fc4a5a4bf22d", "createdAt": "2023-06-01 09:27:12", "updatedAt": "2023-08-11 17:28:23", "deletedAt": "", "path": "/user-api/v1/shipman/cert/renewal/detail", "description": "船员证书续期详情\n", "apiGroup": "user/shipman_cert", "method": "GET", "necessary": 2}}, {"id": 1813, "createdAt": "", "updatedAt": "", "menuId": 7, "key": "证书续期", "api": {"id": 267, "uuid": "cbc0c482-52c2-45b2-8ef7-06e32afd4d6d", "createdAt": "2023-06-01 09:26:16", "updatedAt": "2023-08-11 17:28:23", "deletedAt": "", "path": "/user-api/v1/shipman/cert/renewal", "description": "船员证书续期", "apiGroup": "user/shipman_cert", "method": "POST", "necessary": 2}}, {"id": 1816, "createdAt": "", "updatedAt": "", "menuId": 7, "key": "保险续期详情", "api": {"id": 270, "uuid": "81acecf5-af46-4ef6-baea-e2c53c311dce", "createdAt": "2023-06-01 09:29:00", "updatedAt": "2023-08-10 17:09:00", "deletedAt": "", "path": "/user-api/v1/shipman/insurance/renewal/detail", "description": "船员保险续期详情", "apiGroup": "user/shipman_insurance", "method": "GET", "necessary": 2}}, {"id": 2175, "createdAt": "", "updatedAt": "", "menuId": 7, "key": "证书查询", "api": {"id": 299, "uuid": "a580d769-d1f6-492e-922b-8a77fb8ca332", "createdAt": "2023-06-29 09:59:24", "updatedAt": "2023-08-11 17:28:23", "deletedAt": "", "path": "/user-api/v1/shipman/cert/msa", "description": "海事局船员证书查询\n", "apiGroup": "user/shipman_cert", "method": "GET", "necessary": 2}}, {"id": 2176, "createdAt": "", "updatedAt": "", "menuId": 7, "key": "证书校验", "api": {"id": 300, "uuid": "d25cffac-cc1b-44cd-880e-31fdead10a21", "createdAt": "2023-06-29 10:00:13", "updatedAt": "2023-08-11 17:28:23", "deletedAt": "", "path": "/user-api/v1/shipman/cert/validate", "description": "船员证书校验\n", "apiGroup": "suer/shipman_cert", "method": "POST", "necessary": 2}}, {"id": 2177, "createdAt": "", "updatedAt": "", "menuId": 7, "key": "证书导入", "api": {"id": 301, "uuid": "c1c851d4-c818-400e-94a4-98fb557e8aae", "createdAt": "2023-06-29 10:02:42", "updatedAt": "2023-08-11 17:28:23", "deletedAt": "", "path": "/user-api/v1/shipman/cert/import", "description": "船员证书导入\n", "apiGroup": "user/shipman_cert", "method": "POST", "necessary": 2}}, {"id": 2466, "createdAt": "", "updatedAt": "", "menuId": 7, "key": "培训删除", "api": {"id": 16, "uuid": "8187fd84-f169-4b9f-b418-414b9e645709", "createdAt": "2022-12-28 14:03:24", "updatedAt": "2023-08-11 17:41:57", "deletedAt": "", "path": "/", "description": "权限控制用，无对应接口", "apiGroup": "", "method": "POST", "necessary": 2}}, {"id": 2467, "createdAt": "", "updatedAt": "", "menuId": 7, "key": "培训更新", "api": {"id": 16, "uuid": "8187fd84-f169-4b9f-b418-414b9e645709", "createdAt": "2022-12-28 14:03:24", "updatedAt": "2023-08-11 17:41:57", "deletedAt": "", "path": "/", "description": "权限控制用，无对应接口", "apiGroup": "", "method": "POST", "necessary": 2}}], "children": null}, {"id": 8, "uuid": "b9b7e37c-0952-45e3-9c22-f67a5f40e3cq", "createdAt": "", "updatedAt": "", "deletedAt": "", "platformId": 1, "endpointId": 1, "parentId": 6, "parentTitle": "", "path": "/sailor-mgr/operate-history", "name": "SailorOperateHistory", "hidden": 1, "component": "@/views/sailor-module/sailor-mgr/operate-history/index.vue", "sortNum": 0, "keepAlive": 2, "title": "上下船记录", "icon": "", "closeTab": 1, "openNewPage": 2, "params": null, "btns": [], "children": null}]}, {"id": 8, "uuid": "c0b81432-aeb5-46ec-a764-27f7777f1873", "createdAt": "", "updatedAt": "", "deletedAt": "", "platformId": 1, "endpointId": 1, "parentId": 5, "parentTitle": "", "path": "/sailor-mgr/sailor-cert", "name": "<PERSON><PERSON><PERSON>", "hidden": 2, "component": "@/views/sailor-module/sailor-cert/index.vue", "sortNum": 0, "keepAlive": 2, "title": "船员证书", "icon": "", "closeTab": 2, "openNewPage": 2, "params": null, "btns": [{"id": 172, "createdAt": "", "updatedAt": "", "menuId": 8, "key": "证书列表", "api": {"id": 90, "uuid": "6a1c293b-7202-44fb-a93d-238b6eb7d403", "createdAt": "2023-02-20 13:53:32", "updatedAt": "2023-08-11 17:28:23", "deletedAt": "", "path": "/user-api/v1/shipman/cert/list/{offset}/{length}", "description": "船员证书列表\n", "apiGroup": "user/shipman_cert", "method": "POST", "necessary": 2}}, {"id": 173, "createdAt": "", "updatedAt": "", "menuId": 8, "key": "船员证书统计", "api": {"id": 92, "uuid": "d902d729-d9c6-463a-a0e6-c7702ec2d9d6", "createdAt": "2023-02-20 13:54:10", "updatedAt": "2023-08-11 17:41:57", "deletedAt": "", "path": "/user-api/v1/shipman/cert/statisic", "description": "船员证书统计\n", "apiGroup": "user/shipman_cert", "method": "GET", "necessary": 2}}, {"id": 178, "createdAt": "", "updatedAt": "", "menuId": 8, "key": "变更记录", "api": {"id": 106, "uuid": "15eec463-161e-4f66-9b3a-0de4c9817544", "createdAt": "2023-02-20 14:04:55", "updatedAt": "2023-08-11 17:28:23", "deletedAt": "", "path": "/user-api/v1/user/operate/record/{offset}/{length}", "description": "用户相关更新记录列表\n", "apiGroup": "user/user", "method": "POST", "necessary": 2}}, {"id": 174, "createdAt": "", "updatedAt": "", "menuId": 8, "key": "证书新增", "api": {"id": 91, "uuid": "8236a6b6-bada-4b88-bfb4-5034a8b225d3", "createdAt": "2023-02-20 13:53:56", "updatedAt": "2023-08-11 17:28:23", "deletedAt": "", "path": "/user-api/v1/shipman/cert/new", "description": "船员证书新建\n", "apiGroup": "user/shipman_cert", "method": "POST", "necessary": 2}}, {"id": 176, "createdAt": "", "updatedAt": "", "menuId": 8, "key": "证书详情", "api": {"id": 86, "uuid": "c28cc403-1aa3-46e1-8cd9-70e1f8c55a75", "createdAt": "2023-02-20 13:50:30", "updatedAt": "2023-08-11 17:28:23", "deletedAt": "", "path": "/user-api/v1/shipman/cert/detail", "description": "船员证书详情", "apiGroup": "user/shipman_cert", "method": "GET", "necessary": 2}}, {"id": 177, "createdAt": "", "updatedAt": "", "menuId": 8, "key": "证书删除", "api": {"id": 87, "uuid": "14a383ee-de94-4d1f-b7cd-19975fd83e41", "createdAt": "2023-02-20 13:50:51", "updatedAt": "2023-08-11 17:28:23", "deletedAt": "", "path": "/user-api/v1/shipman/cert/del", "description": "船员证书删除", "apiGroup": "user/shipman_cert", "method": "POST", "necessary": 2}}, {"id": 196, "createdAt": "", "updatedAt": "", "menuId": 8, "key": "证书编辑", "api": {"id": 93, "uuid": "961ca34e-e5d4-49a5-8ec0-3ae9d93f7944", "createdAt": "2023-02-20 13:57:17", "updatedAt": "2023-08-11 17:28:23", "deletedAt": "", "path": "/user-api/v1/shipman/cert/update", "description": "船员证书更新\n", "apiGroup": "user/shipman_cert", "method": "POST", "necessary": 2}}, {"id": 1742, "createdAt": "", "updatedAt": "", "menuId": 8, "key": "证书续期", "api": {"id": 267, "uuid": "cbc0c482-52c2-45b2-8ef7-06e32afd4d6d", "createdAt": "2023-06-01 09:26:16", "updatedAt": "2023-08-11 17:28:23", "deletedAt": "", "path": "/user-api/v1/shipman/cert/renewal", "description": "船员证书续期", "apiGroup": "user/shipman_cert", "method": "POST", "necessary": 2}}, {"id": 1743, "createdAt": "", "updatedAt": "", "menuId": 8, "key": "证书续期详情", "api": {"id": 268, "uuid": "17350d96-5785-4242-b5ed-fc4a5a4bf22d", "createdAt": "2023-06-01 09:27:12", "updatedAt": "2023-08-11 17:28:23", "deletedAt": "", "path": "/user-api/v1/shipman/cert/renewal/detail", "description": "船员证书续期详情\n", "apiGroup": "user/shipman_cert", "method": "GET", "necessary": 2}}, {"id": 1800, "createdAt": "", "updatedAt": "", "menuId": 8, "key": "数据校验", "api": {"id": 262, "uuid": "f7a8007f-5088-4c0c-bdc1-a6e31f571bf2", "createdAt": "2023-06-01 09:21:02", "updatedAt": "2023-08-11 17:28:23", "deletedAt": "", "path": "/power-api/v1/imports/validate", "description": "校验数据\n", "apiGroup": "power/import", "method": "POST", "necessary": 2}}, {"id": 1803, "createdAt": "", "updatedAt": "", "menuId": 8, "key": "导入确认完成", "api": {"id": 265, "uuid": "b2d289bf-4e6f-488d-86c5-8415d508d616", "createdAt": "2023-06-01 09:23:16", "updatedAt": "2023-08-11 17:28:23", "deletedAt": "", "path": "/power-api/v1/imports/complete", "description": "导入确认完成", "apiGroup": "power/import", "method": "POST", "necessary": 2}}, {"id": 1802, "createdAt": "", "updatedAt": "", "menuId": 8, "key": "开始导入", "api": {"id": 264, "uuid": "bec6fb51-6383-4ad1-a655-81de80de272e", "createdAt": "2023-06-01 09:22:04", "updatedAt": "2023-08-11 17:28:23", "deletedAt": "", "path": "/power-api/v1/imports/exec", "description": "开始导入", "apiGroup": "power/import", "method": "POST", "necessary": 2}}, {"id": 1801, "createdAt": "", "updatedAt": "", "menuId": 8, "key": "校验列表", "api": {"id": 263, "uuid": "8ac79724-131e-42e4-8ead-0fdf79c516b9", "createdAt": "2023-06-01 09:21:35", "updatedAt": "2023-08-11 17:28:23", "deletedAt": "", "path": "/power-api/v1/imports/validate/list/{offset}/{length}", "description": "校验列表", "apiGroup": "power/import", "method": "POST", "necessary": 2}}, {"id": 2009, "createdAt": "", "updatedAt": "", "menuId": 8, "key": "培训更新", "api": {"id": 16, "uuid": "8187fd84-f169-4b9f-b418-414b9e645709", "createdAt": "2022-12-28 14:03:24", "updatedAt": "2023-08-11 17:41:57", "deletedAt": "", "path": "/", "description": "权限控制用，无对应接口", "apiGroup": "", "method": "POST", "necessary": 2}}, {"id": 2010, "createdAt": "", "updatedAt": "", "menuId": 8, "key": "培训删除", "api": {"id": 16, "uuid": "8187fd84-f169-4b9f-b418-414b9e645709", "createdAt": "2022-12-28 14:03:24", "updatedAt": "2023-08-11 17:41:57", "deletedAt": "", "path": "/", "description": "权限控制用，无对应接口", "apiGroup": "", "method": "POST", "necessary": 2}}, {"id": 2669, "createdAt": "", "updatedAt": "", "menuId": 8, "key": "查询船员证书", "api": {"id": 299, "uuid": "a580d769-d1f6-492e-922b-8a77fb8ca332", "createdAt": "2023-06-29 09:59:24", "updatedAt": "2023-08-11 17:28:23", "deletedAt": "", "path": "/user-api/v1/shipman/cert/msa", "description": "海事局船员证书查询\n", "apiGroup": "user/shipman_cert", "method": "GET", "necessary": 2}}, {"id": 2670, "createdAt": "", "updatedAt": "", "menuId": 8, "key": "证书校验", "api": {"id": 300, "uuid": "d25cffac-cc1b-44cd-880e-31fdead10a21", "createdAt": "2023-06-29 10:00:13", "updatedAt": "2023-08-11 17:28:23", "deletedAt": "", "path": "/user-api/v1/shipman/cert/validate", "description": "船员证书校验\n", "apiGroup": "suer/shipman_cert", "method": "POST", "necessary": 2}}, {"id": 2671, "createdAt": "", "updatedAt": "", "menuId": 8, "key": "证书导入", "api": {"id": 301, "uuid": "c1c851d4-c818-400e-94a4-98fb557e8aae", "createdAt": "2023-06-29 10:02:42", "updatedAt": "2023-08-11 17:28:23", "deletedAt": "", "path": "/user-api/v1/shipman/cert/import", "description": "船员证书导入\n", "apiGroup": "user/shipman_cert", "method": "POST", "necessary": 2}}, {"id": 2704, "createdAt": "", "updatedAt": "", "menuId": 8, "key": "成员更新身份证", "api": {"id": 320, "uuid": "2d7f1339-8f36-49ad-a0c4-91081d4976bb", "createdAt": "2023-08-11 17:27:43", "updatedAt": "2023-08-11 17:28:23", "deletedAt": "", "path": "/user-api/v1/user/corp/update/id/card", "description": "成员更新身份证", "apiGroup": "user/user", "method": "POST", "necessary": 2}}], "children": [{"id": 9, "uuid": "4b43d294-357e-441c-8876-cc0366939783", "createdAt": "", "updatedAt": "", "deletedAt": "", "platformId": 1, "endpointId": 1, "parentId": 8, "parentTitle": "", "path": "/sailor-mgr/sailor-cert-logs", "name": "SailorCertLogs", "hidden": 1, "component": "@/views/sailor-module/sailor-cert/history/index.vue", "sortNum": 0, "keepAlive": 2, "title": "船员证书变更记录", "icon": "", "closeTab": 2, "openNewPage": 2, "params": null, "btns": [{"id": 179, "createdAt": "", "updatedAt": "", "menuId": 9, "key": "变更记录", "api": {"id": 106, "uuid": "15eec463-161e-4f66-9b3a-0de4c9817544", "createdAt": "2023-02-20 14:04:55", "updatedAt": "2023-08-11 17:28:23", "deletedAt": "", "path": "/user-api/v1/user/operate/record/{offset}/{length}", "description": "用户相关更新记录列表\n", "apiGroup": "user/user", "method": "POST", "necessary": 2}}], "children": null}]}, {"id": 10, "uuid": "2ba7f180-27db-4d6d-9f02-808d819e7efa", "createdAt": "", "updatedAt": "", "deletedAt": "", "platformId": 1, "endpointId": 1, "parentId": 5, "parentTitle": "", "path": "/sailor-mgr/sailor-insurance", "name": "SailorInsurance", "hidden": 2, "component": "@/views/sailor-module/sailor-insurance/index.vue", "sortNum": 0, "keepAlive": 2, "title": "船员保险", "icon": "", "closeTab": 2, "openNewPage": 2, "params": null, "btns": [{"id": 180, "createdAt": "", "updatedAt": "", "menuId": 10, "key": "保险列表", "api": {"id": 101, "uuid": "7574552c-b001-446f-8fbf-fdff5184a38a", "createdAt": "2023-02-20 14:00:35", "updatedAt": "2023-06-01 10:45:01", "deletedAt": "", "path": "/user-api/v1/shipman/insurance/list/{offset}/{length}", "description": "船员保险列表", "apiGroup": "user/shipman_insurance", "method": "POST", "necessary": 2}}, {"id": 184, "createdAt": "", "updatedAt": "", "menuId": 10, "key": "保险删除", "api": {"id": 98, "uuid": "766e9850-f500-4ce7-b5f6-ea237198e5e4", "createdAt": "2023-02-20 13:59:53", "updatedAt": "2023-08-02 14:06:12", "deletedAt": "", "path": "/user-api/v1/shipman/insurance/del", "description": "船员保险删除\n", "apiGroup": "user/shipman_insurance", "method": "GET", "necessary": 2}}, {"id": 181, "createdAt": "", "updatedAt": "", "menuId": 10, "key": "保险新增", "api": {"id": 102, "uuid": "c7ff2a68-f9c1-446f-8dfa-a1ee9f4a5827", "createdAt": "2023-02-20 14:00:54", "updatedAt": "2023-08-02 14:06:12", "deletedAt": "", "path": "/user-api/v1/shipman/insurance/new", "description": "船员保险新建", "apiGroup": "user/shipman_insurance", "method": "POST", "necessary": 2}}, {"id": 185, "createdAt": "", "updatedAt": "", "menuId": 10, "key": "变更记录", "api": {"id": 106, "uuid": "15eec463-161e-4f66-9b3a-0de4c9817544", "createdAt": "2023-02-20 14:04:55", "updatedAt": "2023-08-11 17:28:23", "deletedAt": "", "path": "/user-api/v1/user/operate/record/{offset}/{length}", "description": "用户相关更新记录列表\n", "apiGroup": "user/user", "method": "POST", "necessary": 2}}, {"id": 186, "createdAt": "", "updatedAt": "", "menuId": 10, "key": "船员保险统计", "api": {"id": 103, "uuid": "9850cb42-cf3d-4e17-93e1-90a186ec1a6a", "createdAt": "2023-02-20 14:01:14", "updatedAt": "2023-08-11 17:41:57", "deletedAt": "", "path": "/user-api/v1/shipman/insurance/statisic", "description": "船员保险统计", "apiGroup": "user/shipman_insurance", "method": "GET", "necessary": 2}}, {"id": 183, "createdAt": "", "updatedAt": "", "menuId": 10, "key": "保险详情", "api": {"id": 100, "uuid": "2c468b9d-4a69-4522-b4b7-62be7e1bbc29", "createdAt": "2023-02-20 14:00:20", "updatedAt": "2023-08-02 14:06:12", "deletedAt": "", "path": "/user-api/v1/shipman/insurance/detail", "description": "船员保险详情", "apiGroup": "user/shipman_insurance", "method": "GET", "necessary": 2}}, {"id": 188, "createdAt": "", "updatedAt": "", "menuId": 10, "key": "保险编辑", "api": {"id": 104, "uuid": "2a221f0d-8aae-4601-a4fc-f9f09d4dd11e", "createdAt": "2023-02-20 14:01:27", "updatedAt": "2023-08-02 14:06:12", "deletedAt": "", "path": "/user-api/v1/shipman/insurance/update", "description": "船员保险更新", "apiGroup": "user/shipman_insurance", "method": "POST", "necessary": 2}}, {"id": 1788, "createdAt": "", "updatedAt": "", "menuId": 10, "key": "校验列表", "api": {"id": 263, "uuid": "8ac79724-131e-42e4-8ead-0fdf79c516b9", "createdAt": "2023-06-01 09:21:35", "updatedAt": "2023-08-11 17:28:23", "deletedAt": "", "path": "/power-api/v1/imports/validate/list/{offset}/{length}", "description": "校验列表", "apiGroup": "power/import", "method": "POST", "necessary": 2}}, {"id": 1789, "createdAt": "", "updatedAt": "", "menuId": 10, "key": "开始导入", "api": {"id": 264, "uuid": "bec6fb51-6383-4ad1-a655-81de80de272e", "createdAt": "2023-06-01 09:22:04", "updatedAt": "2023-08-11 17:28:23", "deletedAt": "", "path": "/power-api/v1/imports/exec", "description": "开始导入", "apiGroup": "power/import", "method": "POST", "necessary": 2}}, {"id": 1790, "createdAt": "", "updatedAt": "", "menuId": 10, "key": "导入确认完成", "api": {"id": 265, "uuid": "b2d289bf-4e6f-488d-86c5-8415d508d616", "createdAt": "2023-06-01 09:23:16", "updatedAt": "2023-08-11 17:28:23", "deletedAt": "", "path": "/power-api/v1/imports/complete", "description": "导入确认完成", "apiGroup": "power/import", "method": "POST", "necessary": 2}}, {"id": 1733, "createdAt": "", "updatedAt": "", "menuId": 10, "key": "保险续期", "api": {"id": 269, "uuid": "ae35240d-643b-4f04-bd50-4862fd024644", "createdAt": "2023-06-01 09:28:11", "updatedAt": "2023-08-02 14:06:12", "deletedAt": "", "path": "/user-api/v1/shipman/insurance/renewal", "description": "船员保险续期\n", "apiGroup": "user/shipman_insurance", "method": "POST", "necessary": 2}}, {"id": 1787, "createdAt": "", "updatedAt": "", "menuId": 10, "key": "数据校验", "api": {"id": 262, "uuid": "f7a8007f-5088-4c0c-bdc1-a6e31f571bf2", "createdAt": "2023-06-01 09:21:02", "updatedAt": "2023-08-11 17:28:23", "deletedAt": "", "path": "/power-api/v1/imports/validate", "description": "校验数据\n", "apiGroup": "power/import", "method": "POST", "necessary": 2}}, {"id": 1734, "createdAt": "", "updatedAt": "", "menuId": 10, "key": "保险续期详情", "api": {"id": 270, "uuid": "81acecf5-af46-4ef6-baea-e2c53c311dce", "createdAt": "2023-06-01 09:29:00", "updatedAt": "2023-08-10 17:09:00", "deletedAt": "", "path": "/user-api/v1/shipman/insurance/renewal/detail", "description": "船员保险续期详情", "apiGroup": "user/shipman_insurance", "method": "GET", "necessary": 2}}], "children": [{"id": 11, "uuid": "4d053a6e-1e5a-43a0-b54b-f818040bad93", "createdAt": "", "updatedAt": "", "deletedAt": "", "platformId": 1, "endpointId": 1, "parentId": 10, "parentTitle": "", "path": "/sailor-mgr/sailor-insurance-logs", "name": "SailorInsuranceLogs", "hidden": 1, "component": "@/views/sailor-module/sailor-insurance/history/index.vue", "sortNum": 0, "keepAlive": 2, "title": "保险更新记录", "icon": "", "closeTab": 2, "openNewPage": 2, "params": null, "btns": [{"id": 187, "createdAt": "", "updatedAt": "", "menuId": 11, "key": "变更记录", "api": {"id": 106, "uuid": "15eec463-161e-4f66-9b3a-0de4c9817544", "createdAt": "2023-02-20 14:04:55", "updatedAt": "2023-08-11 17:28:23", "deletedAt": "", "path": "/user-api/v1/user/operate/record/{offset}/{length}", "description": "用户相关更新记录列表\n", "apiGroup": "user/user", "method": "POST", "necessary": 2}}], "children": null}]}]}, {"id": 113, "uuid": "edba0aac-b66a-4932-b1a4-3a4939ceb682", "createdAt": "", "updatedAt": "", "deletedAt": "", "platformId": 1, "endpointId": 1, "parentId": 0, "parentTitle": "", "path": "/big-screen", "name": "BigScreen", "hidden": 2, "component": "@/views/big-screen/index.vue", "sortNum": 75, "keepAlive": 2, "title": "数智驾舱", "icon": "DataAnalysis", "closeTab": 2, "openNewPage": 1, "params": null, "btns": [{"id": 2524, "createdAt": "", "updatedAt": "", "menuId": 113, "key": "数据大屏", "api": {"id": 16, "uuid": "8187fd84-f169-4b9f-b418-414b9e645709", "createdAt": "2022-12-28 14:03:24", "updatedAt": "2023-08-11 17:41:57", "deletedAt": "", "path": "/", "description": "权限控制用，无对应接口", "apiGroup": "", "method": "POST", "necessary": 2}}, {"id": 2532, "createdAt": "", "updatedAt": "", "menuId": 113, "key": "甘特图条形列表", "api": {"id": 125, "uuid": "fdeb8970-492d-485e-937b-76051e45a51c", "createdAt": "2023-02-28 16:35:54", "updatedAt": "2023-08-11 17:41:57", "deletedAt": "", "path": "/ship-api/v1/dispatch/gantt/schedule/list", "description": "甘特图条形列表", "apiGroup": "dispatch/gantt", "method": "POST", "necessary": 2}}, {"id": 2533, "createdAt": "", "updatedAt": "", "menuId": 113, "key": "大屏船舶列表", "api": {"id": 315, "uuid": "b994f03f-2a56-4905-95c4-10320bf6ebd3", "createdAt": "2023-08-10 16:48:21", "updatedAt": "2023-08-11 17:41:57", "deletedAt": "", "path": "/ship-api/v1/screen/ship/list", "description": "大屏船舶列表\n", "apiGroup": "ship/screen", "method": "POST", "necessary": 2}}, {"id": 2534, "createdAt": "", "updatedAt": "", "menuId": 113, "key": "船员在船情况", "api": {"id": 319, "uuid": "294d8d0e-5df4-4978-be63-0c92b1b5f23a", "createdAt": "2023-08-10 16:51:47", "updatedAt": "2023-08-11 17:41:57", "deletedAt": "", "path": "/user-api/v1/screen/shipman/on/statisic", "description": "在船船员统计\n", "apiGroup": "user/screen", "method": "GET", "necessary": 2}}, {"id": 2535, "createdAt": "", "updatedAt": "", "menuId": 113, "key": "船舶证书统计数据", "api": {"id": 114, "uuid": "3e5f0ef5-c8eb-4d1f-9bc0-33d1d9f28f1e", "createdAt": "2023-02-22 11:02:52", "updatedAt": "2023-08-11 17:41:57", "deletedAt": "", "path": "/ship-api/v1/cert/expire/statisic", "description": "船舶证书统计", "apiGroup": "ship/cert", "method": "GET", "necessary": 2}}, {"id": 2536, "createdAt": "", "updatedAt": "", "menuId": 113, "key": "船舶保险统计数据", "api": {"id": 113, "uuid": "57def7e1-351c-446c-9ff8-65bf5183186a", "createdAt": "2023-02-22 11:02:37", "updatedAt": "2023-08-11 17:41:57", "deletedAt": "", "path": "/ship-api/v1/insurance/expire/statisic", "description": "船舶保险统计", "apiGroup": "ship/insurance", "method": "GET", "necessary": 2}}, {"id": 2537, "createdAt": "", "updatedAt": "", "menuId": 113, "key": "船员证书统计数据", "api": {"id": 92, "uuid": "d902d729-d9c6-463a-a0e6-c7702ec2d9d6", "createdAt": "2023-02-20 13:54:10", "updatedAt": "2023-08-11 17:41:57", "deletedAt": "", "path": "/user-api/v1/shipman/cert/statisic", "description": "船员证书统计\n", "apiGroup": "user/shipman_cert", "method": "GET", "necessary": 2}}, {"id": 2538, "createdAt": "", "updatedAt": "", "menuId": 113, "key": "船员保险统计数据", "api": {"id": 103, "uuid": "9850cb42-cf3d-4e17-93e1-90a186ec1a6a", "createdAt": "2023-02-20 14:01:14", "updatedAt": "2023-08-11 17:41:57", "deletedAt": "", "path": "/user-api/v1/shipman/insurance/statisic", "description": "船员保险统计", "apiGroup": "user/shipman_insurance", "method": "GET", "necessary": 2}}, {"id": 2539, "createdAt": "", "updatedAt": "", "menuId": 113, "key": "存油统计", "api": {"id": 219, "uuid": "04e4231b-aa82-42e7-a7d0-e9c3517b0f84", "createdAt": "2023-04-18 09:57:13", "updatedAt": "2023-08-11 17:41:57", "deletedAt": "", "path": "/ship-api/v1/device/oil/static", "description": "存油统计", "apiGroup": "ship/device", "method": "POST", "necessary": 2}}, {"id": 2540, "createdAt": "", "updatedAt": "", "menuId": 113, "key": "大屏调度统计", "api": {"id": 318, "uuid": "a1f82a60-ecce-4b4c-86b7-949afa9a5e8e", "createdAt": "2023-08-10 16:49:58", "updatedAt": "2023-08-11 17:41:57", "deletedAt": "", "path": "/ship-api/v1/screen/dispatch/static", "description": "大屏调度统计\n", "apiGroup": "ship/screen", "method": "GET", "necessary": 2}}, {"id": 2541, "createdAt": "", "updatedAt": "", "menuId": 113, "key": "大屏船舶统计", "api": {"id": 317, "uuid": "83666725-261b-4f5b-89be-d382630053e2", "createdAt": "2023-08-10 16:49:26", "updatedAt": "2023-08-11 17:41:57", "deletedAt": "", "path": "/ship-api/v1/screen/ship/static", "description": "大屏船舶统计\n", "apiGroup": "ship/screen", "method": "GET", "necessary": 2}}, {"id": 2542, "createdAt": "", "updatedAt": "", "menuId": 113, "key": "进行中调度列表统计", "api": {"id": 220, "uuid": "ac2df47c-f1f7-42c1-bc9f-d9f43f17fa62", "createdAt": "2023-04-18 09:58:14", "updatedAt": "2023-08-11 17:41:57", "deletedAt": "", "path": "/ship-api/v1/dispatch/static/progress", "description": "调度统计(进行中)", "apiGroup": "ship/dispatch", "method": "GET", "necessary": 2}}, {"id": 2543, "createdAt": "", "updatedAt": "", "menuId": 113, "key": "摄像头列表", "api": {"id": 230, "uuid": "4442648a-6e6f-4ca9-992d-f9723e4b1d05", "createdAt": "2023-04-28 14:29:46", "updatedAt": "2023-08-11 17:41:57", "deletedAt": "", "path": "/ship-api/v1/camera/list", "description": "摄像头列表", "apiGroup": "ship/camera", "method": "POST", "necessary": 2}}, {"id": 2544, "createdAt": "", "updatedAt": "", "menuId": 113, "key": "摄像头详情", "api": {"id": 229, "uuid": "bee43c55-2a4c-4c78-905c-3d90e077437a", "createdAt": "2023-04-28 14:28:55", "updatedAt": "2023-08-11 17:41:57", "deletedAt": "", "path": "/ship-api/v1/camera/detail", "description": "摄像头详情", "apiGroup": "ship/camera", "method": "POST", "necessary": 2}}, {"id": 2545, "createdAt": "", "updatedAt": "", "menuId": 113, "key": "摄像头状态列表", "api": {"id": 233, "uuid": "203bbc49-ddf8-44e7-a6a4-2c74d36bf3d2", "createdAt": "2023-04-28 14:31:19", "updatedAt": "2023-08-11 17:41:57", "deletedAt": "", "path": "/ship-api/v1/camera/state/list", "description": "摄像头状态获取", "apiGroup": "ship/camera", "method": "POST", "necessary": 2}}, {"id": 2546, "createdAt": "", "updatedAt": "", "menuId": 113, "key": "摄像头统计", "api": {"id": 231, "uuid": "0323b3fd-fe06-4a82-a651-c94b24006441", "createdAt": "2023-04-28 14:30:12", "updatedAt": "2023-08-11 17:41:57", "deletedAt": "", "path": "/ship-api/v1/camera/stat", "description": "摄像头统计\n", "apiGroup": "ship/camera", "method": "GET", "necessary": 2}}, {"id": 2547, "createdAt": "", "updatedAt": "", "menuId": 113, "key": "告警列表", "api": {"id": 313, "uuid": "698bf044-62ab-4a68-9951-c0a0ab8c9c74", "createdAt": "2023-08-10 16:47:32", "updatedAt": "2023-08-11 17:41:57", "deletedAt": "", "path": "/ship-api/v1/screen/warn/list/{offset}/{length}", "description": "大屏告警列表\n", "apiGroup": "ship/screen", "method": "POST", "necessary": 2}}, {"id": 2548, "createdAt": "", "updatedAt": "", "menuId": 113, "key": "忽略警告", "api": {"id": 314, "uuid": "ff4b1f02-fede-457d-b4bf-3c66c78cd1c7", "createdAt": "2023-08-10 16:47:50", "updatedAt": "2023-08-11 17:41:57", "deletedAt": "", "path": "/ship-api/v1/screen/warn/enable", "description": "大屏告警处理\n", "apiGroup": "ship/screen", "method": "POST", "necessary": 2}}, {"id": 2650, "createdAt": "", "updatedAt": "", "menuId": 113, "key": "进行中调度列表", "api": {"id": 316, "uuid": "86f1d8eb-87db-4d61-bd8c-0c468a4be4b3", "createdAt": "2023-08-10 16:48:43", "updatedAt": "2023-08-11 17:41:57", "deletedAt": "", "path": "/ship-api/v1/screen/dispatch/loc/list", "description": "大屏调度点位列表\n", "apiGroup": "ship/screen", "method": "POST", "necessary": 2}}], "children": null}, {"id": 23, "uuid": "1f50ae3f-bcfe-4b70-be09-69f2f55a9c04", "createdAt": "", "updatedAt": "", "deletedAt": "", "platformId": 1, "endpointId": 1, "parentId": 0, "parentTitle": "", "path": "/project-mgr/project-mgr", "name": "ProjectMgr", "hidden": 2, "component": "@/views/project-module/project-mgr/index.vue", "sortNum": 10, "keepAlive": 2, "title": "经纪市场", "icon": "project", "closeTab": 2, "openNewPage": 2, "params": null, "btns": null, "children": [{"id": 24, "uuid": "d8496f64-f977-4ca5-83bc-6f28901d7040", "createdAt": "", "updatedAt": "", "deletedAt": "", "platformId": 1, "endpointId": 1, "parentId": 23, "parentTitle": "", "path": "/project-mgr/project-mgr", "name": "ProjectMgr", "hidden": 2, "component": "/project-mgr/project-mgr", "sortNum": 0, "keepAlive": 2, "title": "船舶项目", "icon": "", "closeTab": 2, "openNewPage": 2, "params": null, "btns": [{"id": 596, "createdAt": "", "updatedAt": "", "menuId": 24, "key": "项目列表", "api": {"id": 144, "uuid": "78c87215-085b-4c95-a122-5b6ff7764f53", "createdAt": "2023-03-21 16:43:39", "updatedAt": "2023-07-13 18:15:11", "deletedAt": "", "path": "/ship-api/v1/project/auth/list/{offset}/{length}", "description": "船东已授权的项目列表\n", "apiGroup": "ship/project/corp", "method": "POST", "necessary": 2}}, {"id": 597, "createdAt": "", "updatedAt": "", "menuId": 24, "key": "项目详情", "api": {"id": 147, "uuid": "0b75e90e-397a-4de6-a631-e217e1a4efce", "createdAt": "2023-03-21 16:44:14", "updatedAt": "2023-03-22 15:47:12", "deletedAt": "", "path": "/ship-api/v1/project/corp/detail", "description": "项目详情\n", "apiGroup": "ship/project/corp", "method": "GET", "necessary": 2}}, {"id": 598, "createdAt": "", "updatedAt": "", "menuId": 24, "key": "对外授权", "api": {"id": 145, "uuid": "4ed9a512-97e0-4365-9580-5458dedaeb30", "createdAt": "2023-03-21 16:43:50", "updatedAt": "2023-03-22 13:41:47", "deletedAt": "", "path": "/ship-api/v1/project/apply/auth/ship", "description": "对项目方授权船舶\n", "apiGroup": "ship/project/corp", "method": "POST", "necessary": 2}}, {"id": 599, "createdAt": "", "updatedAt": "", "menuId": 24, "key": "授权申请列表", "api": {"id": 143, "uuid": "9a5b69db-fd64-422c-bdea-ced61acbbd1c", "createdAt": "2023-03-21 16:43:30", "updatedAt": "2023-03-22 13:41:47", "deletedAt": "", "path": "/ship-api/v1/project/apply/list/{offset}/{length}", "description": "船东项目申请记录列表\n", "apiGroup": "ship/project/corp", "method": "POST", "necessary": 2}}, {"id": 631, "createdAt": "", "updatedAt": "", "menuId": 24, "key": "船舶详情", "api": {"id": 16, "uuid": "8187fd84-f169-4b9f-b418-414b9e645709", "createdAt": "2022-12-28 14:03:24", "updatedAt": "2023-08-11 17:41:57", "deletedAt": "", "path": "/", "description": "权限控制用，无对应接口", "apiGroup": "", "method": "POST", "necessary": 2}}], "children": [{"id": 25, "uuid": "362dbc94-e0b6-4a8e-b680-12297afd8eb9", "createdAt": "", "updatedAt": "", "deletedAt": "", "platformId": 1, "endpointId": 1, "parentId": 24, "parentTitle": "", "path": "/project-mgr/project-detail", "name": "ProjectDetail", "hidden": 1, "component": "@/views/project-module/project-detail/index.vue", "sortNum": 0, "keepAlive": 2, "title": "项目详情", "icon": "", "closeTab": 2, "openNewPage": 2, "params": null, "btns": [{"id": 600, "createdAt": "", "updatedAt": "", "menuId": 25, "key": "项目详情", "api": {"id": 147, "uuid": "0b75e90e-397a-4de6-a631-e217e1a4efce", "createdAt": "2023-03-21 16:44:14", "updatedAt": "2023-03-22 15:47:12", "deletedAt": "", "path": "/ship-api/v1/project/corp/detail", "description": "项目详情\n", "apiGroup": "ship/project/corp", "method": "GET", "necessary": 2}}, {"id": 637, "createdAt": "", "updatedAt": "", "menuId": 25, "key": "船舶详情", "api": {"id": 16, "uuid": "8187fd84-f169-4b9f-b418-414b9e645709", "createdAt": "2022-12-28 14:03:24", "updatedAt": "2023-08-11 17:41:57", "deletedAt": "", "path": "/", "description": "权限控制用，无对应接口", "apiGroup": "", "method": "POST", "necessary": 2}}], "children": null}]}]}, {"id": 76, "uuid": "a7f2a77b-addb-4d85-aef3-a97e38fb768f", "createdAt": "", "updatedAt": "", "deletedAt": "", "platformId": 1, "endpointId": 1, "parentId": 0, "parentTitle": "", "path": "/camera-mgr/camera-mgr", "name": "CameraMgr", "hidden": 2, "component": "@/views/camera-module/camera-mgr/index.vue", "sortNum": 0, "keepAlive": 2, "title": "摄像头管理", "icon": "VideoCamera", "closeTab": 2, "openNewPage": 2, "params": null, "btns": [{"id": 1164, "createdAt": "", "updatedAt": "", "menuId": 76, "key": "摄像头列表", "api": {"id": 230, "uuid": "4442648a-6e6f-4ca9-992d-f9723e4b1d05", "createdAt": "2023-04-28 14:29:46", "updatedAt": "2023-08-11 17:41:57", "deletedAt": "", "path": "/ship-api/v1/camera/list", "description": "摄像头列表", "apiGroup": "ship/camera", "method": "POST", "necessary": 2}}, {"id": 1165, "createdAt": "", "updatedAt": "", "menuId": 76, "key": "历史抓拍", "api": {"id": 228, "uuid": "71258413-fbb8-4964-91ec-4f1396514c1f", "createdAt": "2023-04-28 14:28:22", "updatedAt": "2023-05-04 14:28:27", "deletedAt": "", "path": "/ship-api/v1/camera/capture/list", "description": "历史抓拍列表\n", "apiGroup": "ship/camera", "method": "POST", "necessary": 2}}, {"id": 1166, "createdAt": "", "updatedAt": "", "menuId": 76, "key": "摄像头详情", "api": {"id": 229, "uuid": "bee43c55-2a4c-4c78-905c-3d90e077437a", "createdAt": "2023-04-28 14:28:55", "updatedAt": "2023-08-11 17:41:57", "deletedAt": "", "path": "/ship-api/v1/camera/detail", "description": "摄像头详情", "apiGroup": "ship/camera", "method": "POST", "necessary": 2}}, {"id": 1167, "createdAt": "", "updatedAt": "", "menuId": 76, "key": "摄像头状态列表", "api": {"id": 233, "uuid": "203bbc49-ddf8-44e7-a6a4-2c74d36bf3d2", "createdAt": "2023-04-28 14:31:19", "updatedAt": "2023-08-11 17:41:57", "deletedAt": "", "path": "/ship-api/v1/camera/state/list", "description": "摄像头状态获取", "apiGroup": "ship/camera", "method": "POST", "necessary": 2}}, {"id": 1168, "createdAt": "", "updatedAt": "", "menuId": 76, "key": "编辑摄像头", "api": {"id": 232, "uuid": "f4ff0bf6-e0e0-4123-b84b-3504232e50d6", "createdAt": "2023-04-28 14:30:45", "updatedAt": "2023-05-04 14:28:27", "deletedAt": "", "path": "/ship-api/v1/camera/update", "description": "摄像头编辑\n", "apiGroup": "ship/camera", "method": "POST", "necessary": 2}}, {"id": 1169, "createdAt": "", "updatedAt": "", "menuId": 76, "key": "船舶下拉列表", "api": {"id": 234, "uuid": "798cc0df-1a8e-472e-bdd5-b2f81e4e65b3", "createdAt": "2023-05-04 14:26:50", "updatedAt": "2023-05-04 14:28:57", "deletedAt": "", "path": "/ship-api/v1/camera/ship/search/list", "description": "摄像头船舶下拉列表", "apiGroup": "ship/camera", "method": "POST", "necessary": 2}}], "children": [{"id": 76, "uuid": "a7f2a77b-addb-4d85-aef3-a97e38fb768f", "createdAt": "", "updatedAt": "", "deletedAt": "", "platformId": 1, "endpointId": 1, "parentId": 0, "parentTitle": "", "path": "/camera-mgr/camera-mgr", "name": "CameraMgr", "hidden": 2, "component": "@/views/camera-module/camera-mgr/index.vue", "sortNum": 0, "keepAlive": 2, "title": "摄像头一览", "closeTab": 2, "openNewPage": 2, "params": null, "btns": [{"id": 1164, "createdAt": "", "updatedAt": "", "menuId": 76, "key": "摄像头列表", "api": {"id": 230, "uuid": "4442648a-6e6f-4ca9-992d-f9723e4b1d05", "createdAt": "2023-04-28 14:29:46", "updatedAt": "2023-08-11 17:41:57", "deletedAt": "", "path": "/ship-api/v1/camera/list", "description": "摄像头列表", "apiGroup": "ship/camera", "method": "POST", "necessary": 2}}, {"id": 1165, "createdAt": "", "updatedAt": "", "menuId": 76, "key": "历史抓拍", "api": {"id": 228, "uuid": "71258413-fbb8-4964-91ec-4f1396514c1f", "createdAt": "2023-04-28 14:28:22", "updatedAt": "2023-05-04 14:28:27", "deletedAt": "", "path": "/ship-api/v1/camera/capture/list", "description": "历史抓拍列表\n", "apiGroup": "ship/camera", "method": "POST", "necessary": 2}}, {"id": 1166, "createdAt": "", "updatedAt": "", "menuId": 76, "key": "摄像头详情", "api": {"id": 229, "uuid": "bee43c55-2a4c-4c78-905c-3d90e077437a", "createdAt": "2023-04-28 14:28:55", "updatedAt": "2023-08-11 17:41:57", "deletedAt": "", "path": "/ship-api/v1/camera/detail", "description": "摄像头详情", "apiGroup": "ship/camera", "method": "POST", "necessary": 2}}, {"id": 1167, "createdAt": "", "updatedAt": "", "menuId": 76, "key": "摄像头状态列表", "api": {"id": 233, "uuid": "203bbc49-ddf8-44e7-a6a4-2c74d36bf3d2", "createdAt": "2023-04-28 14:31:19", "updatedAt": "2023-08-11 17:41:57", "deletedAt": "", "path": "/ship-api/v1/camera/state/list", "description": "摄像头状态获取", "apiGroup": "ship/camera", "method": "POST", "necessary": 2}}, {"id": 1168, "createdAt": "", "updatedAt": "", "menuId": 76, "key": "编辑摄像头", "api": {"id": 232, "uuid": "f4ff0bf6-e0e0-4123-b84b-3504232e50d6", "createdAt": "2023-04-28 14:30:45", "updatedAt": "2023-05-04 14:28:27", "deletedAt": "", "path": "/ship-api/v1/camera/update", "description": "摄像头编辑\n", "apiGroup": "ship/camera", "method": "POST", "necessary": 2}}, {"id": 1169, "createdAt": "", "updatedAt": "", "menuId": 76, "key": "船舶下拉列表", "api": {"id": 234, "uuid": "798cc0df-1a8e-472e-bdd5-b2f81e4e65b3", "createdAt": "2023-05-04 14:26:50", "updatedAt": "2023-05-04 14:28:57", "deletedAt": "", "path": "/ship-api/v1/camera/ship/search/list", "description": "摄像头船舶下拉列表", "apiGroup": "ship/camera", "method": "POST", "necessary": 2}}], "children": null}, {"id": 77, "uuid": "a7f2a77b-addb-4d85-aef3-a97e38fb769f", "createdAt": "", "updatedAt": "", "deletedAt": "", "platformId": 1, "endpointId": 1, "parentId": 0, "parentTitle": "", "path": "/camera-mgr/camera-alarm", "name": "CameraAlarm", "hidden": 2, "component": "@/views/camera-module/camera-alarm/index.vue", "sortNum": 0, "keepAlive": 1, "title": "智能预警", "closeTab": 2, "openNewPage": 2, "params": null, "btns": null, "children": [{"id": 77, "uuid": "a7f2a77b-addb-4d85-aef3-a97e38fb769f", "createdAt": "", "updatedAt": "", "deletedAt": "", "platformId": 1, "endpointId": 1, "parentId": 0, "parentTitle": "", "path": "/camera-mgr/alarm-setting", "name": "AlarmSetting", "hidden": 1, "component": "@/views/camera-module/alarm-setting/index.vue", "sortNum": 0, "keepAlive": 2, "title": "预警人员设置", "closeTab": 2, "openNewPage": 2, "params": null, "btns": null, "children": null}]}]}, {"id": 82, "uuid": "1e22dffa-e340-4523-be5d-2a13c7553515", "createdAt": "", "updatedAt": "", "deletedAt": "", "platformId": 1, "endpointId": 1, "parentId": 0, "parentTitle": "", "path": "/msa-announce-mgr/msa-announce-mgr", "name": "MsaAnnounceMgr", "hidden": 2, "component": "@/views/msa-announce-module/announce-list/index.vue", "sortNum": 0, "keepAlive": 1, "title": "海事预警", "icon": "Bell", "closeTab": 2, "openNewPage": 2, "params": null, "btns": null, "children": [{"id": 83, "uuid": "f00972e1-6053-4bbd-bc93-db9c6530a960", "createdAt": "", "updatedAt": "", "deletedAt": "", "platformId": 1, "endpointId": 1, "parentId": 82, "parentTitle": "", "path": "/msa-announce-mgr/msa-announce-mgr", "name": "MsaAnnounceMgr", "hidden": 1, "component": "@/views/msa-announce-module/announce-list/index.vue", "sortNum": 0, "keepAlive": 2, "title": "海事预警列表", "icon": "", "closeTab": 2, "openNewPage": 2, "params": null, "btns": [{"id": 1459, "createdAt": "", "updatedAt": "", "menuId": 83, "key": "预警详情", "api": {"id": 211, "uuid": "e82fcf4b-8126-4ffa-b530-de0026ac56f1", "createdAt": "2023-04-18 09:30:30", "updatedAt": "2023-05-31 16:27:17", "deletedAt": "", "path": "/ship-api/v1/msa/announce/detail", "description": "警告/通告详情", "apiGroup": "msa/announce", "method": "GET", "necessary": 2}}, {"id": 1481, "createdAt": "", "updatedAt": "", "menuId": 83, "key": "获取列表", "api": {"id": 212, "uuid": "7686821f-7042-49f4-8a2a-fc8a4b8d1e72", "createdAt": "2023-04-18 09:31:16", "updatedAt": "2023-06-29 10:04:13", "deletedAt": "", "path": "/ship-api/v1/msa/announce/list/{offset}/{length}", "description": "警告/通告列表", "apiGroup": "msa/announce", "method": "POST", "necessary": 2}}, {"id": 1480, "createdAt": "", "updatedAt": "", "menuId": 83, "key": "发布单位", "api": {"id": 213, "uuid": "b6d1d7cd-14e3-4fca-8ee7-a72bc2b110e0", "createdAt": "2023-04-18 09:32:02", "updatedAt": "2023-08-10 17:09:00", "deletedAt": "", "path": "/ship-api/v1/msa/announce/part/list", "description": "海事局列表", "apiGroup": "msa/announce", "method": "POST", "necessary": 2}}], "children": null}, {"id": 91, "uuid": "412ed27b-a000-4567-a7aa-d1393d0d71eb", "createdAt": "", "updatedAt": "", "deletedAt": "", "platformId": 1, "endpointId": 1, "parentId": 82, "parentTitle": "", "path": "/msa-announce-mgr/msa-announce-detail", "name": "MsaAnnounceDetail", "hidden": 1, "component": "@/views/msa-announce-module/announce-detail/index.vue", "sortNum": 0, "keepAlive": 2, "title": "预警详情", "icon": "", "closeTab": 2, "openNewPage": 2, "params": null, "btns": [{"id": 1483, "createdAt": "", "updatedAt": "", "menuId": 91, "key": "预警详情", "api": {"id": 211, "uuid": "e82fcf4b-8126-4ffa-b530-de0026ac56f1", "createdAt": "2023-04-18 09:30:30", "updatedAt": "2023-05-31 16:27:17", "deletedAt": "", "path": "/ship-api/v1/msa/announce/detail", "description": "警告/通告详情", "apiGroup": "msa/announce", "method": "GET", "necessary": 2}}], "children": null}, {"id": 91, "uuid": "412ed27b-a000-4567-a7aa-d1393d0d71eb", "createdAt": "", "updatedAt": "", "deletedAt": "", "platformId": 1, "endpointId": 1, "parentId": 82, "parentTitle": "", "path": "/msa-announce-mgr/maritime-detail", "name": "MaritimeDetail", "hidden": 1, "component": "@/views/msa-announce-module/maritime-detail/index.vue", "sortNum": 0, "keepAlive": 2, "title": "通知详情", "icon": "", "closeTab": 2, "openNewPage": 2, "params": null, "btns": [], "children": null}, {"id": 93, "uuid": "412ed27b-a000-4567-a7aa-d1393d0d71eb", "createdAt": "", "updatedAt": "", "deletedAt": "", "platformId": 1, "endpointId": 1, "parentId": 82, "parentTitle": "", "path": "/msa-announce-mgr/msa-announce-transmit", "name": "MsaTransmit", "hidden": 1, "component": "@/views/msa-announce-module/announce-transmit/index.vue", "sortNum": 0, "keepAlive": 2, "title": "转发通知", "icon": "", "closeTab": 2, "openNewPage": 2, "params": null, "btns": [{"id": 1483, "createdAt": "", "updatedAt": "", "menuId": 91, "key": "转发", "api": {"id": 211, "uuid": "e82fcf4b-8126-4ffa-b530-de0026ac56f1", "createdAt": "2023-04-18 09:30:30", "updatedAt": "2023-05-31 16:27:17", "deletedAt": "", "path": "/ship-api/v1/lsm/notice/forward/new", "description": "通知转发", "apiGroup": "msa/announce", "method": "POST", "necessary": 2}}], "children": null}]}, {"id": 1124, "uuid": "1f50ae3f-bcfe-4b70-be09-69f2f55a9c05", "createdAt": "", "updatedAt": "", "deletedAt": "", "platformId": 1, "endpointId": 1, "parentId": 0, "parentTitle": "", "path": "/report-center", "name": "SailorData", "hidden": 2, "component": "@/views/report-center-module/sailor-data/index.vue", "sortNum": 10, "keepAlive": 2, "title": "报表中心", "icon": "report-center", "closeTab": 2, "openNewPage": 2, "params": null, "btns": null, "children": [{"id": 1126, "uuid": "d8496f64-f977-4ca5-83bc-6f28911d7041", "createdAt": "", "updatedAt": "", "deletedAt": "", "platformId": 1, "endpointId": 1, "parentId": 23, "parentTitle": "", "path": "/report-center/cloud-documents", "name": "CloudDocuments", "hidden": 2, "component": "@/views/report-center-module/cloud-documents/documents-list/index.vue", "sortNum": 0, "keepAlive": 2, "title": "云文档", "icon": "", "closeTab": 2, "openNewPage": 2, "params": null, "btns": null, "childen": []}, {"id": 1125, "uuid": "d8496f64-f977-4ca5-83bc-6f28901d7041", "createdAt": "", "updatedAt": "", "deletedAt": "", "platformId": 1, "endpointId": 1, "parentId": 23, "parentTitle": "", "path": "/report-center/sailor-data", "name": "SailorData", "hidden": 2, "component": "@/views/report-center-module/sailor-data/index.vue", "sortNum": 0, "keepAlive": 2, "title": "船员数据", "icon": "", "closeTab": 2, "openNewPage": 2, "params": null, "btns": null, "childen": []}, {"id": 1126, "uuid": "d8496f64-f977-4ca5-83bc-6f28901d7042", "createdAt": "", "updatedAt": "", "deletedAt": "", "platformId": 1, "endpointId": 1, "parentId": 23, "parentTitle": "", "path": "/report-center/sailor-salary", "name": "Sailor<PERSON><PERSON><PERSON>", "hidden": 2, "component": "@/views/report-center-module/sailor-salary/index.vue", "sortNum": 0, "keepAlive": 2, "title": "船员薪资", "icon": "", "closeTab": 2, "openNewPage": 2, "params": null, "btns": null, "childen": []}, {"id": 1126, "uuid": "d8496f64-f977-4ca5-83bc-6f28901d7042", "createdAt": "", "updatedAt": "", "deletedAt": "", "platformId": 1, "endpointId": 1, "parentId": 23, "parentTitle": "", "path": "/report-center/salary-statistics", "name": "SailorStatistics", "hidden": 2, "component": "@/views/report-center-module/salary-statistics/index.vue", "sortNum": 0, "keepAlive": 2, "title": "数据统计", "icon": "", "closeTab": 2, "openNewPage": 2, "params": null, "btns": null, "childen": []}, {"id": 1126, "uuid": "d8496f64-f977-4ca5-83bc-6f28901d7042", "createdAt": "", "updatedAt": "", "deletedAt": "", "platformId": 1, "endpointId": 1, "parentId": 23, "parentTitle": "", "path": "/report-center/sailor-salary/add-salary", "name": "Sailor<PERSON><PERSON><PERSON><PERSON><PERSON>", "hidden": 1, "component": "@/views/report-center-module/sailor-salary/add-salary/index.vue", "sortNum": 0, "keepAlive": 2, "title": "新建薪资表", "icon": "", "closeTab": 2, "openNewPage": 2, "params": null, "btns": null, "childen": []}, {"id": 1127, "uuid": "d8496f64-f977-4ca5-83bc-6f28911d7042", "createdAt": "", "updatedAt": "", "deletedAt": "", "platformId": 1, "endpointId": 1, "parentId": 23, "parentTitle": "", "path": "/report-center/documents-edit", "name": "DocumentsEdit", "hidden": 1, "component": "@/views/report-center-module/cloud-documents/documents-edit/index.vue", "sortNum": 0, "keepAlive": 2, "title": "云文档编辑", "icon": "", "closeTab": 2, "openNewPage": 2, "params": null, "btns": null, "childen": []}]}, {"id": 84, "uuid": "e387e14d-8b51-40ef-873c-a73861680e7e", "createdAt": "", "updatedAt": "", "deletedAt": "", "platformId": 1, "endpointId": 1, "parentId": 0, "parentTitle": "", "path": "/announcement-mgr/announcement-mgr", "name": "AnnouncementMgr", "hidden": 2, "component": "@/views/announcement-module/announcement-list/index.vue", "sortNum": 0, "keepAlive": 2, "title": "公告管理", "icon": "announcement", "closeTab": 2, "openNewPage": 2, "params": null, "btns": null, "children": [{"id": 90, "uuid": "10ecff46-e0ed-4e49-9d99-5cf9d5044b51", "createdAt": "", "updatedAt": "", "deletedAt": "", "platformId": 1, "endpointId": 1, "parentId": 84, "parentTitle": "", "path": "/announcement-mgr/announcement-detail", "name": "AnnouncementDetail", "hidden": 1, "component": "@/views/announcement-module/announcement-detail/index.vue", "sortNum": 0, "keepAlive": 2, "title": "公告详情", "icon": "", "closeTab": 2, "openNewPage": 2, "params": null, "btns": [{"id": 1479, "createdAt": "", "updatedAt": "", "menuId": 90, "key": "公告详情", "api": {"id": 254, "uuid": "bbfbd987-6d75-4e18-9b8e-7c469a7869fc", "createdAt": "2023-05-16 10:46:16", "updatedAt": "2023-05-31 17:13:51", "deletedAt": "", "path": "/corp-api/v1/announcement/detail", "description": "公告详情", "apiGroup": "corp-api/announcement", "method": "GET", "necessary": 2}}], "children": null}, {"id": 85, "uuid": "50532bba-cb9e-4287-9276-df6669bc0040", "createdAt": "", "updatedAt": "", "deletedAt": "", "platformId": 1, "endpointId": 1, "parentId": 84, "parentTitle": "", "path": "/announcement-mgr/announcement-mgr", "name": "AnnouncementMgr", "hidden": 1, "component": "@/views/announcement-module/announcement-list/index.vue", "sortNum": 0, "keepAlive": 2, "title": "公告管理", "icon": "", "closeTab": 2, "openNewPage": 2, "params": null, "btns": [{"id": 1460, "createdAt": "", "updatedAt": "", "menuId": 85, "key": "添加公告", "api": {"id": 256, "uuid": "04c0f592-e4a0-4a41-9912-ae1005fd2b67", "createdAt": "2023-05-16 10:47:49", "updatedAt": "2023-05-17 14:38:19", "deletedAt": "", "path": "/corp-api/v1/announcement/new", "description": "公告新建", "apiGroup": "corp-api/announcement", "method": "POST", "necessary": 2}}, {"id": 1461, "createdAt": "", "updatedAt": "", "menuId": 85, "key": "公告详情", "api": {"id": 254, "uuid": "bbfbd987-6d75-4e18-9b8e-7c469a7869fc", "createdAt": "2023-05-16 10:46:16", "updatedAt": "2023-05-31 17:13:51", "deletedAt": "", "path": "/corp-api/v1/announcement/detail", "description": "公告详情", "apiGroup": "corp-api/announcement", "method": "GET", "necessary": 2}}, {"id": 1462, "createdAt": "", "updatedAt": "", "menuId": 85, "key": "编辑公告", "api": {"id": 258, "uuid": "f52627d6-ed04-4e79-8b53-88d6b14b211d", "createdAt": "2023-05-16 10:48:50", "updatedAt": "2023-05-17 14:38:19", "deletedAt": "", "path": "/corp-api/v1/announcement/updete", "description": "公告更新", "apiGroup": "corp-api/announcement", "method": "POST", "necessary": 2}}, {"id": 1464, "createdAt": "", "updatedAt": "", "menuId": 85, "key": "删除公告", "api": {"id": 253, "uuid": "94d0181c-7513-4bf5-8cd2-c861755cc5dc", "createdAt": "2023-05-16 10:45:48", "updatedAt": "2023-05-17 14:38:19", "deletedAt": "", "path": "/corp-api/v1/announcement/del", "description": "公告删除", "apiGroup": "corp-api/announcement", "method": "GET", "necessary": 2}}, {"id": 1463, "createdAt": "", "updatedAt": "", "menuId": 85, "key": "发布公告", "api": {"id": 257, "uuid": "24b0cb99-c540-461e-91b7-86ec399f15b1", "createdAt": "2023-05-16 10:48:21", "updatedAt": "2023-05-17 14:38:19", "deletedAt": "", "path": "/corp-api/v1/announcement/release", "description": "公告发布", "apiGroup": "corp-api/announcement", "method": "GET", "necessary": 2}}, {"id": 1470, "createdAt": "", "updatedAt": "", "menuId": 85, "key": "获取公告", "api": {"id": 255, "uuid": "a5ce1231-5eaf-47ae-b93d-3c667353fca8", "createdAt": "2023-05-16 10:47:03", "updatedAt": "2023-06-29 09:49:52", "deletedAt": "", "path": "/corp-api/v1/announcement/list/{offset}/{length}", "description": "公告列表", "apiGroup": "corp-api/announcement", "method": "POST", "necessary": 2}}], "children": null}, {"id": 86, "uuid": "6c38209e-537d-414a-a804-18b2f7244d1b", "createdAt": "", "updatedAt": "", "deletedAt": "", "platformId": 1, "endpointId": 1, "parentId": 84, "parentTitle": "", "path": "/announcement-mgr/announcement-udpate", "name": "AnnouncementUpdate", "hidden": 1, "component": "@/views/announcement-module/announcement-update/index.vue", "sortNum": 0, "keepAlive": 2, "title": "公告新增、编辑", "icon": "", "closeTab": 2, "openNewPage": 2, "params": null, "btns": [{"id": 1465, "createdAt": "", "updatedAt": "", "menuId": 86, "key": "发布公告", "api": {"id": 258, "uuid": "f52627d6-ed04-4e79-8b53-88d6b14b211d", "createdAt": "2023-05-16 10:48:50", "updatedAt": "2023-05-17 14:38:19", "deletedAt": "", "path": "/corp-api/v1/announcement/updete", "description": "公告更新", "apiGroup": "corp-api/announcement", "method": "POST", "necessary": 2}}, {"id": 1476, "createdAt": "", "updatedAt": "", "menuId": 86, "key": "新建公告", "api": {"id": 256, "uuid": "04c0f592-e4a0-4a41-9912-ae1005fd2b67", "createdAt": "2023-05-16 10:47:49", "updatedAt": "2023-05-17 14:38:19", "deletedAt": "", "path": "/corp-api/v1/announcement/new", "description": "公告新建", "apiGroup": "corp-api/announcement", "method": "POST", "necessary": 2}}, {"id": 1477, "createdAt": "", "updatedAt": "", "menuId": 86, "key": "公告详情", "api": {"id": 254, "uuid": "bbfbd987-6d75-4e18-9b8e-7c469a7869fc", "createdAt": "2023-05-16 10:46:16", "updatedAt": "2023-05-31 17:13:51", "deletedAt": "", "path": "/corp-api/v1/announcement/detail", "description": "公告详情", "apiGroup": "corp-api/announcement", "method": "GET", "necessary": 2}}], "children": null}]}, {"id": 884, "uuid": "e387e14d-8b51-40ef-873c-a73861680e7a", "createdAt": "", "updatedAt": "", "deletedAt": "", "platformId": 1, "endpointId": 1, "parentId": 0, "parentTitle": "", "path": "/ship-report-mgr/ship-report-record", "name": "ShipReaportRecord", "hidden": 2, "component": "@/views/announcement-module/announcement-list/index.vue", "sortNum": 0, "keepAlive": 2, "title": "船舶汇报", "icon": "", "closeTab": 2, "openNewPage": 2, "params": null, "btns": null}, {"id": 885, "uuid": "e387e14d-8b51-40ef-873c-a73861680e7b", "createdAt": "", "updatedAt": "", "deletedAt": "", "platformId": 1, "endpointId": 1, "parentId": 0, "parentTitle": "", "path": "/ask-leave-mgr/ask-leave-record", "name": "AskLeaveRecord", "hidden": 2, "component": "@/views/announcement-module/announcement-list/index.vue", "sortNum": 0, "keepAlive": 2, "title": "请假记录", "icon": "", "closeTab": 2, "openNewPage": 2, "params": null, "btns": null}, {"id": 22, "uuid": "a1e34853-5561-4eb5-9739-e681442760ad", "createdAt": "", "updatedAt": "", "deletedAt": "", "platformId": 1, "endpointId": 1, "parentId": 0, "parentTitle": "", "path": "/account-mgr/account-mgr", "name": "AccountMgr", "hidden": 1, "component": "@/views/account-module/index.vue", "sortNum": 0, "keepAlive": 2, "title": "账户管理", "icon": "", "closeTab": 2, "openNewPage": 2, "params": null, "btns": null, "children": null}, {"id": 1, "uuid": "6a24f597-a517-4e23-a9a7-15df6e7cf276", "createdAt": "", "updatedAt": "", "deletedAt": "", "platformId": 1, "endpointId": 1, "parentId": 0, "parentTitle": "", "path": "/depart-mgr/depart-mgr", "name": "DepartMgr", "hidden": 2, "component": "@/views/depart-module/depart-mgr/index.vue", "sortNum": 0, "keepAlive": 2, "title": "组织架构", "icon": "depart", "closeTab": 2, "openNewPage": 2, "params": null, "btns": [{"id": 1, "createdAt": "", "updatedAt": "", "menuId": 1, "key": "所有部门", "api": {"id": 1, "uuid": "504616d6-e2e1-489e-b31e-def338dea10b", "createdAt": "2022-12-28 13:52:29", "updatedAt": "2023-07-13 15:56:13", "deletedAt": "", "path": "/corp-api/v1/depart/all", "description": "所有部门", "apiGroup": "depart", "method": "GET", "necessary": 2}}, {"id": 2, "createdAt": "", "updatedAt": "", "menuId": 1, "key": "用户部门列表", "api": {"id": 2, "uuid": "e8728850-860b-4ded-b766-509fe33628d2", "createdAt": "2022-12-28 13:54:16", "updatedAt": "2023-03-24 13:21:25", "deletedAt": "", "path": "/corp-api/v1/depart/user/list", "description": "用户部门列表", "apiGroup": "depart", "method": "POST", "necessary": 2}}, {"id": 3, "createdAt": "", "updatedAt": "", "menuId": 1, "key": "部门人数统计", "api": {"id": 22, "uuid": "abba2da6-d279-4321-8c07-02faef63190c", "createdAt": "2022-12-28 14:08:31", "updatedAt": "2023-03-24 13:21:25", "deletedAt": "", "path": "/corp-api/v1/depart/user/count", "description": "部门人数统计", "apiGroup": "depart", "method": "GET", "necessary": 2}}, {"id": 4, "createdAt": "", "updatedAt": "", "menuId": 1, "key": "删除部门", "api": {"id": 3, "uuid": "194bd1d9-2358-4af4-8323-5cf7b0619592", "createdAt": "2022-12-28 13:54:46", "updatedAt": "2023-05-17 09:58:23", "deletedAt": "", "path": "/corp-api/v1/depart/del", "description": "删除部门", "apiGroup": "depart", "method": "GET", "necessary": 2}}, {"id": 5, "createdAt": "", "updatedAt": "", "menuId": 1, "key": "用户部门更新", "api": {"id": 4, "uuid": "8182f80e-9931-43ed-b833-f9638c760dac", "createdAt": "2022-12-28 13:55:16", "updatedAt": "2023-03-24 13:21:25", "deletedAt": "", "path": "/corp-api/v1/depart/user/update", "description": "用户部门更新", "apiGroup": "depart", "method": "POST", "necessary": 2}}, {"id": 6, "createdAt": "", "updatedAt": "", "menuId": 1, "key": "部门详情", "api": {"id": 5, "uuid": "8f1a5b55-7c24-430b-bf41-421f243450ed", "createdAt": "2022-12-28 13:56:04", "updatedAt": "2023-03-24 13:21:25", "deletedAt": "", "path": "/corp-api/v1/depart/detail", "description": "部门详情", "apiGroup": "depart", "method": "GET", "necessary": 2}}, {"id": 12, "createdAt": "", "updatedAt": "", "menuId": 1, "key": "成员新建", "api": {"id": 25, "uuid": "b0dd7467-057b-47f9-8d7e-1ec9d25610e4", "createdAt": "2022-12-28 14:11:32", "updatedAt": "2023-08-02 14:06:12", "deletedAt": "", "path": "/user-api/v1/user/corp/new", "description": "成员新建", "apiGroup": "user/user", "method": "POST", "necessary": 2}}, {"id": 11, "createdAt": "", "updatedAt": "", "menuId": 1, "key": "成员详情", "api": {"id": 24, "uuid": "d62a1108-1eaf-42f5-8c2c-7160f1ce7bdf", "createdAt": "2022-12-28 14:10:53", "updatedAt": "2023-08-02 14:06:12", "deletedAt": "", "path": "/user-api/v1/user/corp/detail", "description": "成员详情", "apiGroup": "user/user", "method": "GET", "necessary": 2}}, {"id": 10, "createdAt": "", "updatedAt": "", "menuId": 1, "key": "成员账号启禁用", "api": {"id": 23, "uuid": "2fc56202-2746-4e05-8ad1-f1b2df2ff1cc", "createdAt": "2022-12-28 14:10:15", "updatedAt": "2023-03-24 13:21:25", "deletedAt": "", "path": "/user-api/v1/user/corp/enable", "description": "成员启禁用", "apiGroup": "user/user", "method": "POST", "necessary": 2}}, {"id": 9, "createdAt": "", "updatedAt": "", "menuId": 1, "key": "部门下成员列表", "api": {"id": 29, "uuid": "67dd05d9-1a93-44df-a73d-5ef4e349a082", "createdAt": "2022-12-28 14:14:10", "updatedAt": "2023-04-18 09:58:52", "deletedAt": "", "path": "/user-api/v1/user/corp/depart/list/{offset}/{length}", "description": "成员列表（按部门查询）", "apiGroup": "user/user", "method": "POST", "necessary": 2}}, {"id": 8, "createdAt": "", "updatedAt": "", "menuId": 1, "key": "部门更新", "api": {"id": 7, "uuid": "0caaba7e-6c0f-4ec2-9611-45ea988aa4e5", "createdAt": "2022-12-28 13:57:24", "updatedAt": "2023-03-24 13:21:25", "deletedAt": "", "path": "/corp-api/v1/depart/update", "description": "部门更新", "apiGroup": "depart", "method": "POST", "necessary": 2}}, {"id": 7, "createdAt": "", "updatedAt": "", "menuId": 1, "key": "部门新建", "api": {"id": 6, "uuid": "ae8dd9ca-c5ac-45eb-b62f-************", "createdAt": "2022-12-28 13:56:33", "updatedAt": "2023-03-24 14:21:14", "deletedAt": "", "path": "/corp-api/v1/depart/new", "description": "部门新建", "apiGroup": "depart", "method": "POST", "necessary": 2}}, {"id": 13, "createdAt": "", "updatedAt": "", "menuId": 1, "key": "成员离职", "api": {"id": 26, "uuid": "d653cce9-7fb6-451d-adcd-6768c967e6d6", "createdAt": "2022-12-28 14:12:18", "updatedAt": "2023-03-24 13:21:25", "deletedAt": "", "path": "/user-api/v1/user/corp/leave", "description": "成员离职", "apiGroup": "user/user", "method": "POST", "necessary": 2}}, {"id": 14, "createdAt": "", "updatedAt": "", "menuId": 1, "key": "成员更新", "api": {"id": 27, "uuid": "4f8c4d18-b93e-4b86-b01f-9354944d26ce", "createdAt": "2022-12-28 14:12:51", "updatedAt": "2023-08-02 14:06:12", "deletedAt": "", "path": "/user-api/v1/user/corp/update", "description": "成员更新", "apiGroup": "user/user", "method": "POST", "necessary": 2}}, {"id": 15, "createdAt": "", "updatedAt": "", "menuId": 1, "key": "成员离职列表", "api": {"id": 28, "uuid": "93cbb735-f4db-4985-a3c1-7725c77779b6", "createdAt": "2022-12-28 14:13:20", "updatedAt": "2023-03-24 13:21:25", "deletedAt": "", "path": "/user-api/v1/user/corp/leave/list/{offset}/{length}", "description": "成员离职列表", "apiGroup": "user/user", "method": "POST", "necessary": 2}}, {"id": 16, "createdAt": "", "updatedAt": "", "menuId": 1, "key": "职位列表", "api": {"id": 30, "uuid": "75a5330e-**************-0b88f0dab320", "createdAt": "2022-12-28 14:14:46", "updatedAt": "2023-05-31 16:16:44", "deletedAt": "", "path": "/corp-api/v1/position/all", "description": "所有职位", "apiGroup": "corp/position", "method": "POST", "necessary": 2}}, {"id": 17, "createdAt": "", "updatedAt": "", "menuId": 1, "key": "用户权限查询", "api": {"id": 36, "uuid": "61ed882f-946a-45f8-8636-b85ef94e74f5", "createdAt": "2022-12-28 14:18:35", "updatedAt": "2023-03-24 13:21:25", "deletedAt": "", "path": "/permission-api/v1/user/role/get", "description": "用户权限查询", "apiGroup": "permission/user", "method": "POST", "necessary": 2}}, {"id": 18, "createdAt": "", "updatedAt": "", "menuId": 1, "key": "角色列表", "api": {"id": 37, "uuid": "5cb18e2d-2662-4057-a228-b332302056ae", "createdAt": "2022-12-28 14:19:05", "updatedAt": "2023-03-24 13:21:25", "deletedAt": "", "path": "/permission-api/v1/corp/roles", "description": "企业角色列表", "apiGroup": "permission/corp", "method": "GET", "necessary": 2}}], "children": [{"id": 2, "uuid": "9c1e775f-166b-4f1b-9d22-c8a9a3edffb3", "createdAt": "", "updatedAt": "", "deletedAt": "", "platformId": 1, "endpointId": 1, "parentId": 1, "parentTitle": "", "path": "/depart-mgr/left-member-mgr", "name": "LeftMemberMgr", "hidden": 1, "component": "@/views/depart-module/left-member-mgr/index.vue", "sortNum": 0, "keepAlive": 2, "title": "离职档案", "icon": "", "closeTab": 2, "openNewPage": 2, "params": null, "btns": null, "children": null}]}, {"id": 35, "uuid": "5365f4d3-14f2-4eea-aa10-5058d108303e", "createdAt": "", "updatedAt": "", "deletedAt": "", "platformId": 1, "endpointId": 1, "parentId": 0, "parentTitle": "", "path": "/ship-report/ship-report", "name": "shipReport", "hidden": 1, "component": "@/views/report-module/daliy-paper/index.vue", "sortNum": 0, "keepAlive": 2, "title": "船舶汇报", "icon": "", "closeTab": 2, "openNewPage": 2, "params": null, "btns": null, "children": [{"id": 39, "uuid": "1d86783e-032e-4ee3-8733-198a185de1e9", "createdAt": "", "updatedAt": "", "deletedAt": "", "platformId": 1, "endpointId": 1, "parentId": 35, "parentTitle": "", "path": "/ship-report/report-setting", "name": "settingReport", "hidden": 1, "component": "@/views/report-module/report-settings/index.vue", "sortNum": 0, "keepAlive": 1, "title": "汇报设置", "icon": "", "closeTab": 2, "openNewPage": 2, "params": null, "btns": [{"id": 659, "createdAt": "", "updatedAt": "", "menuId": 39, "key": "初始化接收人列表", "api": {"id": 163, "uuid": "236b0c53-ea05-40a7-be42-c30792b3c5b2", "createdAt": "2023-03-22 13:33:22", "updatedAt": "2023-04-17 10:03:45", "deletedAt": "", "path": "/ship-api/v1/daily/options/receivers", "description": "汇报设置初始化时获取接收人列表", "apiGroup": "ship/report", "method": "GET", "necessary": 2}}, {"id": 660, "createdAt": "", "updatedAt": "", "menuId": 39, "key": "船舶设备是否填写", "api": {"id": 162, "uuid": "c771672f-cfef-4d34-9d4b-f52f833d9d9d", "createdAt": "2023-03-22 13:32:01", "updatedAt": "2023-04-18 10:27:12", "deletedAt": "", "path": "/ship-api/v1/daily/device/is/required", "description": "船舶设备是否已填写", "apiGroup": "ship/report", "method": "GET", "necessary": 2}}, {"id": 677, "createdAt": "", "updatedAt": "", "menuId": 39, "key": "汇报更新", "api": {"id": 170, "uuid": "d412dd39-5d07-424d-889b-6ce85ba75826", "createdAt": "2023-03-22 13:44:24", "updatedAt": "2023-04-17 10:03:45", "deletedAt": "", "path": "/ship-api/v1/daily/options/update", "description": "汇报更新", "apiGroup": "ship/report", "method": "POST", "necessary": 2}}], "children": null}, {"id": 36, "uuid": "53e98ccc-e4d4-4d43-9457-df4703000044", "createdAt": "", "updatedAt": "", "deletedAt": "", "platformId": 1, "endpointId": 1, "parentId": 35, "parentTitle": "", "path": "/ship-report/ship-report", "name": "shipReport", "hidden": 2, "component": "@/views/report-module/daliy-paper/index.vue", "sortNum": 0, "keepAlive": 2, "title": "船舶日报", "icon": "Document", "closeTab": 2, "openNewPage": 2, "params": null, "btns": [{"id": 640, "createdAt": "", "updatedAt": "", "menuId": 36, "key": "汇报详情", "api": {"id": 166, "uuid": "af1cfe97-e1c8-4201-936d-94a7d16d6271", "createdAt": "2023-03-22 13:42:59", "updatedAt": "2023-04-18 10:19:30", "deletedAt": "", "path": "/ship-api/v1/daily/detail", "description": "日报详情", "apiGroup": "ship/report", "method": "GET", "necessary": 2}}, {"id": 643, "createdAt": "", "updatedAt": "", "menuId": 36, "key": "日报统计", "api": {"id": 174, "uuid": "0b3677d4-f90e-4115-af32-91b7af493684", "createdAt": "2023-03-22 13:46:23", "updatedAt": "2023-04-18 10:19:30", "deletedAt": "", "path": "/ship-api/v1/daily/stat", "description": "日报统计", "apiGroup": "ship/report", "method": "GET", "necessary": 2}}, {"id": 642, "createdAt": "", "updatedAt": "", "menuId": 36, "key": "日报列表", "api": {"id": 167, "uuid": "d891eb0c-46e5-40fe-8878-f618d64c39f7", "createdAt": "2023-03-22 13:43:16", "updatedAt": "2023-04-18 10:19:30", "deletedAt": "", "path": "/ship-api/v1/daily/list/{offset}/{length}", "description": "日报列表", "apiGroup": "ship/report", "method": "POST", "necessary": 2}}, {"id": 644, "createdAt": "", "updatedAt": "", "menuId": 36, "key": "汇报编辑", "api": {"id": 175, "uuid": "52e24199-1425-44da-b565-ba1da8411a5b", "createdAt": "2023-03-22 13:46:49", "updatedAt": "2023-04-18 10:19:30", "deletedAt": "", "path": "/ship-api/v1/daily/update", "description": "日报更新（或补填）", "apiGroup": "ship/report", "method": "POST", "necessary": 2}}, {"id": 645, "createdAt": "", "updatedAt": "", "menuId": 36, "key": "汇报补填", "api": {"id": 175, "uuid": "52e24199-1425-44da-b565-ba1da8411a5b", "createdAt": "2023-03-22 13:46:49", "updatedAt": "2023-04-18 10:19:30", "deletedAt": "", "path": "/ship-api/v1/daily/update", "description": "日报更新（或补填）", "apiGroup": "ship/report", "method": "POST", "necessary": 2}}, {"id": 646, "createdAt": "", "updatedAt": "", "menuId": 36, "key": "汇报设置", "api": {"id": 169, "uuid": "27b0e3a8-b4c3-4661-b38f-f9151301eb03", "createdAt": "2023-03-22 13:44:04", "updatedAt": "2023-04-18 10:19:30", "deletedAt": "", "path": "/ship-api/v1/daily/options/detail", "description": "汇报设置详情", "apiGroup": "ship/report", "method": "GET", "necessary": 2}}, {"id": 641, "createdAt": "", "updatedAt": "", "menuId": 36, "key": "汇报导出", "api": {"id": 16, "uuid": "8187fd84-f169-4b9f-b418-414b9e645709", "createdAt": "2022-12-28 14:03:24", "updatedAt": "2023-08-11 17:41:57", "deletedAt": "", "path": "/", "description": "权限控制用，无对应接口", "apiGroup": "", "method": "POST", "necessary": 2}}], "children": null}, {"id": 37, "uuid": "7dc12989-0d4a-435f-93c0-42c462093166", "createdAt": "", "updatedAt": "", "deletedAt": "", "platformId": 1, "endpointId": 1, "parentId": 35, "parentTitle": "", "path": "/ship-report/report-update", "name": "updateReport", "hidden": 1, "component": "@/views/report-module/report-update/index.vue", "sortNum": 0, "keepAlive": 2, "title": "日报更新", "icon": "", "closeTab": 2, "openNewPage": 2, "params": null, "btns": [{"id": 662, "createdAt": "", "updatedAt": "", "menuId": 37, "key": "日报变更记录新建", "api": {"id": 164, "uuid": "daf7d3d0-9bf3-4683-a8fc-3fc7353868a5", "createdAt": "2023-03-22 13:41:28", "updatedAt": "2023-04-18 10:19:30", "deletedAt": "", "path": "/ship-api/v1/daily/daily/record/new", "description": "日报变更记录新建", "apiGroup": "ship/report", "method": "POST", "necessary": 2}}], "children": null}, {"id": 38, "uuid": "bc23908f-5e30-4c34-9752-f5e9ed7caa84", "createdAt": "", "updatedAt": "", "deletedAt": "", "platformId": 1, "endpointId": 1, "parentId": 35, "parentTitle": "", "path": "/ship-report/report-detail", "name": "detailReport", "hidden": 1, "component": "@/views/report-module/report-detail/index.vue", "sortNum": 0, "keepAlive": 2, "title": "日报详情", "icon": "", "closeTab": 2, "openNewPage": 2, "params": null, "btns": [{"id": 639, "createdAt": "", "updatedAt": "", "menuId": 38, "key": "汇报编辑", "api": {"id": 175, "uuid": "52e24199-1425-44da-b565-ba1da8411a5b", "createdAt": "2023-03-22 13:46:49", "updatedAt": "2023-04-18 10:19:30", "deletedAt": "", "path": "/ship-api/v1/daily/update", "description": "日报更新（或补填）", "apiGroup": "ship/report", "method": "POST", "necessary": 2}}, {"id": 663, "createdAt": "", "updatedAt": "", "menuId": 38, "key": "日报更新记录列表", "api": {"id": 159, "uuid": "d4079d4b-6c22-4df6-8eba-7df580b52190", "createdAt": "2023-03-22 13:28:14", "updatedAt": "2023-04-18 10:19:30", "deletedAt": "", "path": "/ship-api/v1/daily/daily/record/list", "description": "日报更新记录列表", "apiGroup": "ship/report", "method": "POST", "necessary": 2}}, {"id": 730, "createdAt": "", "updatedAt": "", "menuId": 38, "key": "日报信息", "api": {"id": 16, "uuid": "8187fd84-f169-4b9f-b418-414b9e645709", "createdAt": "2022-12-28 14:03:24", "updatedAt": "2023-08-11 17:41:57", "deletedAt": "", "path": "/", "description": "权限控制用，无对应接口", "apiGroup": "", "method": "POST", "necessary": 2}}, {"id": 731, "createdAt": "", "updatedAt": "", "menuId": 38, "key": "变动记录", "api": {"id": 16, "uuid": "8187fd84-f169-4b9f-b418-414b9e645709", "createdAt": "2022-12-28 14:03:24", "updatedAt": "2023-08-11 17:41:57", "deletedAt": "", "path": "/", "description": "权限控制用，无对应接口", "apiGroup": "", "method": "POST", "necessary": 2}}], "children": null}]}, {"id": 880, "uuid": "5365f4d3-14f2-4eea-aa10-5058d108303e", "createdAt": "", "updatedAt": "", "deletedAt": "", "platformId": 1, "endpointId": 1, "parentId": 0, "parentTitle": "", "path": "/msg-mgr/msg-mgr", "name": "MsgMgr", "hidden": 2, "component": "@/views/msg-module/msg-mgr/index.vue", "sortNum": 0, "keepAlive": 2, "title": "通知管理", "icon": "Notification", "closeTab": 2, "openNewPage": 2, "params": null, "btns": null, "children": [{"id": 882, "uuid": "5365f4d3-14f2-4eea-aa10-12", "createdAt": "", "updatedAt": "", "deletedAt": "", "platformId": 1, "endpointId": 1, "parentId": 0, "parentTitle": "", "path": "/msg-mgr/msg-setting", "name": "MsgSetting", "hidden": 1, "component": "@/views/msg-module/msg-setting/index.vue", "sortNum": 0, "keepAlive": 2, "title": "通知设置", "icon": "Bell", "closeTab": 2, "openNewPage": 2, "params": null, "btns": null, "children": []}]}, {"id": 990, "uuid": "a1e34853-5561-4eb5-9739-e681441762mx", "createdAt": "", "updatedAt": "", "deletedAt": "", "platformId": 1, "endpointId": 1, "parentId": 0, "parentTitle": "", "path": "/banner/banner-mgr", "name": "BannerMgr", "hidden": 2, "component": "@/views/banner-module/index.vue", "sortNum": 7, "keepAlive": 2, "title": "轮播图管理", "icon": "Link", "closeTab": 2, "openNewPage": 2, "btns": [], "params": null, "children": []}, {"id": 992, "uuid": "7cdd2875-3423-41de-a11b-ddefb560e45a", "createdAt": "", "updatedAt": "", "deletedAt": "", "platformId": 1, "endpointId": 1, "parentId": 0, "parentTitle": "", "path": "/official-website/news-mgr", "name": "WebsiteMgr", "hidden": 2, "component": "@/views/website-module/news-mgr/index.vue", "sortNum": 88, "keepAlive": 2, "title": "官网管理", "icon": "connection", "closeTab": 2, "openNewPage": 2, "params": null, "btns": null, "children": [{"id": 9921, "uuid": "dd17354d-7a9d-4d41-8809-686934784b7f", "createdAt": "", "updatedAt": "", "deletedAt": "", "platformId": 1, "endpointId": 1, "parentId": 17, "parentTitle": "", "path": "/official-website/news-mgr", "name": "NewsMgr", "hidden": 2, "component": "@/views/website-module/news-mgr/index.vue", "sortNum": 0, "keepAlive": 2, "title": "新闻管理", "icon": "", "closeTab": 2, "openNewPage": 2, "params": null, "btns": [], "children": []}]}, {"id": 40, "uuid": "5a58fc09-f1dc-47e9-b736-29fa4848057e", "createdAt": "2025-06-03 16:01:56", "updatedAt": "2025-06-03 16:01:56", "deletedAt": "", "platformId": 1, "endpointId": 1, "parentId": 0, "parentTitle": "", "path": "/system-module/my-apply", "name": "SystemModule", "hidden": 2, "component": "@/views/system-module/my-apply/index.vue", "sortNum": 0, "keepAlive": 2, "title": "体系管理", "icon": "Promotion", "closeTab": 2, "openNewPage": 2, "params": null, "btns": null, "children": [{"id": 41, "uuid": "00baf19f-bade-4b55-bf00-06715599dc73", "createdAt": "2025-06-03 16:02:46", "updatedAt": "2025-06-03 16:02:46", "deletedAt": "", "platformId": 1, "endpointId": 1, "parentId": 40, "parentTitle": "", "path": "/system-module/my-apply", "name": "MyApply", "hidden": 2, "component": "@/views/system-module/my-apply/index.vue", "sortNum": 0, "keepAlive": 2, "title": "我的申请", "icon": "", "closeTab": 2, "openNewPage": 2, "params": null, "btns": null, "children": [{"id": 45, "uuid": "d9c7c78c-afe1-48eb-bb92-7478d897a8d4", "createdAt": "2025-06-04 00:17:40", "updatedAt": "2025-06-03 16:18:31", "deletedAt": "", "platformId": 1, "endpointId": 1, "parentId": 41, "parentTitle": "", "path": "/system-module/my-apply/detail", "name": "ApplyDetail", "hidden": 1, "component": "@/views/system-module/my-apply/detail/index.vue", "sortNum": 0, "keepAlive": 2, "title": "申请详情", "icon": "", "closeTab": 2, "openNewPage": 2, "params": null, "btns": null, "children": null}, {"id": 44, "uuid": "5ee696ea-5e4c-4d71-81d4-010e82924ecf", "createdAt": "2025-06-03 16:17:00", "updatedAt": "2025-06-03 16:17:00", "deletedAt": "", "platformId": 1, "endpointId": 1, "parentId": 41, "parentTitle": "", "path": "/system-module/my-apply/apply-form", "name": "ApplyForm", "hidden": 1, "component": "@/views/system-module/my-apply/apply-form/index.vue", "sortNum": 0, "keepAlive": 2, "title": "发起申请", "icon": "", "closeTab": 2, "openNewPage": 2, "params": null, "btns": null, "children": null}]}, {"id": 42, "uuid": "8107a169-b038-4159-8813-009cfbadf7dc", "createdAt": "2025-06-03 16:15:45", "updatedAt": "2025-06-03 16:15:45", "deletedAt": "", "platformId": 1, "endpointId": 1, "parentId": 40, "parentTitle": "", "path": "/system-module/approval-mgr", "name": "ApprovalMgr", "hidden": 2, "component": "@/views/system-module/approval-mgr/index.vue", "sortNum": 0, "keepAlive": 2, "title": "审批抄送", "icon": "", "closeTab": 2, "openNewPage": 2, "params": null, "btns": null, "children": [{"id": 47, "uuid": "6faa589f-3e17-472c-9681-4fb66cdfb045", "createdAt": "2025-06-03 16:18:58", "updatedAt": "2025-06-03 16:18:58", "deletedAt": "", "platformId": 1, "endpointId": 1, "parentId": 42, "parentTitle": "", "path": "/system-module/approval-mgr/detail", "name": "ApprovalDetail", "hidden": 1, "component": "@/views/system-module/approval-mgr/detail/index.vue", "sortNum": 0, "keepAlive": 2, "title": "审批详情", "icon": "", "closeTab": 2, "openNewPage": 2, "params": null, "btns": null, "children": null}, {"id": 46, "uuid": "ede60f64-b4e1-4df8-a304-6ea47d242fe3", "createdAt": "2025-06-03 16:18:21", "updatedAt": "2025-06-03 16:18:21", "deletedAt": "", "platformId": 1, "endpointId": 1, "parentId": 42, "parentTitle": "", "path": "/system-module/approval-mgr/apply", "name": "ApprovalForm", "hidden": 1, "component": "@/views/system-module/approval-mgr/approval-form/index.vue", "sortNum": 0, "keepAlive": 2, "title": "流程审批", "icon": "", "closeTab": 2, "openNewPage": 2, "params": null, "btns": null, "children": null}]}, {"id": 43, "uuid": "c614f83a-0a50-4f43-b77c-c032907c65d2", "createdAt": "2025-06-03 16:16:18", "updatedAt": "2025-06-03 16:16:18", "deletedAt": "", "platformId": 1, "endpointId": 1, "parentId": 40, "parentTitle": "", "path": "/system-module/approval-record", "name": "ApprovalRecordList", "hidden": 2, "component": "/system-module/approval-record", "sortNum": 0, "keepAlive": 2, "title": "体系台账", "icon": "", "closeTab": 2, "openNewPage": 2, "params": null, "btns": null, "children": [{"id": 48, "uuid": "92a37d2e-4057-415c-a84e-5fab1d753b68", "createdAt": "2025-06-03 16:19:26", "updatedAt": "2025-06-03 16:19:26", "deletedAt": "", "platformId": 1, "endpointId": 1, "parentId": 43, "parentTitle": "", "path": "/system-module/approval-record/detail", "name": "ApprovalRecordDetail", "hidden": 1, "component": "@/views/system-module/approval-record/detail/index.vue", "sortNum": 0, "keepAlive": 2, "title": "台账详情", "icon": "", "closeTab": 2, "openNewPage": 2, "params": null, "btns": null, "children": null}]}, {"id": 44, "uuid": "c614f83a-0a50-4f43-b77c-c032907c65d6", "createdAt": "2025-06-03 16:16:18", "updatedAt": "2025-06-03 16:16:18", "deletedAt": "", "platformId": 1, "endpointId": 1, "parentId": 40, "parentTitle": "", "path": "/system-module/system-file", "name": "SystemFileList", "hidden": 2, "component": "/system-module/system-file", "sortNum": 0, "keepAlive": 2, "title": "体系文件", "icon": "", "closeTab": 2, "openNewPage": 2, "params": null, "btns": null, "children": []}]}], "msg": "成功"}