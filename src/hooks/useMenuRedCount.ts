// 获取菜单红点数
import { MenuStore } from "@/store/modules/menu";
import { hasAuth } from "@/utils/util";
import { ApprovalService } from "@/api/modules/system";
export function useMenuRedCount() {
	//判断菜单中该路由是否存在
	function hasPath(menuList: any[], path: string): boolean {
		for (const item of menuList) {
			if (item.path === path) return true;
			if (item.children && item.children.length) {
				if (hasPath(item.children, path)) return true;
			}
		}
		return false;
	}

	// 获取红点数接口
	async function getAllRedCount() {
		const menuList = MenuStore().menuList;
		const fnList = [];
		if (!menuList?.length) return;

		if (hasPath(menuList, "/system-module/approval-mgr") && hasAuth("列表未读数量", "/system-module/approval-mgr")) {
			fnList.push(freshSystemRedCount);
		}
		fnList.length && (await Promise.all(fnList.map(fn => fn())));
	}

	/** 体系管理审批红点 */
	async function freshSystemRedCount() {
		const menuStore = MenuStore();
		const res = await ApprovalService.getApprovalCount();
		if (!res.data) throw new Error("获取审批/抄送未读数量失败");
		const { approvalCount, ccCount } = res.data;
		menuStore.setRedCount("system-mgr-approval", approvalCount);
		menuStore.setRedCount("system-mgr-copy", ccCount);
		menuStore.setMenuBadgeCount("ApprovalMgr", approvalCount + ccCount);
		return res;
	}

	let interval: NodeJS.Timer | null = null;
	/** 开始红点轮询 */
	function startPolling(time = 120 * 1000) {
		if (interval) {
			clearInterval(interval);
		}
		getAllRedCount();
		interval = setInterval(() => {
			getAllRedCount();
		}, time);

		document.addEventListener("visibilitychange", handleVisibilityChange);
	}
	/** 停止红点轮询 */
	function stopPolling() {
		if (interval) {
			clearInterval(interval);
		}
		document.removeEventListener("visibilitychange", handleVisibilityChange);
	}
	// 判断当前标签页面是否显示
	function handleVisibilityChange() {
		if (document.visibilityState === "hidden") {
			// 页面隐藏 暂停轮询
			if (interval) {
				clearInterval(interval);
			}
		} else if (document.visibilityState === "visible") {
			// 页面激活 恢复轮询
			startPolling();
		}
	}
	return {
		startPolling,
		stopPolling,
		freshSystemRedCount
	};
}
