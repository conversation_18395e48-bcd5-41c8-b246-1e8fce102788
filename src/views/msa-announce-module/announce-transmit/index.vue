<!-- 预警转发页 -->
<template>
	<SinglePage title="通知设置" page-size="90%">
		<template #body>
			<div class="announce-transmit">
				<MetaForm
					mode="edit"
					ref="form"
					:formConfig="formConfig"
					:styleConfig="{ mode: 'page', labelPosition: 'left', labelWidth: '120px', editBtnText: '发送' }"
					:rule-form="ruleForm"
				>
				</MetaForm>
			</div>
		</template>
	</SinglePage>
</template>
<script setup lang="ts">
import { reactive, ref, onMounted } from "vue";
import SinglePage from "@/meta-components/MetaLayout/SinglePage.vue";
import MetaForm from "@/meta-components/MetaForm/index.vue";
import { FormConfigOptions, FormItemOptions, SelectOption } from "@/meta-components/MetaForm/interface";
import { MemberService } from "@/api/modules/member";
import { DepartService, PositionService } from "@/api/modules/depart";
import { ShipService } from "@/api/modules/ship";
import { UploadService } from "@/api/modules/upload";
import { MaritimeService } from "@/api/modules/msa";
import { MaritimeOptions } from "@/api/interface/msa.model";
// import DepartSelect from "./components/DepartSelect.vue";
import { useRoute } from "vue-router";
import { GlobalStore } from "@/store";
import router from "@/routers";

const route = useRoute();

const globalStore = GlobalStore();
const uuid = ref(route.query.uuid);

const form = ref();
const { SourceType } = MaritimeOptions;

const departOptions: SelectOption[] = reactive([]);

let lsmFilesMap = new Map<string, string>();
const formContent: Array<FormItemOptions> = reactive([
	{
		label: "接收人来源",
		type: "radio",
		name: "source",
		span: 20,
		staticOptions: [
			{
				label: "公司通用",
				value: SourceType.COMPANY
			},
			{
				label: "选择部门",
				value: SourceType.DEPARTMENT
			},
			{
				label: "选择职务",
				value: SourceType.POSITION
			},
			{
				label: "选择人员",
				value: SourceType.USER
			}
		]
	},
	{
		label: "接收部门",
		type: "cascader",
		name: "departIds",
		placeholder: "请选择接收部门",
		staticOptions: departOptions,
		span: 20,
		otherAttr: {
			multiple: true
		},
		visible: data => {
			return data?.source === SourceType.DEPARTMENT;
		}
	},
	{
		label: "接收职务",
		type: "singleSelect",
		name: "positionIds",
		placeholder: "请选择接收职务",
		span: 20,
		otherAttr: {
			multiple: true,
			labelKey: "name",
			valueKey: "id",
			listKey: "positions"
		},
		api: PositionService.getList,
		visible: data => {
			return data?.source === SourceType.POSITION;
		}
	},
	{
		label: "接收人员",
		type: "singleSelect",
		name: "peopleIds",
		span: 20,
		placeholder: "请输入姓名或手机号搜索",
		api: MemberService.getSimpleList,
		otherAttr: {
			remote: true,
			remoteLabelKey: "userName",
			multiple: true,
			labelKey: "name",
			valueKey: "id",
			subLabelKey: "contactMobile",
			appendOptionToFormDataWithKey: [
				{ optionKey: "contactMobile", formKey: "phone" },
				{ optionKey: "name", formKey: "userName" }
			]
		},
		visible: data => {
			return data?.source === SourceType.USER;
		}
	},
	{
		label: "通知公告标题",
		type: "input",
		name: "title",
		span: 20,
		placeholder: "请输入通知公告标题",
		otherAttr: { max: 64 }
	},
	{
		label: "通知公告内容",
		type: "textarea",
		name: "content",
		span: 20,
		placeholder: "请输入通知公告内容",
		otherAttr: { max: 5000, rows: 10 }
	},
	{
		label: "上传附件",
		type: "fileUpload",
		name: "files",
		span: 20,
		api: UploadService.ossApi,
		hint: "支持上传pdf、docx、xls、xlsx、ofd、pptx格式的文件",
		otherAttr: { showUploadPlaceholder: false },
		imageOptions: {
			mode: "multiple",
			accept: ".pdf,.docx,.xls,.xlsx,.ofd,.pptx",
			pathName: "msa-maritime",
			limitNum: 10,
			maxFileSize: 50,
			filePreviewMap: lsmFilesMap
		}
	},
	{
		label: "上传图片",
		type: "fileUpload",
		name: "imageFiles",
		span: 20,
		api: UploadService.ossApi,
		hint: "支持上传jpg、png、gif格式的文件",
		otherAttr: { showUploadPlaceholder: false },
		imageOptions: {
			mode: "multiple",
			accept: ".jpg,.png,.gif",
			pathName: "msa-maritime",
			maxFileSize: 50,
			limitNum: 10
		}
	}
]);
const formConfig: FormConfigOptions = {
	key: "announce-transmit",
	items: formContent,
	// addApi: AnnounceService.newAnnouncement,
	editApi: MaritimeService.transmitNotice,
	detailApi: MaritimeService.getDetail,
	detailKey: "uuid",
	closeFunction: () => router.back(),
	dataCallBack,
	beforeSubmit,
	closeFormAt: "both",
	hintMessage: ["转发成功", "转发成功"]
};
// const cascaderProps = {
// 	multiple: true
// };
let ruleForm = reactive({
	source: [{ required: true, message: "请选择接收人来源" }],
	departIds: [{ required: true, message: "请选择接收部门" }],
	positionIds: [{ required: true, message: "请选择接收职务" }],
	peopleIds: [{ required: true, message: "请选择接收人员" }],
	title: [{ required: true, message: "请输入通知公告标题" }],
	content: [{ required: true, message: "请输入通知公告内容" }]
});
/** 将树级结构转为级联所需的数据格式 */
const reformDataToCas = (
	childrens: any[],
	labelKey: string,
	ValueKey: string
): Array<{ label: string; value: any; children: any[] }> => {
	return childrens.map((item: any) => {
		const { children } = item;
		return {
			label: item[labelKey],
			value: item[ValueKey],
			children: children && children.length > 0 ? reformDataToCas(children, labelKey, ValueKey) : []
		};
	});
};
//	蓝水母初始附件
function dataCallBack(data: any) {
	//蓝水母附件url转换
	const ps = data.files.map((item: { url: string; name: string }) => {
		return MaritimeService.getFileUrl({ url: item.url });
	});
	Promise.all(ps).then(urlsRes => {
		data.files.forEach((item: any, index: number) => {
			lsmFilesMap.set(item.url, urlsRes[index].data.url);
		});
	});
	return data;
}
function beforeSubmit(data: any) {
	if (data.source === SourceType.DEPARTMENT) {
		const sourceIds: number[] = [];
		const sourceShipIds: number[] = [];
		data.departIds.forEach((item: any) => {
			if (item[0] === "type-3") {
				sourceShipIds.push(item[item.length - 1]);
			} else {
				sourceIds.push(item[item.length - 1]);
			}
		});
		data.sourceIds = sourceIds;
		data.sourceShipIds = sourceShipIds;
	} else if (data.source === SourceType.POSITION) {
		data.sourceIds = data.positionIds;
	} else if (data.source === SourceType.USER) {
		data.sourceIds = data.peopleIds;
	}
	const files = [];
	if (data.files && data.files.length > 0) {
		files.push(...data.files);
	}
	if (data.imageFiles && data.imageFiles.length > 0) {
		files.push(...data.imageFiles);
	}
	data.files = files;
	data.lsmNoticeId = data.id;
	delete data.departIds;
	delete data.positionIds;
	delete data.peopleIds;
	delete data.id;
	delete data.imageFiles;
	return data;
}
onMounted(() => {
	// 获取部门数据
	DepartService.getAll({ corpId: globalStore.userInfo.corpId }).then(res => {
		if (res.data && res.data.departs) {
			const { departs } = res.data;
			const departChildren = [[] as SelectOption[], [] as SelectOption[]];
			departs.forEach(item => {
				const casItem: SelectOption = {
					value: item.id,
					label: item.name!,
					children: item.children && item.children.length > 0 ? reformDataToCas(item.children, "name", "id") : []
				};
				if (item.type === 1) {
					departChildren[0].push(casItem);
				} else if (item.type === 2) {
					departChildren[1].push(casItem);
				}
			});
			if (departChildren[0].length > 0) {
				departOptions.push({
					value: "type-1",
					label: "离岸部门",
					children: departChildren[0]
				});
			}
			if (departChildren[1].length > 0) {
				departOptions.push({
					value: "type-2",
					label: "岸基部门",
					children: departChildren[1]
				});
			}
		}
	});

	// 获取船舶数据
	ShipService.getList({
		corpId: globalStore.userInfo.corpId,
		offset: 0,
		length: -1
	}).then(res => {
		if (res.data && res.data.list) {
			const { list } = res.data;
			const shipChildren = [] as SelectOption[];
			list.forEach(item => {
				const casItem = {
					value: item.id,
					label: item.name
				};
				shipChildren.push(casItem);
			});
			if (shipChildren.length > 0) {
				departOptions.push({
					value: "type-3",
					label: "船舶",
					children: shipChildren
				});
			}
		}
	});
	form.value && form.value.iniForm("edit", { uuid: uuid.value }, {});
});
</script>
<style scoped lang="scss"></style>
