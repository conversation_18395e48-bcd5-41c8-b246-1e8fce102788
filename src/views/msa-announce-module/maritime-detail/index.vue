<!-- 宁波海事局通知详情页 -->
<template>
	<SinglePage page-size="100%" title="通知详情">
		<template #body>
			<div class="title-container">
				<div class="title">
					{{ detailValue.title }}
				</div>
				<div class="sub-title">
					<div class="label">来源： {{ detailValue.sender }}</div>
					<div class="label">发布时间： {{ detailValue.senderTime }}</div>
				</div>
			</div>
			<div class="detail-content">
				<div>{{ detailValue?.content }}</div>
				<br />
				<div v-if="imgFiles.length > 0">
					<div class="title">图片附件:</div>
					<div class="img-container">
						<el-image
							v-for="item in imgFiles"
							style="width: 100px; height: 100px"
							:src="item.url"
							:key="item.url"
							:zoom-rate="1.2"
							:max-scale="7"
							:min-scale="0.2"
							:preview-src-list="imgFiles.map((file: any) => file.url)"
							show-progress
							:initial-index="4"
							fit="cover"
						/>
					</div>
				</div>
				<div v-if="otherFiles.length > 0">
					<div class="title">附件：</div>
					<div class="files-container">
						<div class="file" v-for="item in otherFiles" :key="item.url">
							<el-link
								style="font-size: 14px; color: #409eff; white-space: pre-wrap"
								target="_blank"
								@click.prevent="handleDownload(item)"
							>
								{{ item.name }}
							</el-link>
						</div>
					</div>
				</div>
			</div>

			<!-- <div v-html="detailValue.content"></div> -->
		</template>
		<template #bottom-button>
			<div></div>
		</template>
	</SinglePage>
</template>

<script setup lang="ts" name="MaritimeDetail">
import { onMounted, ref, computed } from "vue";
import { useRoute } from "vue-router";
import SinglePage from "@/meta-components/MetaLayout/SinglePage.vue";
import { MaritimeService } from "@/api/modules/msa";
import { MaritimeOptions } from "@/api/interface/msa.model";
import { isPic } from "@/utils/util";
import { YN } from "@/enums/global-enums";

const route = useRoute();
const uuid = ref(route.query.uuid);
const detailValue = ref<MaritimeOptions.LsmNoticeDetailResp>({});
const imgFiles = computed(() => {
	return detailValue.value.files?.filter((item: any) => isPic(item.url)) || [];
});
const otherFiles = computed(() => {
	return detailValue.value.files?.filter((item: any) => !isPic(item.url)) || [];
});
async function getDetail() {
	try {
		let res = await MaritimeService.getDetail({ uuid: uuid.value as string });
		if (!res.data) return;
		detailValue.value = res.data;
		if (res.data.readFlag === YN.No && res.data.noticeId) {
			// 未读状态，标记为已读
			await MaritimeService.readNotice({ id: res.data.id as number, noticeId: res.data.noticeId });
		}
	} catch (error) {
		console.log(error);
	}
}

function handleDownload(file: any) {
	if (file.url) {
		MaritimeService.getFileUrl({ url: file.url })
			.then(res => {
				if (res.data && res.data.url) {
					// downFile(res.data.url, file.name);
					window.open(res.data.url, "_blank");
				} else {
					console.error("文件链接获取失败");
				}
			})
			.catch(error => {
				console.error("文件下载失败", error);
			});
	} else {
		console.error("文件链接不存在");
	}
}
onMounted(() => {
	getDetail();
});
</script>

<style scoped lang="scss">
.title-container {
	padding: 0 50px;
	margin-bottom: 45px;
	overflow: hidden;
	.title {
		margin: 16px auto 0 0;
		font-size: 20px;
		font-weight: bold;
		line-height: 24px;
		text-align: center;
	}
	.sub-title {
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 20px 0;
		font-size: 14px;
		font-weight: normal;
		line-height: 24px;
		color: $color-text-sub;
		border-bottom: 1px solid $devider-color;
		.label {
			margin-right: 32px;
		}
	}
}
.detail-content {
	padding: 20px 50px;
	overflow: hidden;
	font-size: 14px;
	word-break: break-all;
	white-space: pre-wrap;
}
::v-deep .el-link {
	font-size: 14px;
}
.img-container {
	display: flex;
	flex-wrap: wrap;
	gap: 10px;
	margin-top: 10px;
}
</style>
