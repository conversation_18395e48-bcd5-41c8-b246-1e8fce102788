<!-- 预警人员设置弹窗 -->
<template>
	<el-dialog title="预警人员设置" v-model="visible" width="700px" @open="onOpen">
		<MetaForm
			mode="edit"
			ref="form"
			:formConfig="formConfig"
			:styleConfig="{ mode: 'drawer', labelPosition: 'right', labelWidth: '100px' }"
		>
			<!-- <template #button>
				<el-button type="primary" @click="form?.submit">保存</el-button>
				<el-button @click="close">取消</el-button>
			</template> -->
		</MetaForm>
	</el-dialog>
</template>
<script setup lang="ts" name="ReadDialog">
import { ref, reactive } from "vue";
import { FormConfigOptions, FormItemOptions } from "@/meta-components/MetaForm/interface";
import MetaForm from "@/meta-components/MetaForm/index.vue";
import { MemberService } from "@/api/modules/member";
import { MaritimeService } from "@/api/modules/msa";

const visible = ref(false);
const form = ref();
const formContent: Array<FormItemOptions> = reactive([
	{
		name: "corpUserIds",
		label: "短信通知人员",
		type: "singleSelect",
		placeholder: "请输入姓名或手机号搜索",
		api: params => Promise.resolve(MemberService.getSimpleList({ ...params, offset: 0, length: -1 })),
		otherAttr: {
			remote: true,
			remoteLabelKey: "userName",
			multiple: true,
			labelKey: "name",
			valueKey: "id",
			max: 50,
			subLabelKey: "contactMobile",
			appendOptionToFormDataWithKey: [
				{ optionKey: "contactMobile", formKey: "phone" },
				{ optionKey: "name", formKey: "userName" }
			]
		},
		span: 20
	}
]);
const open = () => {
	visible.value = true;
};
const onOpen = () => {
	if (form.value) {
		form.value.iniForm("edit", {}, {});
	}
};
const close = () => {
	visible.value = false;
};
// const beforeSubmit = (formData: any) => {
// 	if (!formData.corpUserIds || formData.corpUserIds.length) {
// 		formData.corpUserIds = [];
// 	}
// 	return formData;
// };
const formConfig = reactive<FormConfigOptions>({
	key: "msa-announce-setting",
	items: formContent,
	editApi: MaritimeService.setWarning,
	// successCallBack: handleSuccess,
	// beforeSubmit: beforeSubmit,
	detailApi: MaritimeService.getWarning,
	detailKey: "id",
	closeFunction: close,
	closeFormAt: "both"
});
defineExpose({
	open,
	close
});
</script>
<style scoped lang="scss"></style>
