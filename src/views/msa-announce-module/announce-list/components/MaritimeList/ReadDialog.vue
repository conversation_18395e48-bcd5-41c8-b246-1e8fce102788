<!-- 已读/未读/接收人员列表展示弹窗 -->
<template>
	<el-dialog :title="title" v-model="visible" width="60%">
		<MetaTable title="接收人员列表" ref="table" :table-config="tableConfig">
			<template #positionInfos="{ row }">
				{{ row.positionInfos?.map((item: any) => item.name).join(",")|| "-" }}
			</template>
			<template #departInfos="{ row }">
				{{ row.departInfos?.map((item: any) => item.name).join(",")|| "-" }}
			</template>
			<template #readFlag="{ row }">
				{{ row.readFlag === YN.Yes ? "已读" : "未读" }}
			</template>
		</MetaTable>
	</el-dialog>
</template>
<script setup lang="ts" name="ReadDialog">
import { ref, computed, reactive } from "vue";
import { ColumnProps, TableConfig } from "@/meta-components/MetaTable/interface";
import MetaTable from "@/meta-components/MetaTable/index.vue";
import { MaritimeService } from "@/api/modules/msa";
// import { MaritimeOptions } from "@/api/interface/msa.model";
import { YN } from "@/enums/global-enums";
const visible = ref(false);
const table = ref();
const type = ref<YN>();

const title = computed(() => {
	switch (type.value) {
		case YN.No:
			return "未读人员列表";
		case YN.Yes:
			return "已读人员列表";
		default:
			return "接收人员列表";
	}
});
let columnsConfig: Array<ColumnProps> = [
	{ label: "接收人", prop: "name" },
	{ label: "联系电话", prop: "mobile" },
	{ label: "职务", prop: "positionInfos", slotName: "positionInfos" },
	{ label: "所在部门", prop: "departInfos", slotName: "departInfos" },
	{ label: "是否阅读", prop: "readFlag", slotName: "readFlag" },
	{ label: "阅读时间", prop: "readTime" }
];

let tableConfig: TableConfig = reactive({
	key: "readPeopleDialog",
	columns: columnsConfig,
	requestApi: MaritimeService.getReadList,
	showTitleArea: false,
	canChangeHead: false,
	selectType: "none",
	pagination: true,
	staticParam: {},
	pageSize: 10
});
const show = (item: any, statusType?: YN) => {
	visible.value = true;
	tableConfig.staticParam!.lsmNoticeId = item.id;
	if (statusType) {
		type.value = statusType;
		tableConfig.staticParam!.readFlag = statusType;
	} else if (tableConfig.staticParam) {
		delete tableConfig.staticParam.readFlag;
	}
	table.value && table.value.search();
};
const close = () => {
	visible.value = false;
};
defineExpose({
	show,
	close
});
</script>
<style scoped lang="scss"></style>
