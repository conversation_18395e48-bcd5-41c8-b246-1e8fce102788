<!-- 海事局通知列表 -->
<template>
	<div v-auth="'海事局通知列表'" style="width: 100%; height: calc(100% - 54px)">
		<MetaTable ref="table" title="宁波海事局通知" :filter-config="filterConfig" :table-config="tableConfig">
			<template #opr="scope">
				<div>
					<el-link
						:underline="false"
						type="primary"
						style="margin-right: 12px"
						@click="preview(scope.row)"
						v-auth="'海事局通知详情'"
						>查看详情</el-link
					>
					<el-link
						:underline="false"
						type="primary"
						style="margin-right: 12px"
						@click="transmit(scope.row)"
						v-auth="'海事局通知转发'"
						>转发</el-link
					>
				</div>
			</template>
			<template #receiveCount="{ row }">
				<span v-if="!row.readCount && !row.unReadCount">0</span>
				<el-link v-else style="color: #4c9dfd" @click="showRead(row)">{{
					(row.readCount || 0) + (row.unReadCount || 0)
				}}</el-link>
			</template>
			<template #readCount="{ row }">
				<span v-if="!row.readCount">0</span>
				<el-link v-else style="color: #4c9dfd" @click="showRead(row, YN.Yes)">{{ row.readCount }}</el-link>
			</template>
			<template #unReadCount="{ row }">
				<span v-if="!row.unReadCount">0</span>
				<el-link v-else style="color: #4c9dfd" @click="showRead(row, YN.No)">{{ row.unReadCount }}</el-link>
			</template>
			<template #files="{ row }">
				<span v-if="row.files && row.files.filter((item: any) => !isPic(item.url)).length > 0">
					{{ row.files.filter((item: any) => !isPic(item.url)).map((item: any) => item.name).join(", ") }}
				</span>
				<span v-else>-</span>
			</template>
			<template #imgFiles="{ row }">
				<span v-if="row.files && row.files.filter((item: any) => isPic(item.url)).length > 0">
					{{ row.files.filter((item: any) => isPic(item.url)).map((item: any) => item.name).join(", ") }}
				</span>
				<span v-else>-</span>
			</template>
		</MetaTable>
		<ReadDialog ref="readDialog" @on-success="refresh" />
	</div>
</template>
<script setup lang="ts" name="MsaMaritimeMgr">
import { reactive, ref, onMounted } from "vue";
import { FilterConfigOptions, ColumnProps, TableConfig } from "@/meta-components/MetaTable/interface";
import MetaTable from "@/meta-components/MetaTable/index.vue";
import { MaritimeService } from "@/api/modules/msa";
import { useTableMemory } from "@/meta-components/MetaHooks/usePageMemory";
import router from "@/routers";
import ReadDialog from "./ReadDialog.vue";
import { YN } from "@/enums/global-enums";
import { isPic } from "@/utils/util";

import { useTableScrollMemory } from "@/hooks/useTableScrollMemory";

// import { MaritimeOptions } from "@/api/interface/msa.model";
/**标签页配置项 */
const table = ref();
const readDialog = ref();
/**筛选框配置数量 */
const filterConfig: Array<FilterConfigOptions> = reactive([
	{
		name: "title",
		type: "input",
		span: 3,
		placeholder: "标题关键词"
	},
	{
		name: "senderTime",
		type: "dateRange",
		span: 4,
		placeholder: "发送时间"
	}
]);

let columnsConfig: Array<ColumnProps> = reactive([
	{
		label: "标题",
		prop: "title"
	},
	{
		label: "附件名称",
		prop: "files",
		slotName: "files"
	},
	{
		label: "图片附件",
		prop: "imgFiles",
		slotName: "imgFiles"
	},
	{
		label: "接收数",
		width: "100px",
		slotName: "receiveCount"
	},
	{
		label: "已读",
		width: "100px",
		slotName: "readCount"
	},
	{
		label: "未读",
		width: "100px",
		slotName: "unReadCount"
	},
	{
		label: "发送时间",
		prop: "senderTime",
		width: "180px",
		sortAttr: "senderTime",
		sortable: true
	},
	{
		label: "转发时间",
		prop: "forwardTime",
		width: "180px",
		sortAttr: "forwardTime",
		sortable: true
	},
	{
		label: "操作",
		slotName: "opr",
		width: "250px",
		fixed: "right"
	}
]);

/**表格配置 */
let tableConfig: TableConfig = reactive({
	key: "maritimeListKey",
	selectType: "none",
	columns: columnsConfig,
	requestApi: MaritimeService.getList,
	pagination: true,
	showTitleArea: false
});

useTableMemory(table, tableConfig);

/**刷新 */
function refresh() {
	table.value.search();
}
/** 查看详情 */
function preview(currentItem: any) {
	router.push({
		name: "MaritimeDetail",
		query: {
			uuid: currentItem.uuid,
			originName: "MsaAnnounceMgr"
		}
	});
}
/** 转发 */
function transmit(currentItem: any) {
	router.push({
		name: "MsaTransmit",
		query: {
			uuid: currentItem.uuid
		}
	});
}
/** 显示阅读用户列表弹窗 */
function showRead(row: any, statusType?: YN) {
	readDialog.value.show(row, statusType);
}

onMounted(() => {
	refresh();
	useTableScrollMemory(table, "MsaMaritimeMgr");
});

defineExpose({ table, refresh });
</script>

<style scoped lang="scss"></style>
