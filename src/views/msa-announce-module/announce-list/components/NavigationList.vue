<!-- 航行通告/警告列表 -->
<template>
	<div v-auth="'获取列表'" style="width: 100%; height: calc(100% - 54px)">
		<MetaTable
			ref="tabs"
			title="航行警告"
			:filter-config="filterConfig"
			:table-config="tableConfig"
			@on-selection-id-change="handleSelectColumn"
		>
			<template #opr="scope">
				<div>
					<el-link :underline="false" type="primary" style="margin-right: 12px" @click="preview(scope.row)" v-auth="'预警详情'"
						>查看详情</el-link
					>
					<el-link :underline="false" type="primary" style="margin-right: 12px" v-copy="`msaAnnounceDetail?${scope.row.uuid}`"
						>小程序路径</el-link
					>
				</div>
			</template>
		</MetaTable>
	</div>
</template>
<script setup lang="ts">
import { reactive, ref, onMounted, watch } from "vue";
import { FilterConfigOptions, ColumnProps, TableConfig } from "@/meta-components/MetaTable/interface";
import MetaTable from "@/meta-components/MetaTable/index.vue";
import { AnnounceService } from "@/api/modules/msa";
import router from "@/routers";
import { useTableMemory } from "@/meta-components/MetaHooks/usePageMemory";

import { useTableScrollMemory } from "@/hooks/useTableScrollMemory";
/**标签页配置项 */
let tabs = ref();
enum ANNOUNCEENUM {
	// 1警告，2通告
	"WARN" = 1,
	"NOTICE" = 2
}
let announceType = ref<number>(ANNOUNCEENUM.WARN);
const emit = defineEmits(["onSelectionIdChange"]);
const props = defineProps({
	// 选中id
	announceType: {
		type: Number,
		default: 1
	}
});
/**筛选框配置数量 */
let filterConfig: Array<FilterConfigOptions> = reactive([
	{
		name: "title",
		type: "input",
		span: 3,
		placeholder: "标题关键词"
	},
	{
		name: "pubTime",
		type: "dateRange",
		span: 4,
		placeholder: "发布时间"
	},
	{
		label: "筛选发布单位",
		type: "singleSelect",
		placeholder: "发布单位",
		name: "partId",
		api: AnnounceService.getPartList,
		span: 3,
		otherAttr: { labelKey: "name", valueKey: "id", listKey: "list" }
	}
]);

let columnsConfig: Array<ColumnProps> = reactive([
	{
		label: "标题",
		prop: "title"
	},
	{
		label: "发布时间",
		prop: "pubTime",
		width: "140px"
	},
	{ label: "发布单位", prop: "part", width: "200px" },
	{
		label: "操作",
		slotName: "opr",
		width: "250px",
		fixed: "right"
	}
]);

/**表格配置 */
let tableConfig: TableConfig = reactive({
	key: "announceIndexKey",
	columns: columnsConfig,
	requestApi: AnnounceService.getList,
	selectType: "multi", // none
	selectId: "uuid",
	pagination: true,
	staticParam: { type: 1, enable: 1 },
	showTitleArea: false
});

useTableMemory(tabs, tableConfig);
let columnIds = ref<string[]>([]);

/**刷新 */
function refresh() {
	tabs.value.search();
}
function handleSelectColumn(columnId: string[]) {
	columnIds.value = columnId;
	emit("onSelectionIdChange", columnIds.value);
}
function preview(currentItem: any) {
	router.push({
		name: "MsaAnnounceDetail",
		query: {
			uuid: currentItem.uuid,
			originName: "MsaAnnounceMgr"
		}
	});
}

onMounted(() => {
	useTableScrollMemory(tabs, "MsaAnnounceMgr");
});
watch(
	() => props.announceType,
	newVal => {
		if ([ANNOUNCEENUM.WARN, ANNOUNCEENUM.NOTICE].includes(newVal)) {
			announceType.value = newVal;
			tableConfig.staticParam = tableConfig.staticParam ?? {};
			tableConfig.staticParam.type = newVal;
			tabs.value && tabs.value.search();
		}
	}
);
// const
defineExpose({ tabs, refresh });
</script>

<style scoped lang="scss">
.el-main {
	// background-color: white !important;
}
</style>
