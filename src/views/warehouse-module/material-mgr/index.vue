<!-- 物资管理列表页 -->
<template>
	<div class="page-container">
		<div style="height: 100%">
			<TableLayout ref="materialsTable" title="物资管理" :table-config="tableConfig" :filter-config="filterConfig">
				<template #button>
					<el-button @click="jumpToMaterialType">物资类型管理</el-button>
					<AddBtn :btn-config="addBtnConfig" />
				</template>
				<template #opr="scope">
					<el-link
						v-if="hasAuth('物资编辑')"
						style="margin-right: 12px"
						:underline="false"
						type="primary"
						@click="handleBtnEdit('edit', scope.row)"
						>编辑</el-link
					>
					<div v-if="hasAuth('物资删除')">
						<OprDelete @on-delete="handleBtnDelete(scope.row)"></OprDelete>
					</div>
				</template>
			</TableLayout>
			<NewDialog ref="newDialogRef" @on-success="refresh" />
		</div>
	</div>
</template>

<script setup lang="ts" name="MaterialMgr">
import TableLayout from "@/meta-components/MetaTable/index.vue";
import { ref, reactive, PropType, nextTick, onMounted } from "vue";
import { ColumnProps, FilterConfigOptions, TableConfig } from "@/meta-components/MetaTable/interface";
import { MaterialService, WarehouseService } from "@/api/modules/warehouse";
import OprDelete from "@/meta-components/MetaTableComponents/OprDelete.vue";
import { ElMessage } from "element-plus";
import { SailorCert } from "@/api/interface/sailor";
import { hasAuth } from "@/utils/util";
import NewDialog from "./components/NewDialog.vue";
import AddBtn, { AddBtnConfig } from "@/components/AddBtn/index.vue";
import { useTableMemory } from "@/meta-components/MetaHooks/usePageMemory";
import { useStatisticFilter } from "@/hooks/useStatisticFilter";

import { useTableScrollMemory } from "@/hooks/useTableScrollMemory";
import router from "@/routers";

let materialsTable = ref();
let props = defineProps({
	statisticFilter: {
		type: Object as PropType<SailorCert.ReqListParam>
	}
});
nextTick(() => {
	useStatisticFilter(props, materialsTable);
});

let typeList = ref<any[]>();
const getTypeList = () => {
	MaterialService.getTypeList({ offset: -1, length: -1 }).then(res => {
		if (res.code === 0 && res.data) {
			typeList.value = res.data.list;
		}
	});
};

/**筛选框配置数量 */
let filterConfig: Array<FilterConfigOptions> = reactive([
	{
		name: "typeId",
		type: "singleSelect",
		span: 3,
		placeholder: "请输入物资类型",
		api: () => MaterialService.getTypeList({ offset: -1, length: -1 }),
		otherAttr: { labelKey: "name", valueKey: "id" }
	},
	{ name: "name", type: "input", span: 3, placeholder: "请输入物资名称" },
	{
		name: "warehouseId",
		type: "singleSelect",
		span: 3,
		placeholder: "请选择所属仓库",
		api: () => WarehouseService.getList({ offset: -1, length: -1 }),
		otherAttr: { labelKey: "name", valueKey: "id" }
	},
	{
		name: "createdAt",
		type: "dateRange",
		span: 4,
		placeholder: "选择创建时间"
	}
]);

let columnsConfig: Array<ColumnProps> = [
	{
		label: "物资类型",
		prop: "typeName",
		width: 180
	},
	{
		label: "物资名称",
		prop: "name"
	},
	{
		label: "总数量",
		width: 80,
		prop: "count"
	},
	{
		label: "创建时间",
		prop: "createdAt",
		width: 180
		// showType: "time"
	},
	{
		label: "操作",
		slotName: "opr",
		fixed: "right"
	}
];

let tableConfig: TableConfig = reactive({
	key: "material-mgr",
	columns: columnsConfig,
	requestApi: MaterialService.getList,
	selectType: "none",
	selectId: "uuid",
	pagination: true,
	staticParam: {}
});
useTableMemory(materialsTable, tableConfig);

const addBtnConfig = reactive<AddBtnConfig>({
	label: "新增物资",
	clickFn: () => {
		return handleBtnEdit("add");
	},
	visible: () => {
		return hasAuth("物资新增");
	}
});
const jumpToMaterialType = () => {
	router.push({
		path: "/warehouse-module/material-mgr/material-type",
		query: {
			originName: "MaterialMgr"
		}
	});
};
/**弹窗 */
function handleBtnEdit(mode: any, data?: SailorCert.DetailInList) {
	//清空data
	if (mode === "detail") {
		router.push("/warehouse-module/warehouse-mgr/detail?uuid=" + data!.uuid);
	} else {
		newDialogRef.value.open(mode, data);
	}
}

defineExpose({ handleBtnEdit });
const emits = defineEmits(["onRefresh"]);
/**删除 */
async function handleBtnDelete(rowData: { [key: string]: any }) {
	let { code } = await MaterialService.delete({ uuid: rowData.uuid });
	if (code !== 0) return;
	ElMessage.success("删除成功");
	refresh();
}

/**刷新 */
function refresh() {
	emits("onRefresh");
	materialsTable.value.search();
}

let newDialogRef = ref();

onMounted(() => {
	setTimeout(() => {
		getTypeList();
	}, 1000);
	useTableScrollMemory(materialsTable, "MaterialsMgr");
});
</script>

<style scoped></style>
