<!-- 物资类型管理列表页 -->
<template>
	<div class="page-container">
		<div style="height: 100%">
			<TableLayout ref="materialsTable" title="" :table-config="tableConfig" :filter-config="filterConfig">
				<template #title>
					<div class="table-title" style="text-align: center">
						<el-button link @click="router.back"
							><el-icon><ArrowLeftBold /></el-icon
						></el-button>
						物资类型
					</div></template
				>
				<template #button>
					<AddBtn :btn-config="addBtnConfig" />
				</template>
				<template #opr="scope">
					<el-link
						v-if="hasAuth('物资类型编辑')"
						style="margin-right: 12px"
						:underline="false"
						type="primary"
						@click="handleBtnEditSailorCert('edit', scope.row)"
						>编辑</el-link
					>
					<div v-if="hasAuth('物资类型删除')">
						<OprDelete @on-delete="handleBtnDelete(scope.row)"></OprDelete>
					</div>
				</template>
			</TableLayout>
			<NewDialog ref="newDialogRef" @on-success="refresh" />
		</div>
	</div>
</template>

<script setup lang="ts" name="MaterialTypeMgr">
import TableLayout from "@/meta-components/MetaTable/index.vue";
import { ref, reactive, PropType, nextTick, onMounted } from "vue";
import { ColumnProps, FilterConfigOptions, TableConfig } from "@/meta-components/MetaTable/interface";
import { MaterialService } from "@/api/modules/warehouse";
import OprDelete from "@/meta-components/MetaTableComponents/OprDelete.vue";
import { ElMessage } from "element-plus";
import { SailorCert } from "@/api/interface/sailor";
import { hasAuth } from "@/utils/util";
import NewDialog from "../components/NewTypeDialog.vue";
import AddBtn, { AddBtnConfig } from "@/components/AddBtn/index.vue";
import { useTableMemory } from "@/meta-components/MetaHooks/usePageMemory";
import { useStatisticFilter } from "@/hooks/useStatisticFilter";

import { useTableScrollMemory } from "@/hooks/useTableScrollMemory";
import router from "@/routers";

let materialsTable = ref();
let props = defineProps({
	statisticFilter: {
		type: Object as PropType<SailorCert.ReqListParam>
	}
});
nextTick(() => {
	useStatisticFilter(props, materialsTable);
});

/**筛选框配置数量 */
let filterConfig: Array<FilterConfigOptions> = reactive([
	{ name: "name", type: "input", span: 4, placeholder: "请输入物资类型" },
	{
		name: "createdAt",
		type: "dateRange",
		span: 8,
		placeholder: "选择创建时间"
	}
]);

let columnsConfig: Array<ColumnProps> = [
	{
		label: "物资类型",
		prop: "name"
	},
	{
		label: "创建时间",
		width: 200,
		prop: "createdAt"
	},
	{
		label: "操作",
		slotName: "opr",
		fixed: "right"
	}
];

let tableConfig: TableConfig = reactive({
	key: "material-type-mgr",
	columns: columnsConfig,
	requestApi: MaterialService.getTypeList,
	selectType: "none",
	selectId: "uuid",
	pagination: true,
	staticParam: {}
});
useTableMemory(materialsTable, tableConfig);

const addBtnConfig = reactive<AddBtnConfig>({
	label: "新增物资类型",
	clickFn: () => {
		return handleBtnEditSailorCert("add");
	},
	visible: () => {
		return hasAuth("物资类型新增");
	}
});

/**弹窗 */
function handleBtnEditSailorCert(mode: any, data?: SailorCert.DetailInList) {
	//清空data
	if (mode === "detail") {
		router.push("/warehouse-module/warehouse-mgr/detail?uuid=" + data!.uuid);
	} else {
		newDialogRef.value.open(mode, data);
	}
}

defineExpose({ handleBtnEditSailorCert });
const emits = defineEmits(["onRefresh"]);
/**删除 */
async function handleBtnDelete(rowData: { [key: string]: any }) {
	let { code } = await MaterialService.deleteType({ uuid: rowData.uuid });
	if (code !== 0) return;
	ElMessage.success("删除成功");
	refresh();
}

/**刷新 */
function refresh() {
	emits("onRefresh");
	materialsTable.value.search();
}

let newDialogRef = ref();

onMounted(() => {
	useTableScrollMemory(materialsTable, "MaterialsMgr");
});
</script>

<style scoped></style>
