<!--仓库管理表单页面-->
<template>
	<div class="page-container">
		<div style="height: 100%">
			<TableLayout ref="wareHouseTable" title="应急仓库一览" :table-config="tableConfig" :filter-config="filterConfig">
				<template #button>
					<AddBtn :btn-config="addBtnConfig" />
				</template>
				<template #contact="{ row }">
					<div>
						<div v-for="i in row.contacts" :key="i.phone">{{ `${i.name} ${i.mobile}\n` }}<br /></div>
					</div>
				</template>
				<template #materials="{ row }">
					<div>
						<div v-for="i in row.goods" :key="i.phone">{{ `${i.name} ${i.count}\n` }}<br /></div>
					</div>
				</template>
				<template #opr="scope">
					<el-link
						v-if="hasAuth('仓库详情')"
						style="margin-right: 12px"
						:underline="false"
						type="primary"
						@click="handleBtnEditSailorCert('detail', scope.row)"
						>查看详情</el-link
					>
					<el-link
						v-if="hasAuth('仓库编辑')"
						style="margin-right: 12px"
						:underline="false"
						type="primary"
						@click="handleBtnEditSailorCert('edit', scope.row)"
						>编辑</el-link
					>
					<div v-if="hasAuth('仓库删除')">
						<OprDelete @on-delete="handleBtnDelete(scope.row)"></OprDelete>
					</div>
				</template>
				<!-- 查看地图 -->
				<template #address="{ row }">
					<div>
						<el-link type="primary" :underline="false" @click="checkMap(row)"
							><img src="@/assets/icons/position.png" style="width: 12px" />{{ row.address }}</el-link
						>
					</div>
				</template>
			</TableLayout>

			<CheckMapDialog
				ref="selectMapDialogRef"
				v-model:checkMapShow="checkMapShow"
				:location="location"
				mode="edit"
				title="查看地点"
			></CheckMapDialog>

			<CertRenewDialog ref="newDialogRef" @on-success="refresh" />
		</div>
	</div>
</template>

<script setup lang="ts" name="WarehouseMgr">
import TableLayout from "@/meta-components/MetaTable/index.vue";
import { ref, reactive, onMounted } from "vue";
import { ColumnProps, FilterConfigOptions, TableConfig } from "@/meta-components/MetaTable/interface";
import { WarehouseService } from "@/api/modules/warehouse";
import OprDelete from "@/meta-components/MetaTableComponents/OprDelete.vue";
import { ElMessage } from "element-plus";
import { SailorCert } from "@/api/interface/sailor";
import { hasAuth } from "@/utils/util";
import AddBtn, { AddBtnConfig } from "@/components/AddBtn/index.vue";
import { useTableMemory } from "@/meta-components/MetaHooks/usePageMemory";
import CertRenewDialog from "./components/NewDialog.vue";

import { useTableScrollMemory } from "@/hooks/useTableScrollMemory";
import router from "@/routers";
import CheckMapDialog from "@/views/dispatch-module/dispatch-mgr/map-components/CheckMapDialog.vue";

let wareHouseTable = ref();

/**筛选框配置数量 */
let filterConfig: Array<FilterConfigOptions> = reactive([
	{ name: "name", type: "input", span: 3, placeholder: "请输入仓库名称" },
	{ name: "contactName", type: "input", span: 3, placeholder: "请输入联系人" },
	{ name: "contactMobile", type: "input", span: 3, placeholder: "请输入联系电话" },
	{
		name: "createdAt",
		type: "dateRange",
		span: 8,
		placeholder: "选择创建时间"
	}
]);

let columnsConfig: Array<ColumnProps> = [
	{
		label: "仓库名称",
		width: 180,
		prop: "name"
	},
	{
		label: "联系人及电话",
		width: 180,
		slotName: "contact"
	},
	{
		label: "仓库物资",
		slotName: "materials"
	},
	{
		label: "地址",
		slotName: "address"
	},
	{
		label: "创建时间",
		prop: "createdAt",
		width: 180
		// showType: "time"
	},
	{
		label: "操作",
		slotName: "opr",
		fixed: "right"
	}
];

let tableConfig: TableConfig = reactive({
	key: "warehouse-mgr",
	columns: columnsConfig,
	requestApi: WarehouseService.getList,
	// dataCallback: data => {
	// 	injectSailorCertStatus(data);
	// 	return reformTrainContents(data);
	// },
	selectType: "none",
	selectId: "uuid",
	childrenKey: "contents",
	pagination: true,
	staticParam: {},
	defaultExpandAll: false
});
useTableMemory(wareHouseTable, tableConfig);

const addBtnConfig = reactive<AddBtnConfig>({
	label: "新增仓库",
	clickFn: () => {
		return handleBtnEditSailorCert("add");
	},
	visible: () => {
		return hasAuth("仓库新增");
	}
});

/**弹窗 */
function handleBtnEditSailorCert(mode: any, data?: SailorCert.DetailInList) {
	//清空data
	if (mode === "detail") {
		router.push({
			path: "/warehouse-module/warehouse-mgr/detail",
			query: { uuid: data!.uuid, originName: "WarehouseMgr" }
		});
	} else {
		newDialogRef.value.open(mode, data);
	}
}

defineExpose({ handleBtnEditSailorCert });
const emits = defineEmits(["onRefresh"]);
/**删除 */
async function handleBtnDelete(rowData: { [key: string]: any }) {
	let { code } = await WarehouseService.delete({ uuid: rowData.uuid });
	if (code !== 0) return;
	ElMessage.success("删除成功");
	refresh();
}

/**刷新 */
function refresh() {
	emits("onRefresh");
	wareHouseTable.value.search();
}

let newDialogRef = ref();

/**选择地图的显隐 */
let checkMapShow = ref(false);
/**显示地图弹框 */
let location = reactive({});
const checkMap = (rowData: any) => {
	Object.assign(location, { lon: rowData.lon, lat: rowData.lat });
	// Object.assign(location, { lon: 101, lat: 30 });
	checkMapShow.value = true;
};

onMounted(() => {
	useTableScrollMemory(wareHouseTable, "WarehouseMgr");
});
</script>

<style scoped></style>
