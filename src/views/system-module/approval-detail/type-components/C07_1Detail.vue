<!--C07-1 SMS文件审查会签表 表单详情  -->
<template>
	<div class="approval-container">
		<ApplyDetail
			:formData="formData"
			:items="tableItems"
			footer-text="注：本表一式二份，一份由文件编制人员/部门留存，一份报SMS办主管备案。"
		>
			<template #purpose>
				<div class="text-box">
					<p>1、确认文件格式是否符合标准要求；</p>
					<p>2、确认文件规定能否满足安全管理规定；</p>
					<p>3、确认文件规定能否完全执行；</p>
					<p>4、确认职责、工作接口是否明确；</p>
					<p>5、其他需确认的内容。</p>
				</div></template
			>
			<template #signUsers="{ content }">
				<el-table
					border
					size="small"
					:data="content.signUsers"
					style="width: calc(100% + 22px); max-width: calc(100% + 22px); margin: -8px -11px"
				>
					<el-table-column prop="departType" label="会签类别" width="200px" />
					<el-table-column prop="departName" label="部门名称" width="200px" />
					<el-table-column prop="advice" label="部门确认意见（签名/日期）" />
				</el-table>
			</template>
			<template #files="{ content }">
				<div class="file-list">
					<el-upload
						v-if="content.files && content.files.length"
						class="file-list"
						action="#"
						:disabled="true"
						:file-list="content.files"
						:on-preview="handlePreview"
					>
						<i class="el-icon-plus"></i>
					</el-upload>
					<span v-else>未上传文件！</span>
				</div>
			</template>
		</ApplyDetail>
		<Preview ref="previewRef" />
	</div>
</template>

<script setup lang="ts" name="EditShip">
import { inject, ref, Ref } from "vue";
import ApplyDetail from "../components/ApplyDetail.vue";
import { DetailItem } from "../components/interface";
import Preview from "@/components/Preview/Preview.vue";

/**表格信息 */
const formData = inject("formData") as Ref<{ [key: string]: any }>;
const showFile = inject("showFile") as Ref<boolean>;

const tableItems: DetailItem[] = [
	{
		type: "text",
		label: "会签文件名称",
		prop: "fileName",
		span: 6,
		width: "25%",
		labelAlign: "center"
	},
	{
		type: "text",
		label: "文件类别",
		prop: "fileType",
		span: 6,
		width: "25%",
		labelAlign: "center"
	},
	{
		type: "text",
		label: "文件编写部门/人员",
		prop: "fileUser",
		span: 6,
		width: "25%",
		labelAlign: "center"
	},
	{
		type: "text",
		label: "文件编号",
		prop: "fileNo",
		span: 6,
		labelAlign: "center"
	},
	{
		type: "slot",
		label: "文件会签目的",
		prop: "purpose",
		span: 24
	},
	{
		type: "slot",
		label: "相关会签人员",
		prop: "signUsers",
		span: 24
	},
	{
		type: "slot",
		label: "上传文件",
		prop: "files",
		span: 24,
		visible: () => showFile.value
	}
];
let previewRef = ref();
const handlePreview = (file: { url: string }) => {
	// 获取文件后缀名
	const extend = file.url ? file.url.split(".").pop()!.toLowerCase() : "";
	/** pdf和图片用预览组件 */
	if (["pdf", "jpg", "jpeg", "png"].includes(extend)) {
		return previewRef.value.open(file.url);
	}
	window.open(file.url, "_blank");
};
</script>

<style lang="scss" scoped>
.approval-container {
	.flex-box {
		display: flex;
		align-items: center;
		justify-content: space-between;
		width: 100%;
		.flex-item {
			display: flex;
			flex: 1;
			align-items: center;
		}
	}
	.text-box {
		p {
			margin: 8px 0;
		}
	}
	.file-list {
		::v-deep .el-upload.el-upload--text {
			display: none;
		}
	}
	::v-deep .el-table__header thead tr .el-table__cell {
		background-color: #ffffff !important;
	}
}
</style>
