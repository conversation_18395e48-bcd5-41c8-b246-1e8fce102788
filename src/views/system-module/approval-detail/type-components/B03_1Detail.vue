<!-- B03-1 培训需求表 申请表单 -->
<template>
	<div>
		<ApplyDetail :formData="formData" :items="tableItems" footer-text="注：本表一式二份，一份提出部门留存，一份报SMS办主管。">
			<template #records="{ content }">
				<el-table
					border
					size="small"
					:data="content.records"
					style="width: calc(100% + 22px); max-width: calc(100% + 22px); margin: -8px -11px"
				>
					<el-table-column prop="colIndex" label="序号" />
					<el-table-column prop="project" label="培训项目" />
					<el-table-column prop="methods" label="培训方式" />
					<el-table-column prop="participant" label="参加对象" />
					<el-table-column prop="plannedTime" label="计划实施时间" />
				</el-table>
			</template>
			<template #files="{ content }">
				<div class="file-list">
					<el-upload
						v-if="content.files && content.files.length"
						class="file-list"
						action="#"
						:disabled="true"
						:file-list="content.files"
						:on-preview="handlePreview"
					>
						<i class="el-icon-plus"></i>
					</el-upload>
					<span v-else>未上传文件！</span>
				</div>
			</template>
		</ApplyDetail>
		<Preview ref="previewRef" />
	</div>
</template>

<script setup lang="ts" name="EditShip">
import { inject, ref, Ref } from "vue";
import ApplyDetail from "../components/ApplyDetail.vue";
import { DetailItem } from "../components/interface";
import Preview from "@/components/Preview/Preview.vue";

/**表格信息 */
const formData = inject("formData") as Ref<{ [key: string]: any }>;
const showFile = inject("showFile") as Ref<boolean>;

const tableItems: DetailItem[] = [
	{
		type: "slot",
		label: "",
		prop: "records",
		labelAlign: "center",
		span: 24
	},
	{
		type: "text",
		label: "备注",
		prop: "remark",
		span: 24
	},
	{
		type: "text",
		label: "提出部门/船舶/人员",
		prop: "fromName",
		span: 12,
		width: "50%"
	},
	{
		type: "text",
		label: "提出时间",
		prop: "exerciseTime",
		span: 12,
		width: "50%"
	},
	{
		type: "text",
		label: "审批人意见",
		prop: "comment",
		span: 24
	},
	{
		type: "slot",
		label: "上传文件",
		prop: "files",
		span: 12,
		visible: () => showFile.value
	}
];

let previewRef = ref();
const handlePreview = (file: { url: string }) => {
	// 获取文件后缀名
	const extend = file.url ? file.url.split(".").pop()!.toLowerCase() : "";
	/** pdf和图片用预览组件 */
	if (["pdf", "jpg", "jpeg", "png"].includes(extend)) {
		return previewRef.value.open(file.url);
	}
	window.open(file.url, "_blank");
};
</script>

<style lang="scss" scoped>
.flex-box {
	display: flex;
	align-items: center;
	justify-content: space-between;
	width: 100%;
	.flex-item {
		display: flex;
		flex: 1;
		align-items: center;
	}
}
.file-list {
	::v-deep .el-upload.el-upload--text {
		display: none;
	}
}
</style>
