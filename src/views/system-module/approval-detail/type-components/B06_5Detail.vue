<!-- B06-5 船岸联合应急演习记录表 申请表单 -->
<template>
	<div>
		<ApplyDetail
			:formData="formData"
			:items="tableItems"
			footer-text="注：本表由SMS办主管记录，经应急总指挥签认后，一份SMS办主管自留，一份海务经理留存。"
		>
			<template #records="{ content }">
				<el-table
					border
					size="small"
					:data="content.records"
					style="width: calc(100% + 22px); max-width: calc(100% + 22px); margin: -8px -11px"
				>
					<el-table-column prop="participants" label="参加人员" />
					<el-table-column prop="arriveTime" label="到达时间" />
					<el-table-column prop="participantsSecond" label="参加人员" />
					<el-table-column prop="arriveTimeSecond" label="到达时间" />
				</el-table>
			</template>
			<template #files="{ content }">
				<div class="file-list">
					<el-upload
						v-if="content.files && content.files.length"
						class="file-list"
						action="#"
						:disabled="true"
						:file-list="content.files"
						:on-preview="handlePreview"
					>
						<i class="el-icon-plus"></i>
					</el-upload>
					<span v-else>未上传文件！</span>
				</div>
			</template>
		</ApplyDetail>
		<Preview ref="previewRef" />
	</div>
</template>

<script setup lang="ts" name="EditShip">
import { inject, ref, Ref } from "vue";
import ApplyDetail from "../components/ApplyDetail.vue";
import { DetailItem } from "../components/interface";
import Preview from "@/components/Preview/Preview.vue";
import { format } from "date-fns";

/**表格信息 */
const formData = inject("formData") as Ref<{ [key: string]: any }>;
const showFile = inject("showFile") as Ref<boolean>;

const tableItems: DetailItem[] = [
	{
		type: "text",
		label: "日期",
		prop: "date",
		width: "25%",
		format: (data: string) => data && format(new Date(data), "yyyy-MM-dd"),
		span: 6,
		labelAlign: "center"
	},
	{
		type: "text",
		label: "项目",
		prop: "project",
		width: "25%",
		span: 6,
		labelAlign: "center"
	},
	{
		type: "text",
		label: "应急总指挥",
		prop: "direct",
		width: "25%",
		span: 6,
		labelAlign: "center"
	},
	{
		type: "text",
		label: "集合地点",
		prop: "assemblingPlace",
		width: "25%",
		span: 6,
		labelAlign: "center"
	},
	{
		type: "slot",
		label: "",
		prop: "records",
		labelAlign: "center",
		span: 24
	},
	{
		type: "text",
		label: "演习和训练情况",
		prop: "trainSituation",
		height: "150px",
		span: 24
	},
	{
		type: "text",
		label: "讲评",
		prop: "comment",
		height: "100px",
		span: 24
	},
	{
		type: "text",
		label: "指定人员评价",
		prop: "personnelOpinion",
		height: "100px",
		span: 24
	},
	{
		type: "text",
		label: "记录人",
		prop: "recorder",
		width: "50%",
		span: 12
	},
	{
		type: "text",
		label: "应急总指挥",
		prop: "endDirect",
		width: "50%",
		span: 12
	},
	{
		type: "slot",
		label: "上传文件",
		prop: "files",
		span: 12,
		visible: () => showFile.value
	}
];
let previewRef = ref();
const handlePreview = (file: { url: string }) => {
	// 获取文件后缀名
	const extend = file.url ? file.url.split(".").pop()!.toLowerCase() : "";
	/** pdf和图片用预览组件 */
	if (["pdf", "jpg", "jpeg", "png"].includes(extend)) {
		return previewRef.value.open(file.url);
	}
	window.open(file.url, "_blank");
};
</script>

<style lang="scss" scoped>
.flex-box {
	display: flex;
	align-items: center;
	justify-content: space-between;
	width: 100%;
	.flex-item {
		display: flex;
		flex: 1;
		align-items: center;
	}
}
.file-list {
	::v-deep .el-upload.el-upload--text {
		display: none;
	}
}
</style>
