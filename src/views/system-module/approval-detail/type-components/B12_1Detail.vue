<!-- B12-1 SMS有效性评价/管理复查通知单 申请表单 -->
<template>
	<div>
		<ApplyDetail
			:formData="formData"
			:items="tableItems"
			footer-text="注：1、有效性评价本表由SMS办主管编制，完成后发送SMS有关各部门相关人员留存；2、管理复查本表由指定人员编制，完成后交SMS办主管发送SMS有关各部门相关人员留存。"
		>
			<template #type="{ content }">
				<span>{{ content.type.includes(1) ? "☑" : "☐" }}有效性评价&nbsp;</span>
				<span>{{ content.type.includes(2) ? "☑" : "☐" }}管理复查</span>
			</template>
			<template #files="{ content }">
				<div class="file-list">
					<el-upload
						v-if="content.files && content.files.length"
						class="file-list"
						action="#"
						:disabled="true"
						:file-list="content.files"
						:on-preview="handlePreview"
					>
						<i class="el-icon-plus"></i>
					</el-upload>
					<span v-else>未上传文件！</span>
				</div>
			</template>
		</ApplyDetail>
		<Preview ref="previewRef" />
	</div>
</template>

<script setup lang="ts" name="EditShip">
import { inject, ref, Ref } from "vue";
import ApplyDetail from "../components/ApplyDetail.vue";
import { DetailItem } from "../components/interface";
import Preview from "@/components/Preview/Preview.vue";
import { format } from "date-fns";

/**表格信息 */
const formData = inject("formData") as Ref<{ [key: string]: any }>;
const showFile = inject("showFile") as Ref<boolean>;

const tableItems: DetailItem[] = [
	{
		type: "slot",
		label: "类别",
		prop: "type",
		width: "50%",
		span: 12,
		labelAlign: "center"
	},
	{
		type: "text",
		label: "拟召开时间",
		prop: "startTime",
		span: 12,
		labelAlign: "center"
	},
	{
		type: "text",
		label: "有效性评价或复查原因",
		prop: "reason",
		height: "100px",
		span: 24
	},
	{
		type: "text",
		label: "参加人员",
		prop: "participants",
		height: "80px",
		span: 24
	},
	{
		type: "text",
		label: "评价/复查主要内容",
		prop: "reviewContent",
		height: "100px",
		span: 24
	},
	{
		type: "text",
		label: "备注",
		prop: "remark",
		span: 24
	},
	{
		type: "text",
		label: "制定人",
		prop: "developer",
		span: 6
	},
	{
		type: "text",
		label: "审核人",
		prop: "reviewer",
		span: 6
	},
	{
		type: "text",
		label: "批准人",
		prop: "approver",
		span: 6
	},
	{
		type: "text",
		label: "日期",
		prop: "time",
		format: value => {
			return value ? format(new Date(value), "yyyy-MM-dd") : "";
		},
		span: 6
	},
	{
		type: "text",
		label: "关联船舶",
		prop: "shipNames",
		format: value => {
			return value?.join("、");
		},
		span: 24
	},
	{
		type: "slot",
		label: "上传文件",
		prop: "files",
		span: 12,
		visible: () => showFile.value
	}
];
let previewRef = ref();
const handlePreview = (file: { url: string }) => {
	// 获取文件后缀名
	const extend = file.url ? file.url.split(".").pop()!.toLowerCase() : "";
	/** pdf和图片用预览组件 */
	if (["pdf", "jpg", "jpeg", "png"].includes(extend)) {
		return previewRef.value.open(file.url);
	}
	window.open(file.url, "_blank");
};
</script>

<style lang="scss" scoped>
.flex-box {
	display: flex;
	align-items: center;
	justify-content: space-between;
	width: 100%;
	.flex-item {
		display: flex;
		flex: 1;
		align-items: center;
	}
}
.file-list {
	::v-deep .el-upload.el-upload--text {
		display: none;
	}
}
</style>
