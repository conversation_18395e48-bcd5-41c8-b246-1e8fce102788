<!--B11-3 审核会议记录 表单详情  -->
<template>
	<div class="approval-container">
		<ApplyDetail
			:formData="formData"
			:items="tableItems"
			footer-text="注：本通知和计划由审核组长下发，通知应发于被审人员、审核员及其它必要的人员并存档，书面不方便下发时，可以用电话或传真等形式先行通知，但实施时仍需相关人员确认。"
		>
			<template #auditTime="{ content }">
				<p>
					兹定于{{
						content.auditTime
					}}，对你部门所涉及的SMS运行情况进行内部审核，现将本次《审核计划》的安排通知你们，希确定陪同人员并作好必要的准备工作（体系文件、体系活动记录、资料信息等）。
				</p>
			</template>
			<template #auditList="{ content }">
				<el-table
					border
					size="small"
					:data="content.auditList"
					style="width: calc(100% + 22px); max-width: calc(100% + 22px); margin: -8px -11px"
				>
					<el-table-column prop="no" label="审核编号" />
					<el-table-column prop="name" label="姓名" />
					<el-table-column prop="position" label="职务" />
				</el-table>
			</template>
			<template #plan="{ content }">
				<el-table
					border
					size="small"
					:data="content.plan"
					style="width: calc(100% + 22px); max-width: calc(100% + 22px); margin: -8px -11px"
				>
					<el-table-column prop="subject" label="被审对象" />
					<el-table-column prop="auditor" label="审核员" />
					<el-table-column prop="time" label="时间" />
				</el-table>
			</template>
			<template #files="{ content }">
				<div class="file-list">
					<el-upload
						v-if="content.files && content.files.length"
						class="file-list"
						action="#"
						:disabled="true"
						:file-list="content.files"
						:on-preview="handlePreview"
					>
						<i class="el-icon-plus"></i>
					</el-upload>
					<span v-else>未上传文件！</span>
				</div>
			</template>
		</ApplyDetail>
		<Preview ref="previewRef" />
	</div>
</template>

<script setup lang="ts" name="EditShip">
import { inject, ref, Ref } from "vue";
import ApplyDetail from "../components/ApplyDetail.vue";
import { DetailItem } from "../components/interface";
import Preview from "@/components/Preview/Preview.vue";

/**表格信息 */
const formData = inject("formData") as Ref<{ [key: string]: any }>;
const showFile = inject("showFile") as Ref<boolean>;

const tableItems: DetailItem[] = [
	{
		type: "text",
		label: "发至岸基/船舶",
		prop: "typeNames",
		format: (value: string[]) => {
			return value && value.join("、");
		},
		width: "25%",
		span: 12
	},
	{
		type: "text",
		label: "接收人/日期",
		prop: "receiver",
		width: "25%",
		span: 12
	},
	{
		type: "slot",
		label: "审核通知",
		prop: "auditTime",
		span: 24
	},
	{
		type: "slot",
		label: "审核成员",
		prop: "auditList",
		span: 24
	},
	{
		type: "slot",
		label: "审核计划安排",
		prop: "plan",
		span: 24
	},
	{
		type: "text",
		label: "被审核人应准备的资料",
		prop: "auditInfo",
		span: 12
	},
	{
		type: "text",
		label: "备注",
		prop: "remark",
		span: 12
	},
	{
		type: "slot",
		label: "上传文件",
		prop: "files",
		span: 24,
		visible: () => showFile.value
	}
];
let previewRef = ref();
const handlePreview = (file: { url: string }) => {
	// 获取文件后缀名
	const extend = file.url ? file.url.split(".").pop()!.toLowerCase() : "";
	/** pdf和图片用预览组件 */
	if (["pdf", "jpg", "jpeg", "png"].includes(extend)) {
		return previewRef.value.open(file.url);
	}
	window.open(file.url, "_blank");
};
</script>

<style lang="scss" scoped>
.approval-container {
	.flex-box {
		display: flex;
		align-items: center;
		justify-content: space-between;
		width: 100%;
		.flex-item {
			display: flex;
			flex: 1;
			align-items: center;
		}
	}
	.text-box {
		p {
			margin: 8px 0;
		}
	}
	.file-list {
		::v-deep .el-upload.el-upload--text {
			display: none;
		}
	}
	::v-deep .el-table__header thead tr .el-table__cell {
		background-color: #ffffff !important;
	}
	.metting-container {
		.meeting-time {
			display: flex;
			align-items: center;
			span {
				margin-right: 10px;
			}
		}
	}
}
</style>
