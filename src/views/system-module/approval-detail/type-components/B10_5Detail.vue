<!--B10-5 文件借阅登记表 详情 -->
<template>
	<div>
		<ApplyDetail :formData="formData" :items="tableItems" footer-text="注：本表一式一份，文件持有人留存。">
			<template #records="{ content }">
				<el-table
					border
					size="small"
					:data="content.records"
					style="width: calc(100% + 22px); max-width: calc(100% + 22px); margin: -8px -11px"
				>
					<el-table-column prop="colIndex" label="序号" width="200px" />
					<el-table-column prop="fileName" label="文件名称" />
					<el-table-column prop="borrowUser" label="借阅人" />
					<el-table-column prop="borrowDate" label="借阅时间" width="200px" />
					<el-table-column prop="returnDate" label="归还时间" />
					<el-table-column prop="remark" label="备注" />
				</el-table>
			</template>
			<template #files="{ content }">
				<div class="file-list">
					<el-upload
						v-if="content.files && content.files.length"
						class="file-list"
						action="#"
						:disabled="true"
						:file-list="content.files"
						:on-preview="handlePreview"
					>
						<i class="el-icon-plus"></i>
					</el-upload>
					<span v-else>未上传文件！</span>
				</div>
			</template>
		</ApplyDetail>
		<Preview ref="previewRef" />
	</div>
</template>

<script setup lang="ts" name="EditShip">
import { inject, ref, Ref } from "vue";
import ApplyDetail from "../components/ApplyDetail.vue";
import { DetailItem } from "../components/interface";
import Preview from "@/components/Preview/Preview.vue";

/**表格信息 */
const formData = inject("formData") as Ref<{ [key: string]: any }>;
const showFile = inject("showFile") as Ref<boolean>;

const tableItems: DetailItem[] = [
	{
		type: "text",
		label: "船舶/部门",
		prop: "shipDepartNames",
		format: data => data && data.join("、"),
		span: 24
	},
	{
		type: "slot",
		label: "借阅记录",
		labelAlign: "center",
		prop: "records",
		span: 24
	},
	{
		type: "slot",
		label: "上传文件",
		prop: "files",
		span: 24,
		visible: () => showFile.value
	}
];
let previewRef = ref();
const handlePreview = (file: { url: string }) => {
	// 获取文件后缀名
	const extend = file.url ? file.url.split(".").pop()!.toLowerCase() : "";
	/** pdf和图片用预览组件 */
	if (["pdf", "jpg", "jpeg", "png"].includes(extend)) {
		return previewRef.value.open(file.url);
	}
	window.open(file.url, "_blank");
};
</script>

<style lang="scss" scoped>
.flex-box {
	display: flex;
	align-items: center;
	justify-content: space-between;
	width: 100%;
	.flex-item {
		display: flex;
		flex: 1;
		align-items: center;
	}
	.flex-text {
		flex: 1;
	}
}
.file-list {
	::v-deep .el-upload.el-upload--text {
		display: none;
	}
}
</style>
