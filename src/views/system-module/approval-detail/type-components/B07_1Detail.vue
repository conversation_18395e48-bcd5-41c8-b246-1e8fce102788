<!-- B07-1 不符合规定情况、事故和险情的报告、分析和纠正记录表 申请表单 -->
<template>
	<div>
		<ApplyDetail
			:formData="formData"
			:items="tableItems"
			footer-text="注：在对适用对象前的方框中打“√”，不适用打“—”。 本表一式二份，部门/船舶一份，指定人员留存一份。"
		>
			<template #type="{ content }">
				<span>{{ content.type?.includes(1) ? "☑" : "☐" }}不符合规定情况&nbsp;</span>
				<span>{{ content.type?.includes(2) ? "☑" : "☐" }}事故&nbsp;</span>
				<span>{{ content.type?.includes(3) ? "☑" : "☐" }}险情</span>
			</template>
			<template #conclusion="{ content }">
				<span>{{ content.conclusion?.includes(1) ? "☑" : "☐" }}1、纠正措施是否按规定期限内完成</span><br />
				<span>{{ content.conclusion?.includes(2) ? "☑" : "☐" }}2、实施情况是否记录</span>
			</template>
			<template #verifier="{ content }">
				<div style="display: flex; flex-direction: column; justify-content: space-between; height: 100px">
					<span>{{ content.verifier }}</span
					><span>日期：{{ content.verifierDate }}</span>
				</div>
			</template>
			<template #files="{ content }">
				<div class="file-list">
					<el-upload
						v-if="content.files && content.files.length"
						class="file-list"
						action="#"
						:disabled="true"
						:file-list="content.files"
						:on-preview="handlePreview"
					>
						<i class="el-icon-plus"></i>
					</el-upload>
					<span v-else>未上传文件！</span>
				</div>
			</template>
		</ApplyDetail>
		<Preview ref="previewRef" />
	</div>
</template>

<script setup lang="ts" name="EditShip">
import { inject, ref, Ref } from "vue";
import ApplyDetail from "../components/ApplyDetail.vue";
import { DetailItem } from "../components/interface";
import Preview from "@/components/Preview/Preview.vue";
// import { format } from "date-fns";

/**表格信息 */
const formData = inject("formData") as Ref<{ [key: string]: any }>;
const showFile = inject("showFile") as Ref<boolean>;

const tableItems: DetailItem[] = [
	{
		type: "slot",
		label: "类别",
		prop: "type",
		width: "40%",
		span: 12,
		labelAlign: "center"
	},
	{
		type: "text",
		label: "岸基部门/船舶",
		prop: "shipDepartNames",
		width: "60%",
		format: (data: string[]) => data && data.join("、"),
		span: 12,
		labelAlign: "center"
	},
	{
		type: "text",
		label: "不符合规定情况、事故和险情简述",
		prop: "resume",
		height: "100px",
		span: 24
	},
	{
		type: "text",
		label: "已采取措施",
		prop: "measures",
		height: "100px",
		span: 24
	},
	{
		type: "text",
		label: "评审意见",
		prop: "opinions",
		height: "100px",
		span: 24
	},
	{
		type: "text",
		label: "原因分析",
		prop: "reasonAnalysis",
		height: "100px",
		span: 24
	},
	{
		type: "text",
		label: "计划采取的纠正措施",
		prop: "plannedMeasures",
		height: "100px",
		span: 24
	},
	{
		type: "text",
		label: "拟定完成日期",
		prop: "plannedDate",
		height: "100px",
		span: 24
	},
	{
		type: "text",
		label: "指定人员意见",
		prop: "personnelOpinions",
		height: "100px",
		span: 24
	},
	{
		type: "slot",
		label: "纠正措施的验证和结论",
		prop: "conclusion",
		height: "100px",
		width: "60%",
		span: 16
	},
	{
		type: "slot",
		label: "验证人",
		prop: "verifier",
		height: "100px",
		span: 8
	},
	{
		type: "slot",
		label: "上传文件",
		prop: "files",
		span: 12,
		visible: () => showFile.value
	}
];
let previewRef = ref();
const handlePreview = (file: { url: string }) => {
	// 获取文件后缀名
	const extend = file.url ? file.url.split(".").pop()!.toLowerCase() : "";
	/** pdf和图片用预览组件 */
	if (["pdf", "jpg", "jpeg", "png"].includes(extend)) {
		return previewRef.value.open(file.url);
	}
	window.open(file.url, "_blank");
};
</script>

<style lang="scss" scoped>
.flex-box {
	display: flex;
	align-items: center;
	justify-content: space-between;
	width: 100%;
	.flex-item {
		display: flex;
		flex: 1;
		align-items: center;
	}
}
.file-list {
	::v-deep .el-upload.el-upload--text {
		display: none;
	}
}
</style>
