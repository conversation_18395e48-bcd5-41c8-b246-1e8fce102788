<!--B11-3 审核会议记录 表单详情  -->
<template>
	<div class="approval-container">
		<ApplyDetail direction="horizontal" :formData="formData" :items="tableItems" footer-text="注：本表完成后由指定人员存档。">
			<template #firstMeeting="{ content }">
				<div class="metting-container" style="min-height: 120px">
					<div class="meeting-time">
						<span>开始时间：</span>
						<span>{{ content.firstStartTime }}</span>
						<span>结束时间：</span>
						<span>{{ content.firstEndTime }}</span>
					</div>
					<div>
						<span>参加人员（会签）：</span><br />
						<span>{{ content.participants }}</span>
					</div>
				</div>
			</template>
			<template #audit="{ content }">
				<div class="metting-container">
					<div class="meeting-time">
						<span>开始时间：</span>
						<span>{{ content.auditStartTime }}</span>
						<span>结束时间：</span>
						<span>{{ content.auditEndTime }}</span>
					</div>
				</div>
			</template>
			<template #lastMeeting="{ content }">
				<div class="metting-container" style="min-height: 120px">
					<div class="meeting-time">
						<span>开始时间：</span>
						<span>{{ content.lastStartTime }}</span>
						<span>结束时间：</span>
						<span>{{ content.lastEndTime }}</span>
					</div>
					<div>
						<span>参加人员（会签）：</span><br />
						<span>{{ content.lastParticipants }}</span>
					</div>
				</div>
			</template>
			<template #auditFinding="{ content }"
				><p>
					共发现问题 {{ content.probleamCount || 0 }} 个。<br />
					不符合规定情况 {{ content.notConformCount || 0 }} 个。
				</p>
			</template>
			<template #files="{ content }">
				<div class="file-list">
					<el-upload
						v-if="content.files && content.files.length"
						class="file-list"
						action="#"
						:disabled="true"
						:file-list="content.files"
						:on-preview="handlePreview"
					>
						<i class="el-icon-plus"></i>
					</el-upload>
					<span v-else>未上传文件！</span>
				</div>
			</template>
		</ApplyDetail>
		<Preview ref="previewRef" />
	</div>
</template>

<script setup lang="ts" name="EditShip">
import { inject, ref, Ref } from "vue";
import ApplyDetail from "../components/ApplyDetail.vue";
import { DetailItem } from "../components/interface";
import Preview from "@/components/Preview/Preview.vue";

/**表格信息 */
const formData = inject("formData") as Ref<{ [key: string]: any }>;
const showFile = inject("showFile") as Ref<boolean>;

const tableItems: DetailItem[] = [
	{
		type: "text",
		label: "岸基/船舶",
		prop: "typeNames",
		format: data => data && data.join("、"),
		width: "25%",
		span: 12
	},
	{
		type: "text",
		label: "审核性质",
		prop: "property",
		width: "25%",
		span: 12
	},
	{
		type: "text",
		label: "实施时间",
		prop: "effectiveDate",
		span: 12
	},
	{
		type: "text",
		label: "编号",
		prop: "no",
		span: 12
	},
	{
		type: "text",
		label: "审核组长",
		prop: "auditLeader",
		span: 12
	},
	{
		type: "text",
		label: "审核员",
		prop: "auditor",
		span: 12
	},
	{
		type: "slot",
		label: "首次会议",
		prop: "firstMeeting",
		height: "300px",
		span: 24
	},
	{
		type: "slot",
		label: "现场审核",
		prop: "audit",
		height: "300px",
		span: 24
	},
	{
		type: "text",
		label: "审核目的",
		prop: "purpose",
		span: 24
	},
	{
		type: "text",
		label: "审核依据",
		prop: "basis",
		span: 24
	},
	{
		type: "text",
		label: "审核范围",
		prop: "scope",
		span: 24
	},
	{
		type: "text",
		label: "审核方法",
		prop: "method",
		span: 24
	},
	{
		type: "text",
		label: "其它",
		prop: "other",
		span: 24
	},
	{
		type: "slot",
		label: "末次会议",
		prop: "lastMeeting",
		span: 24
	},
	{
		type: "slot",
		label: "审核结果",
		prop: "auditFinding",
		span: 24
	},
	{
		type: "slot",
		label: "上传文件",
		prop: "files",
		span: 24,
		visible: () => showFile.value
	}
];
let previewRef = ref();
const handlePreview = (file: { url: string }) => {
	// 获取文件后缀名
	const extend = file.url ? file.url.split(".").pop()!.toLowerCase() : "";
	/** pdf和图片用预览组件 */
	if (["pdf", "jpg", "jpeg", "png"].includes(extend)) {
		return previewRef.value.open(file.url);
	}
	window.open(file.url, "_blank");
};
</script>

<style lang="scss" scoped>
.approval-container {
	.flex-box {
		display: flex;
		align-items: center;
		justify-content: space-between;
		width: 100%;
		.flex-item {
			display: flex;
			flex: 1;
			align-items: center;
		}
	}
	.text-box {
		p {
			margin: 8px 0;
		}
	}
	.file-list {
		::v-deep .el-upload.el-upload--text {
			display: none;
		}
	}
	::v-deep .el-table__header thead tr .el-table__cell {
		background-color: #ffffff !important;
	}
	.metting-container {
		.meeting-time {
			display: flex;
			align-items: center;
			span {
				margin-right: 10px;
			}
		}
	}
}
</style>
