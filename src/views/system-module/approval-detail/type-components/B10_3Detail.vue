<!--B10-3 文件收发登记表 详情 -->
<template>
	<div>
		<ApplyDetail
			:formData="formData"
			:items="tableItems"
			footer-text="文件类别：1、SMS文件；2、规范规则、守则等；3、海图；4、航海通告；5、航海出版物；6、其他文件。
注： 本表一式一份，SMS办保存。"
		>
			<template #records="{ content }">
				<el-table
					border
					size="small"
					:data="content.records"
					style="width: calc(100% + 22px); max-width: calc(100% + 22px); margin: -8px -11px"
				>
					<el-table-column prop="colIndex" label="序号" />
					<el-table-column prop="sendDate" label="时间" />
					<el-table-column prop="nameType" label="文件名称、类别" />
					<el-table-column prop="sendUser" label="发放部门/人员" />
					<el-table-column prop="count" label="份数" />
					<el-table-column prop="receiveUser" label="接收部门/人员" />
					<el-table-column prop="sendMethod" label="发放方式" />
					<el-table-column prop="remark" label="备注" />
				</el-table>
			</template>
			<template #files="{ content }">
				<div class="file-list">
					<el-upload
						v-if="content.files && content.files.length"
						class="file-list"
						action="#"
						:disabled="true"
						:file-list="content.files"
						:on-preview="handlePreview"
					>
						<i class="el-icon-plus"></i>
					</el-upload>
					<span v-else>未上传文件！</span>
				</div>
			</template>
		</ApplyDetail>
		<Preview ref="previewRef" />
	</div>
</template>

<script setup lang="ts" name="EditShip">
import { inject, ref, Ref } from "vue";
import ApplyDetail from "../components/ApplyDetail.vue";
import { DetailItem } from "../components/interface";
import Preview from "@/components/Preview/Preview.vue";

/**表格信息 */
const formData = inject("formData") as Ref<{ [key: string]: any }>;
const showFile = inject("showFile") as Ref<boolean>;

const tableItems: DetailItem[] = [
	{
		type: "slot",
		label: "",
		// labelAlign: "center",
		prop: "records",
		span: 24
	},
	{
		type: "slot",
		label: "上传文件",
		prop: "files",
		span: 24,
		visible: () => showFile.value
	}
];
let previewRef = ref();
const handlePreview = (file: { url: string }) => {
	// 获取文件后缀名
	const extend = file.url ? file.url.split(".").pop()!.toLowerCase() : "";
	/** pdf和图片用预览组件 */
	if (["pdf", "jpg", "jpeg", "png"].includes(extend)) {
		return previewRef.value.open(file.url);
	}
	window.open(file.url, "_blank");
};
</script>

<style lang="scss" scoped></style>
