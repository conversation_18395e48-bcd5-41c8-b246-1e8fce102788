<!-- 申请表单 -->
<template>
	<div>
		<ApplyDetail :formData="formData" :items="tableItems" footer-text="注：本表一式一份，完成后SMS办留存。">
			<template #fileType="{ content }">
				<div class="flex-box">
					<div class="flex-item">
						外来文件：
						<span>{{ content.externalFileType?.includes(1) ? "☑" : "☐" }}强制性&nbsp;</span>
						<span>{{ content.externalFileType?.includes(2) ? "☑" : "☐" }}建议性</span>
					</div>
					<div class="flex-item">
						非外来文件：
						<span>{{ content.nonExternalFileType?.includes(1) ? "☑" : "☐" }}资料&nbsp;</span>
						<span>{{ content.nonExternalFileType?.includes(2) ? "☑" : "☐" }}信息</span>
					</div>
				</div></template
			>
			<template #files="{ content }">
				<div class="file-list">
					<el-upload
						v-if="content.files && content.files.length"
						class="file-list"
						action="#"
						:disabled="true"
						:file-list="content.files"
						:on-preview="handlePreview"
					>
						<i class="el-icon-plus"></i>
					</el-upload>
					<span v-else>未上传文件！</span>
				</div>
			</template>
		</ApplyDetail>
		<Preview ref="previewRef" />
	</div>
</template>

<script setup lang="ts" name="EditShip">
import { inject, ref, Ref } from "vue";
import ApplyDetail from "../components/ApplyDetail.vue";
import { DetailItem } from "../components/interface";
import Preview from "@/components/Preview/Preview.vue";
import { format } from "date-fns";

/**表格信息 */
const formData = inject("formData") as Ref<{ [key: string]: any }>;
const showFile = inject("showFile") as Ref<boolean>;

const tableItems: DetailItem[] = [
	{
		type: "text",
		label: "文件名称",
		prop: "fileName",
		width: "70%",
		height: "80px",
		span: 16,
		labelAlign: "center"
	},
	{
		type: "text",
		label: "颁发机构",
		prop: "authority",
		span: 4,
		labelAlign: "center"
	},
	{
		type: "text",
		label: "生效日期",
		prop: "effectiveDate",
		format: (value: string) => {
			return value ? format(new Date(value), "yyyy-MM-dd") : "";
		},
		span: 4,
		labelAlign: "center"
	},
	{
		type: "slot",
		label: "文件类别",
		prop: "fileType",
		span: 24
	},
	{
		type: "text",
		label: "文件主要内容概述",
		prop: "content",
		height: "150px",
		span: 24
	},
	{
		type: "text",
		label: "收集人建议",
		prop: "suggestion",
		height: "100px",
		span: 24
	},
	{
		type: "text",
		label: "相关部门会签意见",
		prop: "signUser",
		height: "100px",
		span: 24
	},
	{
		type: "text",
		label: "确认文件类别",
		prop: "fileType",
		width: "50%",
		span: 12
	},
	{
		type: "text",
		label: "发放范围",
		prop: "scope",
		width: "50%",
		span: 12
	},
	{
		type: "slot",
		label: "上传文件",
		prop: "files",
		span: 12,
		visible: () => showFile.value
	}
];
let previewRef = ref();
const handlePreview = (file: { url: string }) => {
	// 获取文件后缀名
	const extend = file.url ? file.url.split(".").pop()!.toLowerCase() : "";
	/** pdf和图片用预览组件 */
	if (["pdf", "jpg", "jpeg", "png"].includes(extend)) {
		return previewRef.value.open(file.url);
	}
	window.open(file.url, "_blank");
};
</script>

<style lang="scss" scoped>
.flex-box {
	display: flex;
	align-items: center;
	justify-content: space-between;
	width: 100%;
	.flex-item {
		display: flex;
		flex: 1;
		align-items: center;
	}
}
.file-list {
	::v-deep .el-upload.el-upload--text {
		display: none;
	}
}
</style>
