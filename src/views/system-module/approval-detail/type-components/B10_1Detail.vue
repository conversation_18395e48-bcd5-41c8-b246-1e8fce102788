<!--B11-4 审核实施记录 表单详情  -->
<template>
	<div class="approval-container">
		<ApplyDetail :formData="formData" :items="tableItems" footer-text="注：本表一式一份，完成后SMS办留存。">
			<template #auditee="{ content }">
				<el-table
					border
					size="small"
					:data="content.auditee"
					style="width: calc(100% + 22px); max-width: calc(100% + 22px); margin: -8px -11px"
				>
					<el-table-column type="index" label="序号" />
					<el-table-column prop="position" label="职务" />
					<el-table-column prop="name" label="姓名" />
				</el-table>
			</template>
			<template #files="{ content }">
				<div class="file-list">
					<el-upload
						v-if="content.files && content.files.length"
						class="file-list"
						action="#"
						:disabled="true"
						:file-list="content.files"
						:on-preview="handlePreview"
					>
						<i class="el-icon-plus"></i>
					</el-upload>
					<span v-else>未上传文件！</span>
				</div>
			</template>
		</ApplyDetail>
		<Preview ref="previewRef" />
	</div>
</template>

<script setup lang="ts" name="EditShip">
import { inject, ref, Ref } from "vue";
import ApplyDetail from "../components/ApplyDetail.vue";
import { DetailItem } from "../components/interface";
import Preview from "@/components/Preview/Preview.vue";

/**表格信息 */
const formData = inject("formData") as Ref<{ [key: string]: any }>;
const showFile = inject("showFile") as Ref<boolean>;

const tableItems: DetailItem[] = [
	{
		type: "text",
		label: "编号",
		prop: "no",
		span: 5,
		width: "20%",
		labelAlign: "center"
	},
	{
		type: "text",
		label: "文件名称",
		prop: "fileName",
		span: 5,
		width: "20%",
		labelAlign: "center"
	},
	{
		type: "text",
		label: "编号（版次）",
		prop: "version",
		span: 5,
		width: "20%",
		labelAlign: "center"
	},
	{
		type: "text",
		label: "拟修改页码",
		prop: "pageNumber",
		span: 5,
		width: "20%",
		labelAlign: "center"
	},
	{
		type: "text",
		label: "申请部门",
		prop: "department",
		span: 4,
		width: "20%",
		labelAlign: "center"
	},
	{
		type: "text",
		label: "更改前内容",
		prop: "contentBefore",
		span: 12,
		height: "300px",
		labelAlign: "center"
	},
	{
		type: "text",
		label: "更改后内容",
		prop: "contentAfter",
		span: 12,
		height: "300px",
		labelAlign: "center"
	},
	{
		type: "text",
		label: "申请更改原因",
		prop: "reason",
		span: 24
	},
	{
		type: "text",
		label: "更改审核意见",
		prop: "advice",
		span: 24
	},
	{
		type: "text",
		label: "批准",
		prop: "approve",
		span: 24
	},
	{
		type: "text",
		label: "备注",
		prop: "remark",
		span: 24
	},
	{
		type: "slot",
		label: "上传文件",
		prop: "files",
		span: 24,
		visible: () => showFile.value
	}
];
let previewRef = ref();
const handlePreview = (file: { url: string }) => {
	// 获取文件后缀名
	const extend = file.url ? file.url.split(".").pop()!.toLowerCase() : "";
	/** pdf和图片用预览组件 */
	if (["pdf", "jpg", "jpeg", "png"].includes(extend)) {
		return previewRef.value.open(file.url);
	}
	window.open(file.url, "_blank");
};
</script>

<style lang="scss" scoped>
.approval-container {
	.flex-box {
		display: flex;
		align-items: center;
		justify-content: space-between;
		width: 100%;
		.flex-item {
			display: flex;
			flex: 1;
			align-items: center;
		}
	}
	.text-box {
		p {
			margin: 8px 0;
		}
	}
	.file-list {
		::v-deep .el-upload.el-upload--text {
			display: none;
		}
	}
	::v-deep .el-table__header thead tr .el-table__cell {
		background-color: #ffffff !important;
	}
}
</style>
