<!--B10-6 文件回收、作废处理单 详情 -->
<template>
	<div>
		<ApplyDetail
			:formData="formData"
			:items="tableItems"
			footer-text="注：1、本表一式一份，完成后SMS办主管保存。 2、□ 内适用划“√”，不适用划“x”。"
		>
			<template #method="{ content }">
				<div class="flex-box">
					<div class="flex-item">
						<span>{{ content.method?.includes(1) ? "☑" : "☐" }}回收&nbsp;</span>
						<span>{{ content.method?.includes(2) ? "☑" : "☐" }}保存&nbsp;</span>
						<span>{{ content.method?.includes(3) ? "☑" : "☐" }}作废&nbsp;</span>
						<span>{{ content.method?.includes(4) ? "☑" : "☐" }}销毁</span>
					</div>
				</div>
			</template>
			<template #fileRecycle="{ content }">
				<el-table
					border
					size="small"
					:data="content.fileRecycle"
					style="width: calc(100% + 22px); max-width: calc(100% + 22px); margin: -8px -11px"
				>
					<el-table-column prop="belong" label="原保存部门/船舶/人员" width="200px" />
					<el-table-column prop="count" label="数量" />
					<el-table-column prop="date" label="回收日期" />
					<el-table-column prop="belongSecond" label="原保存部门/船舶/人员" width="200px" />
					<el-table-column prop="countSecond" label="数量" />
					<el-table-column prop="dateSecond" label="回收日期" />
				</el-table>
			</template>
			<template #destoryInfo="{ content }">
				<div class="flex-box">
					<div class="flex-text">
						销毁日期： {{ content.destoryDate ? format(new Date(content.destoryDate), "yyyy-MM-dd") : "" }}
					</div>
					<div class="flex-text">销毁方法： {{ content.destoryMethod }}</div>
					<div class="flex-text">销毁人： {{ content.destoryPerson }}</div>
					<div class="flex-text">验证人：{{ content.verifyPerson }}</div>
				</div>
			</template>
			<template #files="{ content }">
				<div class="file-list">
					<el-upload
						v-if="content.files && content.files.length"
						class="file-list"
						action="#"
						:disabled="true"
						:file-list="content.files"
						:on-preview="handlePreview"
					>
						<i class="el-icon-plus"></i>
					</el-upload>
					<span v-else>未上传文件！</span>
				</div>
			</template>
		</ApplyDetail>
		<Preview ref="previewRef" />
	</div>
</template>

<script setup lang="ts" name="EditShip">
import { inject, ref, Ref } from "vue";
import ApplyDetail from "../components/ApplyDetail.vue";
import { DetailItem } from "../components/interface";
import Preview from "@/components/Preview/Preview.vue";
import { format } from "date-fns";

/**表格信息 */
const formData = inject("formData") as Ref<{ [key: string]: any }>;
const showFile = inject("showFile") as Ref<boolean>;

const tableItems: DetailItem[] = [
	{
		type: "text",
		label: "申请作废文件名称",
		prop: "fileName",
		width: "50%",
		span: 12,
		labelAlign: "center"
	},
	{
		type: "slot",
		label: "处理方式",
		prop: "method",
		span: 12,
		labelAlign: "center"
	},
	{
		type: "text",
		label: "申请原因",
		prop: "reason",
		span: 24
	},
	{
		type: "text",
		label: "指定人员审核意见",
		prop: "reviewComments",
		span: 24
	},
	{
		type: "slot",
		label: "文件回收登记",
		labelAlign: "center",
		prop: "fileRecycle",
		span: 24
	},
	{
		type: "slot",
		label: "销毁信息",
		prop: "destoryInfo",
		span: 24
	},
	{
		type: "text",
		label: "备注",
		prop: "remark",
		height: "120px",
		span: 24
	},
	{
		type: "slot",
		label: "上传文件",
		prop: "files",
		span: 12,
		visible: () => showFile.value
	}
];
let previewRef = ref();
const handlePreview = (file: { url: string }) => {
	// 获取文件后缀名
	const extend = file.url ? file.url.split(".").pop()!.toLowerCase() : "";
	/** pdf和图片用预览组件 */
	if (["pdf", "jpg", "jpeg", "png"].includes(extend)) {
		return previewRef.value.open(file.url);
	}
	window.open(file.url, "_blank");
};
</script>

<style lang="scss" scoped>
.flex-box {
	display: flex;
	align-items: center;
	justify-content: space-between;
	width: 100%;
	.flex-item {
		display: flex;
		flex: 1;
		align-items: center;
	}
	.flex-text {
		flex: 1;
	}
}
.file-list {
	::v-deep .el-upload.el-upload--text {
		display: none;
	}
}
</style>
