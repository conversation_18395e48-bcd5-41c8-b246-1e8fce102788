<!--B10-2 文件修改通知单 详情 -->
<template>
	<div>
		<ApplyDetail :formData="formData" :items="tableItems" footer-text="注： 本表一式多份，一份通知部门/人员留存，一份SMS办留存。">
			<template #notices="{ content }">
				<el-table
					border
					size="small"
					:data="content.notices"
					style="width: calc(100% + 22px); max-width: calc(100% + 22px); margin: -8px -11px"
				>
					<el-table-column prop="noticeTo" label="通知部门/人员" />
					<el-table-column prop="noticeDate" label="通知日期/方式" />
					<el-table-column prop="sign" label="签名" />
				</el-table>
			</template>
			<template #modifyList="{ content }">
				<el-table
					border
					size="small"
					:data="content.modifyList"
					style="width: calc(100% + 22px); max-width: calc(100% + 22px); margin: -8px -11px"
				>
					<el-table-column prop="colIndex" label="序号" />
					<el-table-column prop="fileName" label="文件名称" />
					<el-table-column prop="version" label="编号(版次)" />
					<el-table-column prop="pageNumber" label="修改页面" />
					<el-table-column prop="effectiveDate" label="生效日期" />
					<el-table-column prop="remark" label="备注" />
				</el-table>
			</template>
			<template #files="{ content }">
				<div class="file-list">
					<el-upload
						v-if="content.files && content.files.length"
						class="file-list"
						action="#"
						:disabled="true"
						:file-list="content.files"
						:on-preview="handlePreview"
					>
						<i class="el-icon-plus"></i>
					</el-upload>
					<span v-else>未上传文件！</span>
				</div>
			</template>
		</ApplyDetail>
		<Preview ref="previewRef" />
	</div>
</template>

<script setup lang="ts" name="EditShip">
import { inject, ref, Ref } from "vue";
import ApplyDetail from "../components/ApplyDetail.vue";
import { DetailItem } from "../components/interface";
import Preview from "@/components/Preview/Preview.vue";

/**表格信息 */
const formData = inject("formData") as Ref<{ [key: string]: any }>;
const showFile = inject("showFile") as Ref<boolean>;

const tableItems: DetailItem[] = [
	{
		type: "slot",
		label: "",
		labelAlign: "center",
		prop: "notices",
		span: 24
	},
	{
		type: "text",
		label: "文件更改原因",
		height: "120px",
		prop: "changeReason",
		span: 24
	},
	{
		type: "slot",
		label: "",
		labelAlign: "center",
		prop: "modifyList",
		span: 24
	},
	{
		type: "text",
		label: "更改前内容",
		prop: "contentBefore",
		height: "200px",
		span: 12
	},
	{
		type: "text",
		label: "更改后内容",
		prop: "contentAfter",
		height: "200px",
		span: 12
	},
	{
		type: "text",
		label: "关联船舶",
		prop: "shipNames",
		format: data => data && data.join("、"),
		span: 24
	},
	{
		type: "slot",
		label: "上传文件",
		prop: "files",
		span: 24,
		visible: () => showFile.value
	}
];
let previewRef = ref();
const handlePreview = (file: { url: string }) => {
	// 获取文件后缀名
	const extend = file.url ? file.url.split(".").pop()!.toLowerCase() : "";
	/** pdf和图片用预览组件 */
	if (["pdf", "jpg", "jpeg", "png"].includes(extend)) {
		return previewRef.value.open(file.url);
	}
	window.open(file.url, "_blank");
};
</script>

<style lang="scss" scoped></style>
