<!-- 申请详情页 -->
<template>
	<Layout :title="mode === 'detail' ? '申请详情' : '体系审批'" :can-back="true" page-size="1132px">
		<template #body v-if="!loading">
			<component
				v-if="formData?.type&&oprCom[formData?.type as ApplyType]"
				:is="oprCom[formData?.type as ApplyType]"
				ref="infoRef"
				:mode="mode"
			/>
			<template v-else>
				<div>未找到对应的组件</div>
			</template>
		</template>
	</Layout>
</template>
<script setup lang="ts">
import { ref, onMounted, provide } from "vue";
import { useRoute } from "vue-router";
import Layout from "@/meta-components/MetaLayout/SinglePage.vue";
import { ApplyService, ApprovalService } from "@/api/modules/system";
import { ApplyType, Apply } from "@/api/interface/system.model";
import B03_1 from "./type-components/B03_1Detail.vue";
import B06_5 from "./type-components/B06_5Detail.vue";
import B07_1 from "./type-components/B07_1Detail.vue";
import B10_1 from "./type-components/B10_1Detail.vue";
import B10_2 from "./type-components/B10_2Detail.vue";
import B10_3 from "./type-components/B10_3Detail.vue";
import B10_4 from "./type-components/B10_4Detail.vue";
import B10_5 from "./type-components/B10_5Detail.vue";
import B10_6 from "./type-components/B10_6Detail.vue";
import C07_1 from "./type-components/C07_1Detail.vue";
import B12_1 from "./type-components/B12_1Detail.vue";
import B11_4 from "./type-components/B11_4Detail.vue";
import B11_3 from "./type-components/B11_3Detail.vue";
import B11_2 from "./type-components/B11_2Detail.vue";

const route = useRoute();
// 详情页mode通过params传递，默认detail
const mode = route.params.mode === "approval" ? "approval" : "detail";
const formData = ref<Apply.Detail>();
provide("formData", formData);
const oprCom: { [key: string]: any } = {
	"B10-4": B10_4,
	"B03-1": B03_1,
	"B06-5": B06_5,
	"B07-1": B07_1,
	"B10-1": B10_1,
	"B10-2": B10_2,
	"B10-3": B10_3,
	"B10-5": B10_5,
	"B10-6": B10_6,
	"B11-2": B11_2,
	"B11-3": B11_3,
	"B11-4": B11_4,
	"B12-1": B12_1,
	"C07-1": C07_1
};
const loading = ref(false);
onMounted(() => {
	loading.value = true;
	ApplyService.detail({
		uuid: route.query.uuid as string
	}).then(res => {
		if (res.data) {
			const data = res.data;
			formData.value = data;
			loading.value = false;
			if (Boolean(route.params.fromApproval) === true) {
				// 从审批页面跳转过来的，需要已读该申请项
				ApprovalService.read({
					applyId: data.id,
					approvalType: data.approvalType
				}).then(res => {
					if (res.code !== 0) return;
				});
			}
		}
	});
});
</script>
<style scoped lang="scss"></style>
