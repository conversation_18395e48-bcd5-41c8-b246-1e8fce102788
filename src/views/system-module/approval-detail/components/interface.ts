/** 申请详情表格组件配置项 */
export interface DetailItem {
	/**类别  text文本展示 slot插槽展示#prop */
	type: "text" | "slot";
	/** 字段文本 */
	label: string;
	/** 数值转换 非必填 传递字段值 调用转换方法后进行展示 仅对text类型有效 */
	format?: (arg: any) => string;
	/** 字段名 */
	prop: string;
	/** 单元格宽度 非必填 */
	width?: string;
	/** 单元格高度 非必填 */
	height?: string;
	/** 占用栅格数量 总共24格 */
	span: number;
	/** 表头对齐方式 非必填 默认left*/
	labelAlign?: "center" | "left" | "right";
	/** 是否显示 判断 */
	visible?: (content: { [key: string]: any }) => boolean;
}
