<!-- 详情表格页面布局 -->
<template>
	<div class="table-container">
		<!-- 表格标题 -->
		<div class="table-header-container">
			<div class="title">
				{{ formData.typeName }}
			</div>
			<div class="title-tips">
				<div class="title-tips-item">关键词：{{ formData.keyword }}</div>
				<div class="title-tips-item">申请人：{{ formData.corpUserName }}</div>
				<div class="title-tips-item">申请时间：{{ formData.applyTime }}</div>
			</div>
		</div>
		<!-- 表格内容 -->
		<div class="table-content">
			<el-descriptions :column="24" :border="true" style="width: 100%" :direction="direction">
				<el-descriptions-item
					v-for="item in items.filter(item => {
						if (typeof item.visible === 'function') {
							return item.visible(content[item.prop]);
						}
						return true;
					})"
					:label="item.label"
					:key="item.prop"
					:width="item.width"
					:span="item.span"
					:label-align="item.labelAlign"
					:label-class-name="item.label === '' ? 'display-none' : ''"
				>
					<template v-if="item.type === 'slot'">
						<slot :name="item.prop" :content="content"></slot>
					</template>
					<td v-else :style="{ height: item.height ?? '48px', 'vertical-align': 'middle', 'white-space': 'pre-wrap' }">
						{{ item.format ? item.format(content[item.prop]) : content[item.prop] }}
					</td>
				</el-descriptions-item>
			</el-descriptions>
			<div class="footer-text">{{ footerText }}</div>
		</div>
		<!-- 审批流程 -->
		<Approval v-if="showApproval" :approvalDatas="formData.approvals" :formData="formData" />
		<div style="height: 10px"></div>
	</div>
</template>

<script setup lang="ts" name="EditShip">
import { onMounted, PropType, inject, Ref, computed } from "vue";
import { DetailItem } from "./interface";
import Approval from "./Approval.vue";
// import { Apply } from "@/api/interface/system.model";
// import { Course } from "@/api/interface/course";
// import { ElMessageBox } from "element-plus";
// import { useRouter, useRoute } from "vue-router";

/** 显示流程图 */
const showApproval = inject("showApproval") as Ref<boolean>;
const props = defineProps({
	formData: {
		type: Object,
		default: () => ({})
	},
	items: {
		type: Array as PropType<DetailItem[]>,
		default: () => []
	},
	footerText: {
		type: String
	},
	direction: {
		type: String,
		default: () => "vertical"
	}
});
const content = computed(() => {
	return props.formData.content && JSON.parse(props.formData.content);
});
// const content = props.formData.content && JSON.parse(props.formData.content);

onMounted(() => {});
</script>

<style lang="scss" scoped>
.table-container {
	.title {
		width: 100%;
		margin-bottom: 4px;
		font-family: serif;
		font-size: 24px;
		font-weight: bold;
		text-align: center;
	}
	.title-tips {
		display: flex;
		align-items: center;
		justify-content: space-between;
		width: 100%;
		margin-bottom: 4px;
		font-family: serif;
		font-size: 14px;
		.title-tips-item {
			margin-left: 24px;
			white-space: nowrap;
			&:first-child {
				flex: 3;
				margin-left: 0;
			}
		}
	}
	.footer-text {
		font-family: serif;
		font-size: 14px;
	}
	.table-content {
		margin-bottom: 12px;
		::v-deep .el-descriptions__body .el-descriptions__table .el-descriptions__cell {
			font-family: serif;
			font-size: 15px;
		}
	}
	::v-deep .el-upload-list__item-status-label {
		display: none;
	}
	::v-deep .el-descriptions__label.display-none {
		display: none;
	}
	::v-deep .file-list {
		.el-upload.el-upload--text {
			display: none;
		}
	}
}
</style>
