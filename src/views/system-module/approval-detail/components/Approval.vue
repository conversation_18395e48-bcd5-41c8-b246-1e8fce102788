<!-- 审批流程组件 -->
<template>
	<div>
		<div class="approval-container">
			<span class="el-form-item__label" style="font-size: 14px">审批流程图</span>
			<div class="approval-items">
				<div v-for="(item, index) in transFormApprovals" :key="item.type" class="approval-item-container">
					<div class="approval-item">
						<div
							:class="{
								'approval-header': true,
								processing: item.status === Approval.ApprovalStatus.WAITING,
								reject: item.status === Approval.ApprovalStatus.DISAGREE
							}"
						>
							<!-- 待审批 -->
							<img
								src="@/assets/icons/sandy-clock.png"
								v-if="item.status === Approval.ApprovalStatus.WAITING"
								style="width: 14px"
							/>
							<!-- 未通过 -->
							<el-icon v-else-if="item.status === Approval.ApprovalStatus.DISAGREE">
								<CircleCloseFilled />
							</el-icon>
							<!-- 已通过 -->
							<el-icon v-else>
								<SuccessFilled />
							</el-icon>
							{{ item.type }}
						</div>
						<div class="approval-content">
							<el-tooltip placement="top" effect="light">
								<div>
									<p>{{ item.corpUserName }}</p>
									<p v-if="item.approvalTime">{{ item.approvalTime }}</p>
									<p v-if="item.advice">审批意见：{{ item.advice }}</p>
								</div>
								<template #content>
									<div style="width: 200px">
										<p>{{ item.corpUserName }}</p>
										<p v-if="item.approvalTime">{{ item.approvalTime }}</p>
										<p v-if="item.advice">审批意见：{{ item.advice }}</p>
									</div>
								</template>
							</el-tooltip>
						</div>
					</div>
					<!-- 添加连接线，最后一个 item 不显示 -->
					<div v-if="index < transFormApprovals.length - 1" class="approval-line"></div>
				</div>
			</div>
		</div>
		<div v-if="canOperate" class="approval-form">
			<MetaForm mode="add" ref="form" :formConfig="formConfig" :styleConfig="{ labelWidth: '80px', labelPosition: 'top' }">
				<template #button>
					<el-button @click="router.back"> 取消 </el-button>
					<el-popconfirm title="确认驳回？" placement="top" @confirm="approval(Approval.ApprovalStatus.DISAGREE)">
						<template #reference>
							<el-button type="warning"> 驳回 </el-button>
						</template>
					</el-popconfirm>
					<el-popconfirm title="确认同意？" placement="top" @confirm="approval(Approval.ApprovalStatus.AGREE)">
						<template #reference>
							<el-button type="primary"> 同意 </el-button>
						</template>
					</el-popconfirm>
				</template>
			</MetaForm>
		</div>
	</div>
</template>
<script setup lang="ts">
import { PropType, ref, watch } from "vue";
import { Apply, Approval, ApprovalText, ApprovalType } from "@/api/interface/system.model";
import MetaForm from "@/meta-components/MetaForm/index.vue";
import { FormConfigOptions, FormItemOptions } from "@/meta-components/MetaForm/interface";
import { useRouter, useRoute } from "vue-router";
import { ApprovalService } from "@/api/modules/system";
import { ElMessage } from "element-plus";
const router = useRouter();
const route = useRoute();
const canOperate = route.params.mode === "approval";

const props = defineProps({
	// 审批数据
	approvalDatas: {
		type: Array as PropType<Apply.ApplyApprovalInfo[]>,
		default: () => []
	},
	// 当前审批状态
	currentApproval: {
		type: Number,
		default: 0
	},
	// 完整表单数据
	formData: {
		type: Object,
		default: () => ({})
	}
});
interface ApprovalItem {
	type: string;
	corpUserName: string;
	approvalTime?: string;
	advice?: string;
	status?: Approval.ApprovalStatus;
}
/** 审批流数据合并转换 */
const transFormApprovals = ref<ApprovalItem[]>([]);
watch(
	props.approvalDatas,
	() => {
		const approvalMap = props.approvalDatas.reduce((map, item) => {
			if (item.type) {
				if (!map[item.type]) {
					map[item.type] = [];
				}
				map[item.type].push(item);
			}
			return map;
		}, {} as Record<number, Apply.ApplyApprovalInfo[]>);

		// 转换流程数组
		const process: ApprovalItem[] = Object.entries(approvalMap).map(([type, items]) => {
			return {
				type: ApprovalText[Number(type)],
				corpUserName: items.map((i: any) => i.corpUserName).join("、"),
				approvalTime: items[0].approvalTime,
				advice: items[0].advice,
				status: items[0].status
			};
		});
		process.unshift({
			type: "提交申请",
			corpUserName: props.formData.corpUserName,
			approvalTime: props.formData.applyTime,
			status: Approval.ApprovalStatus.AGREE
		});
		transFormApprovals.value = process;
	},
	{
		immediate: true
	}
);
const formContent: FormItemOptions[] = [
	{
		label: "审批意见",
		type: "textarea",
		name: "advice",
		span: 24,
		otherAttr: {
			max: 5000,
			showWordLimit: false
		}
	}
];
const formConfig: FormConfigOptions = {
	key: "system-approval",
	items: formContent,
	// successCallBack: handleSuccess,
	// beforeSubmit: beforeSubmit,
	detailKey: "uuid",
	closeFunction: close,
	closeFormAt: "both",
	hintMessage: ["审批完成", "审批完成"]
};
const form = ref();
const approval = (type: Approval.ApprovalStatus) => {
	const params = {
		uuid: route.query.uuid as string,
		approvalType: props.formData?.approvalType as ApprovalType,
		advice: form.value?.formData?.advice,
		status: type
	} as Approval.ApprovalReq;
	// 审批后返回上一页
	ApprovalService.approval(params)
		.then(res => {
			if (res.code === 0) {
				ElMessage.success("审批成功");
				setTimeout(() => {
					router.back();
				}, 1000);
			}
		})
		.catch(() => {
			setTimeout(() => {
				router.back();
			}, 1000);
		});
};
</script>
<style lang="scss" scoped>
.approval-container {
	margin: 24px 0 12px;
	.approval-items {
		display: flex;
		align-items: center;
		width: 50vw;
		min-width: 1100px;
		height: 120px;
		padding-left: 32px;
		margin-top: 8px;
		background: #f6f7fb;
		.approval-item-container {
			display: flex;
			align-items: center;
			.approval-item {
				width: 180px;
				height: 90px;
				background: #ffffff;
				border-radius: 6px;
				.approval-header {
					padding: 4px;
					font-size: 14px;
					color: #6bbf4a;
					background: #ddf0d4;
					border-radius: 6px 6px 0 0;
					&.processing {
						color: #3e79f2;
						background: #cee0f9;
					}
					&.reject {
						color: #f56c6c;
						background: #fdeeee;
					}
				}
				.approval-content {
					padding: 4px 8px;
					font-size: 14px;
					color: #616161;
					p {
						margin: 0;
						overflow: hidden;
						text-overflow: ellipsis;
						white-space: nowrap;
					}
				}
			}
			.approval-line {
				width: 40px;
				height: 2px;
				margin-top: 4px;
				background: #dcdcdc;
			}
		}
	}
}
</style>
