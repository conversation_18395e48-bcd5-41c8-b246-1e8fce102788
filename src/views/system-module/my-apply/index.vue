<!--我的申请列表-->
<template>
	<div class="page-container">
		<div style="height: 100%">
			<TableLayout ref="myApplyTable" title="我的申请" :table-config="tableConfig" :filter-config="filterConfig">
				<template #button>
					<AddBtn :btn-config="addBtnConfig" />
				</template>
				<template #depart="{ row }">
					{{ row.depart && row.depart.length ? row.depart.map((i:any) => i.name).join("、") : "-" }}
				</template>
				<template #status="{ row }">
					<div v-if="row.status === Apply.Status.UNSUBMITTED">
						<el-tag type="primary">未提交</el-tag>
					</div>
					<div v-if="row.status === Apply.Status.APPROVING">
						<el-tag type="warning">审批中</el-tag>
					</div>
					<div v-if="row.status === Apply.Status.DISAGREE">
						<el-tag type="danger">不同意</el-tag>
					</div>
					<div v-if="row.status === Apply.Status.AGREE">
						<el-tag type="success">同意</el-tag>
					</div>
				</template>
				<template #readStatus="{ row }">
					<div v-if="row.readStatus === YN.Yes">
						<el-tag type="success">已阅</el-tag>
					</div>
					<div v-if="row.readStatus === YN.No">
						<el-tag type="warning">未阅</el-tag>
					</div>
				</template>
				<template #isRead="{ row }">
					{{ row.isRead === YN.Yes ? "已阅" : row.isRead === YN.No ? "未阅" : "-" }}
				</template>
				<template #keyword="{ row }">
					<el-input
						v-if="inEditCell?.fieldName === 'keyword' && inEditCell?.row.id === row.id"
						v-model="row.keyword"
						ref="keywordInputRef"
						@blur="handleSave(row)"
						@keydown.enter="onEnter"
						:style="{ width: '100%' }"
					/>
					<div v-else style="cursor: pointer" @dblclick="handleEdit(row, 'keyword')">
						<span v-if="!row.keyword || row.keyword === ''" style="color: #e6e6e6">双击修改</span>
						<span v-else>{{ row.keyword }}</span>
					</div>
				</template>
				<template #timeLabel="{ row }">
					<div style="cursor: pointer" @dblclick="handleEdit(row, 'timeLabel')">
						<span v-if="!row.timeLabel || row.timeLabel === ''" style="color: #e6e6e6">双击修改</span>
						<span v-else>{{ row.timeLabel }}</span>
					</div>
				</template>
				<template #opr="scope">
					<el-link
						v-if="hasAuth('申请详情') && scope.row.status !== Apply.Status.UNSUBMITTED"
						style="margin-right: 12px"
						:underline="false"
						type="primary"
						@click="jumpTo('detail', scope.row)"
						>查看详情</el-link
					>
					<el-link
						v-if="hasAuth('申请编辑') && scope.row.status === Apply.Status.UNSUBMITTED"
						style="margin-right: 12px"
						:underline="false"
						type="primary"
						@click="jumpTo('edit', scope.row)"
						>编辑</el-link
					>
					<div v-if="hasAuth('申请删除') && scope.row.status === Apply.Status.UNSUBMITTED">
						<OprDelete @on-delete="handleBtnDelete(scope.row, Apply.OperateType.DELETE)"></OprDelete>
					</div>
					<!-- 流程按钮 -->
					<el-link
						v-if="hasAuth('再次申请') && scope.row.status === Apply.Status.DISAGREE"
						style="margin-right: 12px"
						:underline="false"
						type="primary"
						@click="jumpTo('resubmit', scope.row)"
						>再次申请</el-link
					>
					<div v-if="hasAuth('申请撤回') && scope.row.status === Apply.Status.APPROVING">
						<OprDelete
							title="确定撤回？"
							@on-delete="handleBtnDelete(scope.row, Apply.OperateType.RECALL)"
							btnText="撤回"
						></OprDelete>
					</div>
				</template>
			</TableLayout>
			<TimeTagModal
				:formData="inEditCell?.row"
				:visible="inEditCell?.fieldName === 'timeLabel'"
				@on-save="handleSave"
				@update:visible="updateVisible"
			/>
		</div>
	</div>
</template>

<script setup lang="ts" name="MyApply">
import TableLayout from "@/meta-components/MetaTable/index.vue";
import { ref, reactive, onMounted, nextTick } from "vue";
import {
	ColumnProps,
	FilterConfigOptions,
	TableConfig
	// TabsConfig
} from "@/meta-components/MetaTable/interface";
import { ApplyService } from "@/api/modules/system";
import { Apply } from "@/api/interface/system.model";
import OprDelete from "@/meta-components/MetaTableComponents/OprDelete.vue";
import { ElMessage } from "element-plus";
import { hasAuth } from "@/utils/util";
import AddBtn, { AddBtnConfig } from "@/components/AddBtn/index.vue";
import { useTableMemory } from "@/meta-components/MetaHooks/usePageMemory";

import { useTableScrollMemory } from "@/hooks/useTableScrollMemory";
import router from "@/routers";

import TimeTagModal from "./components/TimeTagModal.vue";
import { YN } from "@/enums/global-enums";

let myApplyTable = ref();
// const globalStore = GlobalStore();

const inEditCell = ref<{ row: any; fieldName: any }>();
// let tabsConfig: TabsConfig = reactive({
// 	tabList: [
// 		{
// 			label: "我的申请",
// 			value: 1
// 		}
// 	],
// 	showCount: 1,
// 	filterAttr: "type"
// });
/**筛选框配置数量 */
let filterConfig: Array<FilterConfigOptions> = reactive([
	{ name: "typeName", type: "input", span: 3, placeholder: "筛选申请名称" },
	{
		name: "status",
		type: "singleSelect",
		staticOptions: [
			{ label: "未提交", value: Apply.Status.UNSUBMITTED },
			{ label: "审批中", value: Apply.Status.APPROVING },
			{ label: "不同意", value: Apply.Status.DISAGREE },
			{ label: "同意", value: Apply.Status.AGREE }
		],
		span: 3,
		placeholder: "筛选申请状态"
	},
	{
		name: "isRead",
		type: "singleSelect",
		staticOptions: [
			{ label: "已阅", value: YN.Yes },
			{ label: "未阅", value: YN.No }
		],
		span: 3,
		placeholder: "筛选是否已阅"
	},
	{
		name: "departName",
		type: "input",
		span: 3,
		placeholder: "筛选流程所属"
	},
	{
		name: "applyTime",
		type: "dateRange",
		span: 4,
		placeholder: "申请时间"
	}
]);

let columnsConfig: Array<ColumnProps> = [
	{
		label: "申请状态",
		slotName: "status"
	},
	{
		label: "申请名称",
		prop: "typeName"
	},
	{
		label: "关键词",
		slotName: "keyword"
	},
	{
		label: "时间标签",
		slotName: "timeLabel"
	},
	{
		label: "流程所属部门",
		slotName: "depart"
	},
	{
		label: "申请时间",
		prop: "applyTime",
		width: 160,
		sortable: true,
		sortAttr: "applyTime"
		// showType: "time"
	},
	{
		label: "是否已阅",
		slotName: "isRead"
	},
	{
		label: "操作",
		slotName: "opr",
		fixed: "right",
		width: "180px"
	}
];

let tableConfig: TableConfig = reactive({
	key: "warehouse-mgr",
	columns: columnsConfig,
	requestApi: ApplyService.getList,
	// dataCallback: data => {
	// 	injectSailorCertStatus(data);
	// 	return reformTrainContents(data);
	// },
	selectType: "none",
	selectId: "uuid",
	childrenKey: "contents",
	pagination: true,
	staticParam: {},
	defaultExpandAll: false,
	needRefreshOnActivated: true
});
useTableMemory(myApplyTable, tableConfig);

const addBtnConfig = reactive<AddBtnConfig>({
	label: "发起申请",
	clickFn: () => {
		return jumpTo("add");
	},
	visible: () => {
		return hasAuth("发起申请");
	}
});

// 页面跳转
function jumpTo(mode: "edit" | "add" | "detail" | "resubmit", rowData?: any) {
	// 详情
	if (mode === "detail") return router.push({ name: "ApplyDetail", params: { mode }, query: { uuid: rowData?.uuid } });
	// 申请/编辑/重新申请
	router.push({ name: "ApplyForm", query: { mode, uuid: rowData?.uuid } });
}

// const emits = defineEmits(["onRefresh"]);
//删除
async function handleBtnDelete(rowData: { [key: string]: any }, operateType: Apply.OperateType) {
	const operateApi = operateType === Apply.OperateType.DELETE ? ApplyService.delete : ApplyService.revoke;
	let { code } = await operateApi({ uuid: rowData.uuid });
	if (code !== 0) return;
	ElMessage.success(`${operateType === Apply.OperateType.DELETE ? "删除" : "撤回"}成功`);
	refresh();
}
const keywordInputRef = ref<HTMLInputElement | null>(null);
//编辑字段
function handleEdit(rowData: any, fieldName: string) {
	inEditCell.value = { row: rowData, fieldName };
	if (fieldName === "keyword") {
		nextTick(() => {
			keywordInputRef.value?.focus();
		});
	}
}

// 输入框按下回车时，失去焦点
function onEnter(e: KeyboardEvent) {
	(e.target as HTMLInputElement).blur();
}

//保存字段
function handleSave(rowData: any) {
	const params = {
		uuid: inEditCell.value!.row.uuid,
		timeLabel: rowData.timeLabel,
		timeLabelType: rowData.timeLabelType,
		keyword: rowData.keyword
	};
	ApplyService.updateInList(params).then(({ code }) => {
		if (code !== 0) return;
		ElMessage.success("保存成功");
		refresh();
		inEditCell.value = undefined;
	});
}
//关闭编辑
function updateVisible(visible: boolean) {
	if (!visible) {
		inEditCell.value = undefined;
	}
}
/**刷新 */
function refresh() {
	// emits("onRefresh");
	myApplyTable.value.search();
}

onMounted(() => {
	useTableScrollMemory(myApplyTable, "WarehouseMgr");
});
</script>

<style scoped></style>
