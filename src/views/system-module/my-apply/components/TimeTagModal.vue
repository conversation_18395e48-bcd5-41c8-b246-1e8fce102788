<!-- 时间标签弹窗 -->
<template>
	<el-dialog
		title="时间标签"
		v-model="props.visible"
		width="400px"
		:close-on-click-modal="false"
		:before-close="handleClose"
		append-to-body
		@open="handleOpen"
	>
		<el-form :model="form" ref="formRef" label-width="80px">
			<el-row>
				<el-col :span="12">
					<el-form-item label="时间类型" prop="timeLabelType">
						<el-select v-model="form.timeLabelType" placeholder="选择时间" @change="formRef.resetFields('timeLabel')">
							<el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
						</el-select>
					</el-form-item>
				</el-col>
			</el-row>
			<el-form-item :label="form.timeLabelType === TimeLabelType.MONTH ? '年月' : '日期'" prop="timeLabel">
				<el-date-picker
					v-model="form.timeLabel"
					:type="form.timeLabelType === TimeLabelType.MONTH ? 'month' : 'date'"
					placeholder="选择时间"
					:value-format="form.timeLabelType === TimeLabelType.MONTH ? 'YYYY-MM' : 'YYYY-MM-DD'"
				></el-date-picker>
			</el-form-item>
		</el-form>
		<template #footer>
			<div class="dialog-footer">
				<el-button type="primary" @click="handleSave">保存</el-button>
				<el-button @click="handleClose">取消</el-button>
			</div>
		</template>
	</el-dialog>
</template>
<script setup lang="ts" name="TimeTagModal">
import { ref } from "vue";
import { TimeLabelType } from "@/api/interface/system.model";

const props = defineProps({
	visible: {
		type: Boolean,
		default: false
	},
	formData: {
		type: Object,
		default: () => ({
			timeLabelType: TimeLabelType.DAY,
			timeLabel: ""
		})
	}
});
const emit = defineEmits(["update:visible", "on-save"]);
const form = ref<{ [key: string]: any }>({
	timeLabelType: TimeLabelType.DAY,
	timeLabel: ""
});
const formRef = ref();
const options = [
	{ value: TimeLabelType.MONTH, label: "年月" },
	{ value: TimeLabelType.DAY, label: "年月日" }
];

const handleSave = () => {
	formRef.value.validate(async (valid: boolean) => {
		if (valid) {
			// 保存逻辑
			emit("on-save", form.value);
			handleClose();
		}
	});
};
const handleClose = () => {
	emit("update:visible", false);
};
const handleOpen = () => {
	form.value = { ...props.formData };
};
</script>
<style scoped lange="scss"></style>
