<!-- 审批流程组件 -->
<template>
	<div class="approval-item">
		<el-form>
			<el-form-item label="审批流程" required="true" style="margin-top: 12px" :inline-message="false">
				<div class="approval-container">
					<el-timeline style="max-width: 750px; margin-top: 12px">
						<ProcessItem :level="0" title="发起" :fixed="true" :needApprovers="false" />
						<ProcessItem
							:level="1"
							title="审批"
							filedKey="approval"
							:required="true"
							:fixed="true"
							:needApprovers="true"
							@open-modal="handleOpenModal"
						/>
						<ProcessItem
							:level="2"
							title="二级审批"
							filedKey="secondApproval"
							:required="processData.needSecondApproval"
							:needApprovers="true"
							@add="handleAddProcess"
							@remove="handleRemoveProcess"
							@open-modal="handleOpenModal"
						/>
						<ProcessItem
							v-if="processData.needSecondApproval"
							:level="3"
							title="三级审批"
							filedKey="thirdApproval"
							:required="processData.needThirdApproval"
							:needApprovers="true"
							:value="processData.thirdApproval"
							@add="handleAddProcess"
							@remove="handleRemoveProcess"
							@open-modal="handleOpenModal"
						/>
						<ProcessItem :level="4" title="完成" :fixed="true" :needApprovers="false" />
						<ProcessItem
							:level="5"
							title="抄送"
							filedKey="cc"
							:required="true"
							:fixed="true"
							:needApprovers="true"
							:multiple="true"
							@open-modal="handleOpenModal"
						/>
					</el-timeline>
				</div>
			</el-form-item>
		</el-form>
		<el-dialog
			v-model="isModalVisible"
			:title="`选择${currentFieldName === 'cc' ? '抄送' : '审批'}人员`"
			width="500px"
			top="400px"
			destory-on-close="true"
		>
			<el-select
				:filterable="true"
				v-if="currentFieldName === 'cc'"
				v-model="selectedApprover"
				:multiple="true"
				placeholder="请选择人员"
				value-key="id"
			>
				<el-option v-for="option in userList" :key="option.id" :label="option.name" :value="option" />
			</el-select>
			<el-select :filterable="true" v-else v-model="selectedApprover" placeholder="请选择人员" value-key="id">
				<el-option v-for="option in approverList" :key="option.id" :label="option.name" :value="option" />
			</el-select>
			<p class="modal-tips" v-if="currentFieldName !== 'cc'">
				<el-icon><Warning /></el-icon>在组织架构中设置了“体系审批权限”的用户可以进行审批
			</p>
			<template #footer>
				<el-button @click="isModalVisible = false">取消</el-button>
				<el-button type="primary" @click="handleConfirm">确定</el-button>
			</template>
		</el-dialog>
	</div>
</template>
<script setup lang="tsx">
import { reactive, ref, onMounted, provide, inject, Ref } from "vue";
import ProcessItem from "./ProcessItem.vue";
import { MemberService } from "@/api/modules/member";
import { ApprovalType } from "@/api/interface/system.model";
import { Member } from "@/api/interface/member";
import { ElMessage } from "element-plus";
// import { useRoute } from "vue-router";
// const route = useRoute();

/** 审批权限人员 */
const approverList = ref<Member.SimpleDetail[]>([]);
/** 所有人 */
const userList = ref<Member.SimpleDetail[]>([]);

/** 流程初始数据（后端格式）新建时传默认模版，编辑时回显detail */
const initData = inject("initApprovals") as Ref<ApprovalType[]>;
const processData = reactive({
	/** 审批人*/
	approval: [] as any[],
	/**二级审批人*/
	secondApproval: [] as any[],
	/** 三级审批人*/
	thirdApproval: [] as any[],
	/**抄送人*/
	cc: [] as any[],
	/**是否需要二级审批*/
	needSecondApproval: false,
	/**是否需要三级审批*/
	needThirdApproval: false
});
provide("processData", processData);

// 添加审批流程
const handleAddProcess = ({ level }: { level: number }) => {
	if (level === 2) {
		processData.needSecondApproval = true;
	} else if (level === 3) {
		processData.needThirdApproval = true;
	}
};
// 删除审批层级
const handleRemoveProcess = ({ level }: { level: number }) => {
	// 移除审批人、重置判断状态
	if (level === 2) {
		processData.needSecondApproval = false;
		processData.needThirdApproval = false;
		processData.thirdApproval = [];
		processData.secondApproval = [];
	} else if (level === 3) {
		processData.needThirdApproval = false;
		processData.thirdApproval = [];
	}
};
// 选择人员弹窗 相关
const isModalVisible = ref(false);
const selectedApprover = ref<any[] | any>();
const currentFieldName = ref("");
const handleOpenModal = (fieldName: string) => {
	currentFieldName.value = fieldName;
	const users = processData[fieldName as ApprovalFiled];
	selectedApprover.value = fieldName === "cc" ? users : users[0];
	isModalVisible.value = true;
};
type ApprovalFiled = "approval" | "secondApproval" | "thirdApproval";
const handleConfirm = () => {
	if (currentFieldName.value && selectedApprover.value) {
		if (Array.isArray(selectedApprover.value)) {
			processData[currentFieldName.value as ApprovalFiled] = selectedApprover.value;
		} else {
			processData[currentFieldName.value as ApprovalFiled] = [selectedApprover.value];
		}
	}
	isModalVisible.value = false;
};
/** 数据校验 */
const validate = () => {
	// 数据校验通过标识
	let validateFlag = true;
	if (processData.approval.length === 0) {
		ElMessage.error("请选择审批人");
		validateFlag = false;
		return validateFlag;
	}
	if (processData.needSecondApproval && processData.secondApproval.length === 0) {
		ElMessage.error("请选择二级审批人");
		validateFlag = false;
		return validateFlag;
	}
	if (processData.needThirdApproval && processData.thirdApproval.length === 0) {
		ElMessage.error("请选择三级审批人");
		validateFlag = false;
		return validateFlag;
	}
	if (processData.cc.length === 0) {
		ElMessage.error("请选择抄送人");
		validateFlag = false;
		return validateFlag;
	}
	return validateFlag;
};

defineExpose({
	data: processData,
	validate
});
let userIdItemMap: Map<number, any>;
/** 获取用户列表 */
const fetchUserList = () => {
	// 获取userListOption
	MemberService.getSimpleList({ length: -1 }).then(res => {
		if (res.data && res.data.list) {
			userList.value = res.data.list;
			userIdItemMap = new Map(res.data.list.map((item: any) => [item.id, item]));
			initProcess();
		}
	});
	MemberService.getSimpleList({ length: -1, roleName: "体系审批权限" }).then(res => {
		if (res.data && res.data.list) {
			// 替换成有审批权限的人
			approverList.value = res.data.list;
		}
	});
};
const emits = defineEmits(["process-inited"]);
/** 初始化审批流程 */
const initProcess = async () => {
	if (initData.value && initData.value.length) {
		// 编辑、重提回显审批
		const processTypeMap = new Map();
		initData.value.forEach((item: any) => {
			processTypeMap.get(item.type)?.push(item.corpUserId) || processTypeMap.set(item.type, [item.corpUserId]);
		});
		if (processTypeMap.get(ApprovalType.APPROVAL)) {
			processData.approval = processTypeMap
				.get(ApprovalType.APPROVAL)
				.map((userId: number) => userIdItemMap.get(userId))
				.filter(Boolean);
		}
		if (processTypeMap.get(ApprovalType.SECOND_APPROVAL)) {
			processData.needSecondApproval = true;
			processData.secondApproval = processTypeMap
				.get(ApprovalType.SECOND_APPROVAL)
				.map((userId: number) => userIdItemMap.get(userId))
				.filter(Boolean);
		}
		if (processTypeMap.get(ApprovalType.THIRD_APPROVAL)) {
			processData.needThirdApproval = true;
			processData.needSecondApproval = true;
			processData.thirdApproval = processTypeMap
				.get(ApprovalType.THIRD_APPROVAL)
				.map((userId: number) => userIdItemMap.get(userId))
				.filter(Boolean);
		}
		if (processTypeMap.get(ApprovalType.COPY)) {
			processData.cc = processTypeMap
				.get(ApprovalType.COPY)
				.map((userId: number) => userIdItemMap.get(userId))
				.filter(Boolean);
		}
	}
	emits("process-inited");
};
onMounted(() => {
	fetchUserList();
});
</script>
<style lang="scss" scoped>
.modal-tips {
	display: flex;
	align-items: center;
	margin: 8px 0 -12px;
	font-size: 14px;
	i {
		margin-right: 4px;
	}
}
</style>
