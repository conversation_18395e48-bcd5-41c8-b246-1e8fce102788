<!-- 申请表框架 -->
<template>
	<div>
		<div class="apply-form-container">
			<div class="apply-header">
				<el-form :model="headerData" ref="form">
					<el-row :gutter="10">
						<el-col :span="6">
							<el-form-item label="关键词" prop="keyword">
								<el-input v-model="headerData.keyword" />
							</el-form-item>
						</el-col>
						<el-col :span="6">
							<el-form-item label="时间标签类型" prop="timeLabelType">
								<el-select v-model="headerData.timeLabelType" @change="form.resetFields('timeLabel')">
									<el-option label="年月" :value="TimeLabelType.MONTH"></el-option>
									<el-option label="年月日" :value="TimeLabelType.DAY"></el-option>
								</el-select>
							</el-form-item>
						</el-col>
						<el-col :span="6">
							<el-form-item label="时间标签" prop="timeLabel">
								<el-date-picker
									:type="headerData.timeLabelType === TimeLabelType.MONTH ? 'month' : 'date'"
									placeholder="选择时间"
									:value-format="headerData.timeLabelType === TimeLabelType.MONTH ? 'YYYY-MM' : 'YYYY-MM-DD'"
									v-model="headerData.timeLabel"
								/>
							</el-form-item>
						</el-col>
					</el-row>
				</el-form>
			</div>
			<div class="apply-body">
				<div class="title">{{ props.title }}</div>
				<!-- 这里是具体的表单内容 -->
				<slot />
			</div>
		</div>
		<!-- 审批流程 -->
		<div class="approval-container">
			<ApprovalProcess ref="approvalRef" @process-inited="onProcessInited" />
			<el-checkbox v-model="isSaveAsDefBool" style="display: block; margin: -36px 92px"> 保存为此申请的默认审批流程 </el-checkbox>
		</div>
	</div>
</template>
<script setup lang="ts">
import { ref, reactive, computed } from "vue";
import ApprovalProcess from "./ApprovalProcess.vue";
import { Apply, ApprovalType, TimeLabelType } from "@/api/interface/system.model";
import { YN } from "@/enums/global-enums";

const approvalRef = ref();
const props = defineProps({
	title: String,
	formData: {
		type: Object,
		default: () => ({})
	}
});

// 转换审批流程数据为一人一项（后端）的格式
const getTransformData = (data: any) => {
	const transProcessData: Apply.ApplyApprovalInfo[] = [];
	if (data.approval?.[0]) {
		// 审批人
		transProcessData.push({
			type: ApprovalType.APPROVAL,
			corpUserId: data.approval[0].id,
			corpUserName: data.approval[0].name
		});
	}
	if (data.needSecondApproval && data.secondApproval?.[0]) {
		// 二级审批人
		transProcessData.push({
			type: ApprovalType.SECOND_APPROVAL,
			corpUserId: data.secondApproval?.[0].id,
			corpUserName: data.secondApproval?.[0].name
		});
	}
	if (data.needThirdApproval && data.thirdApproval?.[0]) {
		// 三级审批人
		transProcessData.push({
			type: ApprovalType.THIRD_APPROVAL,
			corpUserId: data.thirdApproval?.[0].id,
			corpUserName: data.thirdApproval?.[0].name
		});
	}
	if (data.cc && data.cc?.length) {
		//抄送人
		data.cc.forEach((item: { id: number; name: string }) => {
			transProcessData.push({
				type: ApprovalType.COPY,
				corpUserId: item.id,
				corpUserName: item.name
			});
		});
	}
	return transProcessData;
};
const validateAndSave = (callback: Function) => {
	save();
	const valid = approvalRef.value?.validate();
	callback(valid);
};
// 保存转换后流程数据到表单
const save = () => {
	const processData = approvalRef.value?.data;
	headerData.approvals = getTransformData(processData);
};

const emit = defineEmits(["process-inited"]);
function onProcessInited() {
	emit("process-inited");
}

// 表单数据（一级）
const headerData = reactive(props.formData);
const form = ref();
const isSaveAsDefBool = computed({
	get() {
		return headerData.isSaveAsDef === YN.Yes;
	},
	set(newVal) {
		headerData.isSaveAsDef = newVal ? YN.Yes : YN.No;
	}
});
defineExpose({
	validateAndSave,
	save
});
</script>
<style lang="scss" scoped>
.apply-form-container {
	width: 100%;
	border: 1px solid #d4d4d4;
	border-radius: 5px;
	.apply-header {
		padding: 10px;
		border-bottom: 1px solid #d4d4d4;
		::v-deep .el-form-item--default {
			margin-bottom: 0 !important;
		}
	}
	.apply-body {
		min-height: 300px;
		padding: 24px;
		.title {
			margin-bottom: 24px;
			font-family: serif;
			font-size: 24px;
			font-weight: bold;
			text-align: center;
		}
	}
	.apply-footer {
		padding: 24px;
	}
}
</style>
