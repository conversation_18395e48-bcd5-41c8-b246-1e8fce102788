<!-- 可编辑表单组件：表格形式，单元格可设置为输入框或选择框，支持点击表头批量更新该列数据 -->
<template>
	<div style="width: 100%">
		<el-table :data="tableData" border style="width: 100%" :span-method="arraySpanMethod" :header-cell-style="rowStyle">
			<!-- 新增删除行 -->
			<el-table-column width="50" align="center" header-align="center">
				<template #header></template>
				<template #default="{ $index, row }">
					<div class="table-row-actions">
						<el-icon @click="addItem($index)" style="font-size: 18px; cursor: pointer"><Plus /></el-icon>
						<el-icon
							v-if="canDelCondition ? canDelCondition(row, $index) : true"
							@click="deleteItem($index)"
							style="font-size: 18px; cursor: pointer"
							><Minus
						/></el-icon>
					</div>
				</template>
			</el-table-column>
			<el-table-column
				v-for="(column, index) in columns"
				:type="column.type === 'index' ? 'index' : 'default'"
				:key="index"
				:prop="column.prop"
				:label="column.label"
				:width="column.width"
				:align="column.align"
			>
				<template #header>
					<div v-if="column.type !== 'text'" style="width: 100%; cursor: pointer" @click="openCellModal(column, index)">
						{{ column.label }}
					</div>
					<div v-else style="width: 100%">{{ column.label }}</div>
				</template>
				<template #default="{ row }">
					<el-input v-if="column.type === 'input'" v-model="row[column.prop]" />
					<span v-else-if="column.type === 'text'">{{ row[column.prop] }}</span>
					<el-select
						v-else-if="column.type === 'select'"
						v-model="row[column.prop]"
						:placeholder="`请选择${column.label}`"
						style="width: 100%"
					>
						<el-option v-for="option in column.options" :key="option.value" :label="option.label" :value="option.value" />
					</el-select>
					<el-date-picker
						v-else-if="['datePicker', 'dateTimePicker'].includes(column.type!)"
						v-model="row[column.prop]"
						:type="column.type === 'dateTimePicker' ? 'datetime' : 'date'"
						:placeholder="column.type === 'dateTimePicker' ? '请选择日期时间' : '请选择日期'"
						style="width: 100%"
						:value-format="column.type === 'dateTimePicker' ? 'YYYY-MM-DD HH:mm:ss' : 'YYYY-MM-DD'"
					/>
				</template>
			</el-table-column>
			<template #empty>
				<div style="cursor: pointer" @click="addItem()">新增一条数据</div>
			</template>
		</el-table>
		<el-dialog
			:model-value="modalData.visible"
			width="400"
			:title="`请输入“${modalData.row.label}”列填充内容`"
			@close="closeModal"
		>
			<el-input v-if="modalData.row.type === 'input'" v-model="modalData.value" :rows="4" type="textarea" />
			<el-date-picker
				v-else-if="['datePicker', 'dateTimePicker'].includes(modalData.row.type!)"
				v-model="modalData.value"
				:type="modalData.row.type === 'dateTimePicker' ? 'datetime' : 'date'"
				:placeholder="modalData.row.type === 'dateTimePicker' ? '请选择日期时间' : '请选择日期'"
				style="width: 100%"
				:value-format="modalData.row.type === 'dateTimePicker' ? 'YYYY-MM-DD HH:mm:ss' : 'YYYY-MM-DD'"
			/>
			<el-select
				v-else-if="modalData.row.type === 'select'"
				v-model="modalData.value"
				:placeholder="`请选择${modalData.row.label}`"
				style="width: 100%"
			>
				<el-option v-for="option in modalData.row.options" :key="option.value" :label="option.label" :value="option.value" />
			</el-select>
			<template #footer>
				<div class="dialog-footer">
					<el-button type="primary" @click="saveCell"> 确定 </el-button>
					<el-button @click="closeModal">取消</el-button>
				</div>
			</template>
		</el-dialog>
	</div>
</template>
<script setup lang="ts">
import { PropType, ref, watch, nextTick } from "vue";
import { isEqual } from "lodash";
export interface ColumnProps {
	//字段key
	prop: string;
	//字段value
	label: string;
	// 宽度
	width?: string;
	// 类型 纯文本text 输入框input 选择框select
	type?: "input" | "select" | "text" | "index" | "datePicker" | "dateTimePicker";
	// 对齐方式
	align?: "center" | "left" | "right";
	// 选择框选项
	options?: { label: string; value: string }[];
	// 默认值
	defaultValue?: string;
	// 合并相同单元格
	mergeInSave?: boolean;
	// 表头合并
	colSpan?: number;
}
const props = defineProps({
	// 表格数据
	columns: {
		type: Array as PropType<ColumnProps[]>,
		default: () => []
	},
	// 表格数据
	modelValue: {
		type: Array,
		default: () => []
	},
	//可以操作的条件
	canDelCondition: { type: Function as PropType<(row: any, index?: number) => boolean> }
});
// 合并表头样式
const rowStyle = ({ column, rowIndex }: { row: any; column: any; columnIndex: number; rowIndex: number }) => {
	// 表头合并
	if (rowIndex === 0) {
		const columnItem = props.columns.find(item => item.prop === column.property);
		if (columnItem && columnItem.colSpan) {
			nextTick(() => {
				if (document.getElementsByClassName(column.id).length !== 0) {
					document.getElementsByClassName(column.id)[0].setAttribute("colSpan", columnItem.colSpan!.toString());
					return false;
				}
			});
		}
		if (columnItem && columnItem.colSpan === 0) {
			return { display: "none" };
		}
	}
};
const tableData = ref<any[]>([...props.modelValue]);
// 双向绑定 modelValue和tableData
watch(
	() => props.modelValue,
	val => {
		if (!isEqual(val, tableData.value)) {
			tableData.value = [...val];
		}
	}
);
const emit = defineEmits(["update:modelValue"]);
watch(
	tableData,
	val => {
		if (!isEqual(val, props.modelValue)) {
			emit("update:modelValue", val.slice());
		}
	},
	{ deep: true }
);

const addItem = (index?: number) => {
	const defaultItem: any = {};
	props.columns.forEach(item => {
		if (item.defaultValue) {
			defaultItem[item.prop] = item.defaultValue;
		}
	});
	if (index !== undefined) {
		tableData.value.splice(index + 1, 0, defaultItem);
		return;
	}
	tableData.value.push(defaultItem);
};
const deleteItem = (index: number) => {
	tableData.value.splice(index, 1);
	// const index = tableData.value.indexOf(row);
};
const mergeRowKeys = props.columns.filter(item => item.mergeInSave).map(item => item.prop);
// 合并单元格（列数据相等）
const arraySpanMethod = ({ row, column, rowIndex }: { row: any; column: any; rowIndex: number; columnIndex: number }) => {
	if (column.property && mergeRowKeys.includes(column.property)) {
		const rowsData = tableData.value.map(i => i[column.property]);
		const currentValue = row[column.property];
		const sum = rowsData.filter((data: any) => data === currentValue).length;
		const firstIndex = rowsData.findIndex((data: any) => data === currentValue);
		return [rowIndex === firstIndex ? sum : 0, 1];
	}
	return [1, 1];
};

// 填充全列弹窗数据
const modalData = ref({
	visible: false,
	value: "",
	index: 0,
	row: {} as ColumnProps
});
const openCellModal = (columnItem: ColumnProps, index: number) => {
	if (!tableData.value || tableData.value.length === 0) return;
	modalData.value = {
		visible: true,
		value: "",
		index: index + 3,
		row: columnItem
	};
};
const closeModal = () => {
	modalData.value.visible = false;
};
const saveCell = () => {
	const { row, value } = modalData.value;
	tableData.value.forEach(item => {
		item[row.prop] = value;
	});
	closeModal();
};
</script>
<style lang="scss" scoped>
.table-row-actions {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding-left: 11px;
	.el-icon {
		&:first-child {
			padding-bottom: 6px;
		}
	}
}
</style>
