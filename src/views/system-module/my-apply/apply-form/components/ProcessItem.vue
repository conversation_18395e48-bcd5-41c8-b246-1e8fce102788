<!-- 流程步骤条Item组件 -->
<template>
	<el-timeline-item type="primary">
		<template v-if="required || fixed">
			<div :class="{ content: true, required }">
				<span>{{ title }}</span>
				<el-button text @click="handleRemove" v-if="!fixed">
					<el-icon color="#bfc2cb"><Delete /></el-icon>
				</el-button>
			</div>
			<div class="user-container" v-if="needApprovers">
				<div class="user" v-for="item in selectPerson" :key="item.id">
					<el-avatar size="small" :src="item.avatar" v-if="item.avatar" />
					<el-icon v-else class="avatar-default"><UserFilled /></el-icon>
					<span>{{ item.name }}</span>
					<el-icon color="#bfc2cb" @click="handleRemoveUser(item.id)" class="close-icon">
						<Close />
					</el-icon>
				</div>
				<el-button v-if="multiple || (!multiple && !selectPerson.length)" round style="width: 75px" @click="openUserModal">
					<el-icon><Plus /></el-icon>
				</el-button>
			</div>
		</template>
		<template v-else>
			<el-button style="color: #999999; border: 1px dashed #999999" @click="handleAdd">
				<el-icon><Plus /></el-icon>
				{{ `增加${title}` }}
			</el-button>
		</template>
	</el-timeline-item>
</template>
<script setup lang="ts">
import { defineProps, defineEmits, inject, computed } from "vue";

const processData = inject("processData") as { [key: string]: any };

const props = defineProps({
	/** 步骤number */
	level: Number,
	/** 步骤标题 */
	title: String,
	/** 是否必填 */
	required: {
		type: Boolean,
		default: false
	},
	/** 固定节点 */
	fixed: {
		type: Boolean,
		default: false
	},
	/** 流程人 */
	approvers: Array,
	/** 需要配置人员 */
	needApprovers: {
		type: Boolean,
		default: true
	},
	/** 字段名 */
	filedKey: String,
	/** 是否多选 */
	multiple: {
		type: Boolean,
		default: false
	}
});

const selectPerson = computed(() => {
	if (processData && props.filedKey && processData[props.filedKey]) {
		let users = processData[props.filedKey];
		if (!Array.isArray(processData[props.filedKey])) {
			users = [processData[props.filedKey]];
		}
		return users;
	}
	return [];
});
const emit = defineEmits(["remove", "add", "open-modal"]);
const handleRemove = () => {
	emit("remove", {
		level: props.level
	});
};
const handleAdd = () => {
	// TODO: 添加当前节点
	emit("add", {
		level: props.level
	});
};
const openUserModal = () => {
	// 打开用户选择框
	emit("open-modal", props.filedKey);
};
const handleRemoveUser = (userId: number) => {
	// 移除用户
	if (props.filedKey && processData[props.filedKey]) {
		if (!Array.isArray(processData[props.filedKey])) {
			processData[props.filedKey] = [];
		}
		const userIndex = processData[props.filedKey].findIndex(({ id }: { id: number }) => id === userId);
		processData[props.filedKey].splice(userIndex, 1);
	}
};
</script>
<style scoped lang="scss">
.approval-item {
	height: 200px;
	.content {
		display: flex;
		align-items: center;
		span {
			margin-right: 4px;
		}
		&.required {
			&::before {
				margin-right: 4px;
				color: red;
				content: "*";
			}
		}
	}
	.user-container {
		display: flex;
		flex-wrap: wrap;
		.user {
			box-sizing: border-box;
			display: flex;
			align-items: center;
			justify-content: space-between;
			width: 128px;
			height: 32px;
			padding: 0 14px 0 4px;
			margin-right: 12px;
			margin-bottom: 4px;
			background: #f5f5f5;
			border-radius: 16px;
			.avatar-default {
				width: 24px;
				height: 24px;
				overflow: hidden;
				color: $blank-color;
				background-color: $primary-color;
				border-radius: 50%;
			}
			span {
				max-width: 72px;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
			}
			.close-icon {
				cursor: pointer;
			}
		}
	}
}
</style>
