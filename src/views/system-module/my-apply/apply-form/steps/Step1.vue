<!-- 发起申请-选择申请 -->
<template>
	<div>
		<el-row :gutter="12">
			<el-col :span="6" v-for="item in typeOptions" :key="item.value">
				<div
					:class="{
						type: true,
						checked: formData.type === item.value
					}"
					@click="checkType(item)"
				>
					{{ item.name }}
				</div>
			</el-col>
		</el-row>
	</div>
</template>
<script setup lang="ts">
import { inject, onMounted, ref } from "vue";
import { ElMessage } from "element-plus";
import { ApplyType } from "@/api/interface/system.model";
import { getDictPairList } from "@/api/modules/power";

let formData = inject("formData") as { [key: string]: any };
const typeOptions = ref<{ name: string; value: ApplyType }[]>();
const getTypeOptions = async () => {
	const res = await getDictPairList({ dictCode: "SysManageApplyType" });
	if (res.data?.list) {
		typeOptions.value = res.data.list as { name: string; value: ApplyType }[];
	}
};
const save = () => {
	if (!formData.value.type) {
		ElMessage.error("请选择申请类型");
		return false;
	}
	return true;
};
const checkType = (typeItem: { name: string; value: ApplyType }) => {
	formData.value.type = typeItem.value;
	formData.value.typeName = typeItem.name;
};
onMounted(() => {
	getTypeOptions();
});
defineExpose({
	tryToNext: save
});
</script>
<style scoped lang="scss">
.type {
	display: flex;
	align-items: center;
	justify-content: center;
	height: 64px;
	margin-bottom: 12px;
	color: #3d3d3d;
	cursor: pointer;
	border: 1px solid #e6e6e6;
	border-radius: 5px;
	&.checked {
		color: #409eff;
		border-color: #409eff;
	}
}
</style>
