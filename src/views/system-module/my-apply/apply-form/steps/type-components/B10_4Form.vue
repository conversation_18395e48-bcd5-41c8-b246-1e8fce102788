<!-- B10-4外来文件评审表 -->
<template>
	<div class="table-container">
		<MetaForm
			mode="add"
			ref="form"
			:formConfig="formConfig"
			:styleConfig="{ mode: 'page', labelPosition: 'top', createBtnText: '确定' }"
			style="margin-bottom: -36px"
			:rule-form="ruleForm"
		>
			<template #type>
				<div class="flex-box" v-if="form">
					<div class="flex-item">
						<div class="label">外来文件：</div>
						<el-checkbox-group v-model="form.formData.externalFileType">
							<el-checkbox border :label="1">强制性</el-checkbox>
							<el-checkbox border :label="2">建议性</el-checkbox>
						</el-checkbox-group>
					</div>
					<div class="flex-item">
						<div class="label">非外来文件：</div>
						<el-checkbox-group v-model="form.formData.nonExternalFileType">
							<el-checkbox border :label="1" :value="1">资料</el-checkbox>
							<el-checkbox border :label="2" :value="2">信息</el-checkbox>
						</el-checkbox-group>
					</div>
				</div>
			</template>
			<template #foot-text>
				<span style="color: #606266">注：1、本表一式一份，完成后SMS办留存。</span>
			</template>
			<template #button> </template>
		</MetaForm>
	</div>
</template>
<script setup lang="ts">
import {
	ref,
	reactive
	//  inject,
} from "vue";

import MetaForm from "@/meta-components/MetaForm/index.vue";
import { UploadService } from "@/api/modules/upload";

import { FormConfigOptions, FormItemOptions } from "@/meta-components/MetaForm/interface";
const formContent: FormItemOptions[] = reactive([
	{
		label: "文件名称",
		name: "fileName",
		type: "input",
		placeholder: "请输入文件名称",
		otherAttr: {
			max: 100,
			showWordLimit: false
		}
	},
	{
		label: "颁发机构",
		name: "authority",
		type: "input",
		placeholder: "请输入颁发机构",
		otherAttr: {
			max: 100,
			showWordLimit: false
		}
	},
	{
		label: "生效日期",
		name: "effectiveDate",
		type: "date",
		placeholder: "请选择生效日期"
	},
	{ type: "slot", label: "文件类别", name: "type" },
	// {
	// 	label: "外来文件",
	// 	name: "externalFileType",
	// 	type: "checkbox",
	// 	span: 24,
	// 	staticOptions: [
	// 		{ label: "强制性", value: 1 },
	// 		{ label: "建议性", value: 2 }
	// 	]
	// },
	// {
	// 	label: "非外来文件",
	// 	name: "nonExternalFileType",
	// 	type: "checkbox",
	// 	span: 24,
	// 	staticOptions: [
	// 		{ label: "资料", value: 1 },
	// 		{ label: "信息", value: 2 }
	// 	]
	// },
	{
		label: "文件主要内容概述",
		name: "content",
		type: "textarea",
		otherAttr: {
			max: 5000,
			showWordLimit: false
		}
	},
	{
		label: "收集人建议",
		name: "suggestion",
		type: "textarea",
		otherAttr: {
			max: 5000,
			showWordLimit: false
		}
	},
	{
		label: "相关部门会签意见",
		name: "signUser",
		type: "textarea",
		otherAttr: {
			max: 5000,
			showWordLimit: false
		}
	},
	{
		label: "确认文件类别",
		name: "fileType",
		type: "input",
		placeholder: "请输入确认文件类别",
		otherAttr: {
			max: 100,
			showWordLimit: false
		}
	},
	{
		label: "发放范围",
		name: "scope",
		type: "input",
		placeholder: "请输入发放范围",
		otherAttr: {
			max: 100,
			showWordLimit: false
		}
	},
	{
		name: "foot-text",
		type: "slot"
	},
	{
		name: "files",
		label: "上传文件",
		type: "fileUpload",
		span: 14,
		api: UploadService.ossApi,
		hint: "注：请上传文件或者图片，支持pdf、doc、docx、xls、xlsx、ceb、pptx等格式，最大限制30MB",
		otherAttr: { showUploadPlaceholder: false },
		imageOptions: {
			pathName: "system-apply",
			mode: "multiple",
			limitNum: 50,
			maxFileSize: 30,
			accept: ".pdf,.doc,.docx,.xls,.xlsx,.ceb,.pptx,image/*"
		}
	}
]);
const formConfig: FormConfigOptions = reactive({
	key: "system-apply-B10-4",
	items: formContent,
	detailKey: "uuid",
	closeFunction: close,
	closeFormAt: "both",
	hintMessage: ["创建成功", "保存成功"]
});
const ruleForm = reactive({
	signUser: [
		{
			required: true,
			message: "请输入相关部门会签意见"
		}
	]
});
// const formData = inject("formData") as Ref<{[key: string]: any }>;
const form = ref();

// beforeSubmit 修改提交前conetent数据
// const beforeSubmit = () => {
// };
defineExpose({
	form
	// beforeSubmit
});
</script>
<style lang="scss" scoped>
.table-container {
	::v-deep .el-descriptions__label.el-descriptions__cell.is-bordered-label {
		background-color: #f4f4f5;
	}
	.flex-box {
		display: flex;
		align-items: center;
		justify-content: space-between;
		width: 100%;
		.flex-item {
			display: flex;
			flex: 1;
			align-items: center;
			color: #606266;
			.label {
				margin-right: 24px;
			}
		}
	}
}
</style>
