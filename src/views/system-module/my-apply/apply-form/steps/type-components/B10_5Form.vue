<!-- B10-5 文件借阅登记表 -->
<template>
	<div class="table-container">
		<MetaForm
			mode="add"
			ref="form"
			:formConfig="formConfig"
			:styleConfig="{ mode: 'page', labelPosition: 'top', createBtnText: '确定' }"
			style="margin-bottom: -36px"
		>
			<template #records>
				<div v-if="form" style="width: 100%">
					<InEditTable :columns="inEditColumns" v-model="form.formData.records" />
				</div>
			</template>
			<template #foot-text>
				<span style="color: #606266">注：本表一式一份，文件持有人留存。</span>
			</template>
			<template #button> </template>
		</MetaForm>
	</div>
</template>
<script setup lang="ts">
import { ref, reactive, inject, Ref, onMounted } from "vue";

import MetaForm from "@/meta-components/MetaForm/index.vue";
import InEditTable, { ColumnProps } from "../../components/InEditTable.vue";
import { UploadService } from "@/api/modules/upload";
import { DepartService } from "@/api/modules/depart";
import { useShipOptions } from "@/views/system-module/hooks/useOptions";
import { GlobalStore } from "@/store";

import { FormConfigOptions, FormItemOptions, SelectOption } from "@/meta-components/MetaForm/interface";
import { Apply } from "@/api/interface/system.model";
const { getOption } = useShipOptions();
const globalStore = GlobalStore();

const inEditColumns: ColumnProps[] = [
	{
		prop: "index",
		type: "index",
		label: "行号"
	},
	{
		prop: "colIndex",
		label: "序号",
		type: "input"
	},
	{
		prop: "fileName",
		label: "文件名称",
		type: "input"
	},
	{
		prop: "borrowUser",
		label: "借阅人",
		type: "input"
	},
	{
		prop: "borrowDate",
		label: "借阅时间",
		type: "dateTimePicker"
	},
	{
		prop: "returnDate",
		label: "归还时间",
		type: "dateTimePicker"
	},
	{
		prop: "remark",
		label: "备注",
		type: "input"
	}
];

/** 将树级结构转为级联所需的数据格式 */
const reformDataToCas = (
	childrens: any[],
	labelKey: string,
	ValueKey: string[] | string
): Array<{ label: string; value: any; children: any[] }> => {
	return childrens.map((item: any) => {
		const { children } = item;
		return {
			label: item[labelKey],
			value:
				typeof ValueKey === "string"
					? item[ValueKey]
					: ValueKey.reduce((pre, cur) => {
							pre[cur] = item[cur];
							return pre;
					  }, {} as any),
			children: children && children.length > 0 ? reformDataToCas(children, labelKey, ValueKey) : []
		};
	});
};

const getCasOptions = async () => {
	const res = await getOption();
	const shipOptins = res.data.list.map((item: SelectOption) => ({
		label: item.label,
		value: { id: item.data!.id, name: item.data!.name }
	}));

	// 获取部门数据
	const departOptions: any[] = [];
	const departRes = await DepartService.getAll({ corpId: globalStore.userInfo.corpId });
	if (departRes.data && departRes.data.departs) {
		const { departs } = departRes.data;
		const departChildren = [[] as any[], [] as any[]];
		departs.forEach(item => {
			const casItem: any = {
				value: { id: item.id, name: item.name },
				label: item.name!,
				children: item.children && item.children.length > 0 ? reformDataToCas(item.children, "name", ["id", "name"]) : []
			};
			if (item.type === 1) {
				departChildren[0].push(casItem);
			} else if (item.type === 2) {
				departChildren[1].push(casItem);
			}
		});
		if (departChildren[0].length > 0) {
			departOptions.push({
				value: { name: "离岸部门", id: Apply.DepartType.OFFSHORE },
				label: "离岸部门",
				children: departChildren[0]
			});
		}
		if (departChildren[1].length > 0) {
			departOptions.push({
				value: { name: "岸基部门", id: Apply.DepartType.ONSHORE },
				label: "岸基部门",
				children: departChildren[1]
			});
		}
	}

	return {
		code: 0,
		data: {
			list: [
				{
					label: "船舶",
					value: "船舶",
					children: shipOptins
				},
				{
					label: "部门",
					value: "部门",
					children: departOptions
				}
			]
		}
	};
};
const formContent: FormItemOptions[] = reactive([
	{
		label: "船舶/部门",
		name: "shipDeparts",
		type: "cascader",
		placeholder: "可多选",
		api: getCasOptions,
		otherAttr: {
			multiple: true
		}
	},
	{
		label: "借阅记录",
		name: "records",
		type: "slot"
	},
	{
		name: "foot-text",
		type: "slot"
	},
	{
		name: "files",
		label: "上传文件",
		type: "fileUpload",
		span: 14,
		api: UploadService.ossApi,
		hint: "注：请上传文件或者图片，支持pdf、doc、docx、xls、xlsx、ceb、pptx等格式，最大限制30MB",
		otherAttr: { showUploadPlaceholder: false },
		imageOptions: {
			pathName: "system-apply",
			mode: "multiple",
			limitNum: 50,
			maxFileSize: 30,
			accept: ".pdf,.doc,.docx,.xls,.xlsx,.ceb,.pptx,image/*"
		}
	}
]);
const formConfig: FormConfigOptions = reactive({
	key: "system-apply-B10-4",
	items: formContent,
	detailKey: "uuid",
	closeFunction: close,
	closeFormAt: "both",
	hintMessage: ["创建成功", "保存成功"]
});
const formData = inject("formData") as Ref<{ [key: string]: any }>;
const form = ref();

// beforeSubmit 修改提交前conetent数据
const beforeSubmit = (data: any) => {
	const shipDeparts = data.shipDeparts || [];
	const selectShips = shipDeparts.filter((item: any[]) => item[0] == "船舶");
	const selectDeparts = shipDeparts.filter((item: any[]) => item[0] == "部门");
	formData.value.shipIds = selectShips.map((item: any) => item[1].id);
	formData.value.departTypes = [...new Set(selectDeparts.map((item: any) => item[1].id))];
	formData.value.departIds = selectDeparts.map((item: any) => item[item.length - 1].id);
	data.shipDepartNames = shipDeparts.map((item: any[]) => {
		return item
			.slice(1)
			.map(item => item.name)
			.filter(Boolean)
			.join("/");
	});

	return data;
};
onMounted(() => {
	// 新增or编辑 重新进入该类别页面 加载默认值
	if (!form.value.formData.records) {
		form.value.formData.records = [{}, {}, {}];
	}
});
defineExpose({
	form,
	beforeSubmit
});
</script>
<style lang="scss" scoped>
.table-container {
	::v-deep .el-descriptions__label.el-descriptions__cell.is-bordered-label {
		background-color: #f4f4f5;
	}
}
</style>
