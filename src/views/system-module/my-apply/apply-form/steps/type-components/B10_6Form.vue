<!-- B10-6 文件回收、作废处理单 -->
<template>
	<div class="table-container">
		<MetaForm
			mode="add"
			ref="form"
			:formConfig="formConfig"
			:styleConfig="{ mode: 'page', labelPosition: 'top', createBtnText: '确定' }"
			style="margin-bottom: -36px"
		>
			<template #fileRecycle>
				<div v-if="form" style="width: 100%">
					<InEditTable :columns="inEditColumns" v-model="form.formData.fileRecycle" />
				</div>
			</template>
			<template #foot-text>
				<span style="color: #606266">注：1、本表一式一份，完成后SMS办主管保存。 2、□ 内适用划“√”，不适用划“x”。</span>
			</template>
			<template #button> </template>
		</MetaForm>
	</div>
</template>
<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";

import MetaForm from "@/meta-components/MetaForm/index.vue";
import { UploadService } from "@/api/modules/upload";
import InEditTable, { ColumnProps } from "../../components/InEditTable.vue";

import { FormConfigOptions, FormItemOptions } from "@/meta-components/MetaForm/interface";
const inEditColumns: ColumnProps[] = [
	{
		prop: "index",
		type: "index",
		label: "行号"
	},
	{
		prop: "belong",
		label: "原保存部门/船舶/人员",
		type: "input"
	},
	{
		prop: "count",
		label: "数量",
		type: "input"
	},
	{
		prop: "date",
		label: "回收日期",
		type: "datePicker"
	},
	{
		prop: "belongSecond",
		label: "原保存部门/船舶/人员",
		type: "input"
	},
	{
		prop: "countSecond",
		label: "数量",
		type: "input"
	},
	{
		prop: "dateSecond",
		label: "回收日期",
		type: "datePicker"
	}
];
const formContent: FormItemOptions[] = reactive([
	{
		label: "申请作废文件名称",
		name: "fileName",
		type: "input",
		otherAttr: {
			max: 100,
			showWordLimit: false
		}
	},
	{
		label: "处理方式",
		name: "method",
		type: "checkbox",
		staticOptions: [
			{ label: "回收", value: 1 },
			{ label: "保存", value: 2 },
			{ label: "作废", value: 3 },
			{ label: "销毁", value: 4 }
		]
	},
	{
		label: "申请原因",
		name: "reason",
		type: "textarea",
		otherAttr: {
			max: 5000,
			showWordLimit: false
		}
	},
	{
		label: "指定人员审核意见",
		name: "reviewComments",
		type: "textarea",
		otherAttr: {
			max: 5000,
			showWordLimit: false
		}
	},
	{
		label: "文件回收登记",
		name: "fileRecycle",
		type: "slot"
	},
	{
		label: "销毁日期",
		name: "destoryDate",
		type: "date",
		placeholder: "请选择销毁日期"
	},
	{
		label: "销毁方法",
		name: "destoryMethod",
		type: "input",
		otherAttr: {
			max: 100,
			showWordLimit: false
		}
	},
	{
		label: "销毁人",
		name: "destoryPerson",
		type: "input",
		otherAttr: {
			max: 100,
			showWordLimit: false
		}
	},
	{
		label: "验证人",
		name: "verifyPerson",
		type: "input",
		otherAttr: {
			max: 100,
			showWordLimit: false
		}
	},
	{
		label: "备注",
		name: "remark",
		type: "textarea",
		otherAttr: {
			max: 5000,
			showWordLimit: false
		}
	},
	{
		name: "foot-text",
		type: "slot"
	},
	{
		name: "files",
		label: "上传文件",
		type: "fileUpload",
		span: 14,
		api: UploadService.ossApi,
		hint: "注：请上传文件或者图片，支持pdf、doc、docx、xls、xlsx、ceb、pptx等格式，最大限制30MB",
		otherAttr: { showUploadPlaceholder: false },
		imageOptions: {
			pathName: "system-apply",
			mode: "multiple",
			limitNum: 50,
			maxFileSize: 30,
			accept: ".pdf,.doc,.docx,.xls,.xlsx,.ceb,.pptx,image/*"
		}
	}
]);
const formConfig: FormConfigOptions = reactive({
	key: "system-apply-B10-4",
	items: formContent,
	detailKey: "uuid",
	closeFunction: close,
	closeFormAt: "both",
	hintMessage: ["创建成功", "保存成功"]
});
// const formData = inject("formData") as Ref<{[key: string]: any }>;
const form = ref();

// beforeSubmit 修改提交前conetent数据
// const beforeSubmit = () => {
// };
onMounted(() => {
	// 新增or编辑 重新进入该类别页面 加载默认值
	if (!form.value.formData.fileRecycle) {
		form.value.formData.fileRecycle = [{}, {}, {}];
	}
});
defineExpose({
	form
	// beforeSubmit
});
</script>
<style lang="scss" scoped>
.table-container {
	::v-deep .el-descriptions__label.el-descriptions__cell.is-bordered-label {
		background-color: #f4f4f5;
	}
}
</style>
