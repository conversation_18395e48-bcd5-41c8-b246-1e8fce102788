<!-- B10-2 文件借阅登记表 -->
<template>
	<div class="table-container">
		<MetaForm
			mode="add"
			ref="form"
			:formConfig="formConfig"
			:styleConfig="{ mode: 'page', labelPosition: 'top', createBtnText: '确定' }"
			style="margin-bottom: -36px"
		>
			<template #notices>
				<div v-if="form" style="width: 100%">
					<InEditTable :columns="noticeColumns" v-model="form.formData.notices" />
				</div>
			</template>
			<template #modifyList>
				<div v-if="form" style="width: 100%">
					<InEditTable :columns="modifyColumns" v-model="form.formData.modifyList" />
				</div>
			</template>
			<template #foot-text>
				<span style="color: #606266">注： 本表一式多份，一份通知部门/人员留存，一份SMS办留存。</span>
			</template>
			<template #button> </template>
		</MetaForm>
	</div>
</template>
<script setup lang="ts">
import { ref, reactive, inject, Ref, onMounted } from "vue";

import MetaForm from "@/meta-components/MetaForm/index.vue";
import InEditTable, { ColumnProps } from "../../components/InEditTable.vue";
import { UploadService } from "@/api/modules/upload";
import { useShipOptions } from "@/views/system-module/hooks/useOptions";
// import { GlobalStore } from "@/store";

import { FormConfigOptions, FormItemOptions } from "@/meta-components/MetaForm/interface";
// import { Apply } from "@/api/interface/system.model";
const { getOption } = useShipOptions();
// const globalStore = GlobalStore();

const noticeColumns: ColumnProps[] = [
	{
		prop: "index",
		type: "index",
		label: "行号"
	},
	{
		prop: "noticeTo",
		label: "通知部门/人员",
		type: "input"
	},
	{
		prop: "noticeDate",
		label: "通知日期/方式",
		type: "input"
	},
	{
		prop: "sign",
		label: "签名",
		type: "input"
	}
];

const modifyColumns: ColumnProps[] = [
	{
		prop: "index",
		type: "index",
		label: "行号"
	},
	{
		prop: "colIndex",
		label: "序号",
		type: "input"
	},
	{
		prop: "fileName",
		label: "文件名称",
		type: "input"
	},
	{
		prop: "version",
		label: "编号(版次)",
		type: "input"
	},
	{
		prop: "pageNumber",
		label: "修改页码",
		type: "input"
	},
	{
		prop: "effectiveDate",
		label: "生效日期",
		type: "datePicker"
	},
	{
		prop: "remark",
		label: "备注",
		type: "input"
	}
];

const formContent: FormItemOptions[] = reactive([
	{
		label: "",
		name: "notices",
		type: "slot"
	},
	{
		label: "文件更改原因",
		name: "changeReason",
		type: "textarea",
		otherAttr: {
			max: 5000,
			showWordLimit: false
		}
	},
	{
		label: "",
		name: "modifyList",
		type: "slot"
	},
	{
		label: "更改前内容",
		name: "contentBefore",
		type: "textarea",
		otherAttr: {
			max: 5000,
			showWordLimit: false
		}
	},
	{
		label: "更改后内容",
		name: "contentAfter",
		type: "textarea",
		otherAttr: {
			max: 5000,
			showWordLimit: false
		}
	},
	{
		label: "关联船舶",
		name: "shipIds",
		type: "singleSelect",
		api: getOption,
		otherAttr: {
			multiple: true,
			appendLabelToFormDataWithKey: "shipNames"
		}
	},
	{
		name: "foot-text",
		type: "slot"
	},
	{
		name: "files",
		label: "上传文件",
		type: "fileUpload",
		span: 14,
		api: UploadService.ossApi,
		hint: "注：请上传文件或者图片，支持pdf、doc、docx、xls、xlsx、ceb、pptx等格式，最大限制30MB",
		otherAttr: { showUploadPlaceholder: false },
		imageOptions: {
			pathName: "system-apply",
			mode: "multiple",
			limitNum: 50,
			maxFileSize: 30,
			accept: ".pdf,.doc,.docx,.xls,.xlsx,.ceb,.pptx,image/*"
		}
	}
]);
const formConfig: FormConfigOptions = reactive({
	key: "system-apply-B10-4",
	items: formContent,
	detailKey: "uuid",
	closeFunction: close,
	closeFormAt: "both",
	hintMessage: ["创建成功", "保存成功"]
});
const formData = inject("formData") as Ref<{ [key: string]: any }>;
const form = ref();

// beforeSubmit 修改提交前conetent数据
const beforeSubmit = (data: any) => {
	const shipIds = data.shipIds || [];
	formData.value.shipIds = shipIds;

	return data;
};
onMounted(() => {
	// 新增or编辑 重新进入该类别页面 加载默认值
	if (!form.value.formData.notices) {
		form.value.formData.notices = [{}, {}, {}];
	}
	if (!form.value.formData.modifyList) {
		form.value.formData.modifyList = [{}, {}, {}];
	}
});
defineExpose({
	form,
	beforeSubmit
});
</script>
<style lang="scss" scoped>
.table-container {
	::v-deep .el-descriptions__label.el-descriptions__cell.is-bordered-label {
		background-color: #f4f4f5;
	}
}
</style>
