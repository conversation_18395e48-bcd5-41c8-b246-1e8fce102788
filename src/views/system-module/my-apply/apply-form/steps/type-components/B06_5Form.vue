<!-- B06-5 船岸联合应急演习记录表 -->
<template>
	<div class="table-container">
		<MetaForm
			mode="add"
			ref="form"
			:formConfig="formConfig"
			:styleConfig="{ mode: 'page', labelPosition: 'top', createBtnText: '确定' }"
			style="margin-bottom: -36px"
		>
			<template #records>
				<div v-if="form" style="width: 100%">
					<InEditTable :columns="noticeColumns" v-model="form.formData.records" />
				</div>
			</template>
			<template #foot-text>
				<span style="color: #606266">注：本表由SMS办主管记录，经应急总指挥签认后，一份SMS办主管自留，一份海务经理留存。</span>
			</template>
			<template #button> </template>
		</MetaForm>
	</div>
</template>
<script setup lang="ts">
import { ref, reactive, inject, Ref, onMounted } from "vue";

import MetaForm from "@/meta-components/MetaForm/index.vue";
import InEditTable, { ColumnProps } from "../../components/InEditTable.vue";
import { UploadService } from "@/api/modules/upload";

import { FormConfigOptions, FormItemOptions } from "@/meta-components/MetaForm/interface";

const noticeColumns: ColumnProps[] = [
	{
		prop: "index",
		type: "index",
		label: "行号"
	},
	{
		prop: "participants",
		label: "参加人员",
		type: "input"
	},
	{
		prop: "arriveTime",
		label: "到达时间",
		type: "input"
	},
	{
		prop: "participantsSecond",
		label: "参加人员",
		type: "input"
	},
	{
		prop: "arriveTimeSecond",
		label: "到达时间",
		type: "input"
	}
];

const formContent: FormItemOptions[] = reactive([
	{
		label: "日期",
		name: "date",
		type: "date"
	},
	{
		label: "项目",
		name: "project",
		type: "input",
		otherAttr: {
			max: 100,
			showWordLimit: false
		}
	},
	{
		label: "应急总指挥",
		name: "direct",
		type: "input",
		otherAttr: {
			max: 100,
			showWordLimit: false
		}
	},
	{
		label: "集合地点",
		name: "assemblingPlace",
		type: "input",
		otherAttr: {
			max: 100,
			showWordLimit: false
		}
	},
	{
		label: "",
		name: "records",
		type: "slot"
	},
	{
		label: "演习和训练情况",
		name: "trainSituation",
		type: "textarea",
		otherAttr: {
			max: 5000,
			showWordLimit: false
		}
	},
	{
		label: "讲评",
		name: "comment",
		type: "textarea",
		otherAttr: {
			max: 5000,
			showWordLimit: false
		}
	},
	{
		label: "指定人员评价",
		name: "personnelOpinion",
		type: "textarea",
		otherAttr: {
			max: 5000,
			showWordLimit: false
		}
	},
	{
		label: "记录人",
		name: "recorder",
		type: "input",
		otherAttr: {
			max: 100,
			showWordLimit: false
		}
	},
	{
		label: "应急总指挥",
		name: "endDirect",
		type: "input",
		otherAttr: {
			max: 100,
			showWordLimit: false
		}
	},
	{
		name: "foot-text",
		type: "slot"
	},
	{
		name: "files",
		label: "上传文件",
		type: "fileUpload",
		span: 14,
		api: UploadService.ossApi,
		hint: "注：请上传文件或者图片，支持pdf、doc、docx、xls、xlsx、ceb、pptx等格式，最大限制30MB",
		otherAttr: { showUploadPlaceholder: false },
		imageOptions: {
			pathName: "system-apply",
			mode: "multiple",
			limitNum: 50,
			maxFileSize: 30,
			accept: ".pdf,.doc,.docx,.xls,.xlsx,.ceb,.pptx,image/*"
		}
	}
]);
const formConfig: FormConfigOptions = reactive({
	key: "system-apply-B10-4",
	items: formContent,
	detailKey: "uuid",
	closeFunction: close,
	closeFormAt: "both",
	hintMessage: ["创建成功", "保存成功"]
});
const formData = inject("formData") as Ref<{ [key: string]: any }>;
const form = ref();

// beforeSubmit 修改提交前conetent数据
const beforeSubmit = (data: any) => {
	const shipIds = data.shipIds || [];
	formData.value.shipIds = shipIds;

	return data;
};
onMounted(() => {
	// 新增or编辑 重新进入该类别页面 加载默认值
	if (!form.value.formData.records) {
		form.value.formData.records = [{}, {}, {}];
	}
});
defineExpose({
	form,
	beforeSubmit
});
</script>
<style lang="scss" scoped></style>
