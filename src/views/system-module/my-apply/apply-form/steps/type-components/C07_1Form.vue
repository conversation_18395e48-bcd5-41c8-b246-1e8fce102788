<!-- C07-1SMS文件审查会签表 -->
<template>
	<div class="table-container">
		<MetaForm
			mode="add"
			ref="form"
			:formConfig="formConfig"
			:styleConfig="{ mode: 'page', labelPosition: 'top', createBtnText: '确定' }"
			style="margin-bottom: -36px"
			:rule-form="ruleForm"
		>
			<template #info>
				<el-descriptions v-if="form" title="" border :column="2" size="large" style="width: 100%">
					<el-descriptions-item label="会签文件名称" align="left">
						<el-input v-model="form.formData.fileName" />
					</el-descriptions-item>
					<el-descriptions-item label="文件类别" align="left">
						<el-input v-model="form.formData.fileType" />
					</el-descriptions-item>
					<el-descriptions-item label="文件编写部门/人员" align="left">
						<el-input v-model="form.formData.fileUser" />
					</el-descriptions-item>
					<el-descriptions-item label="文件编号" align="left">
						<el-input v-model="form.formData.fileNo" />
					</el-descriptions-item>
				</el-descriptions>
			</template>
			<template #purpose>
				<el-table :data="tableData" style="width: 100%" size="small" border :header-cell-style="{ textAlign: 'center' }">
					<el-table-column prop="text" label="文件会签目的：" align="center" />
				</el-table>
			</template>
			<template #signUsers>
				<div v-if="form" style="width: 100%">
					<InEditTable :columns="inEditColumns" v-model="form.formData.signUsers" :canDelCondition="canDelCondition" />
				</div>
			</template>
			<template #foot-text>
				<span style="color: #606266">注：1、本表一式二份，一份由文件编制人员/部门留存，一份报SMS办主管备案。</span>
			</template>
			<template #button> </template>
		</MetaForm>
	</div>
</template>
<script setup lang="ts">
import {
	ref,
	reactive,
	onMounted,
	nextTick
	// inject
} from "vue";

import MetaForm from "@/meta-components/MetaForm/index.vue";
import InEditTable, { ColumnProps } from "../../components/InEditTable.vue";
import { UploadService } from "@/api/modules/upload";

import { FormConfigOptions, FormItemOptions } from "@/meta-components/MetaForm/interface";
// import { useRoute } from "vue-router";

// const mode = useRoute().query.mode;
const tableData = [
	{
		text: "1、确认文件格式是否符合标准要求;"
	},
	{
		text: "2、确认文件规定能否满足安全管理规定;"
	},
	{
		text: "3、确认文件规定能否完全执行;"
	},
	{
		text: "4、确认职责、工作接口是否明确;"
	},
	{
		text: "5、其他需确认的内容。"
	}
];
const formContent: FormItemOptions[] = reactive([
	// 会签信息
	{ name: "info", type: "slot" },
	// 会签目的
	{ name: "purpose", type: "slot" },
	{ label: "", name: "signUsers", type: "slot" },
	{
		name: "foot-text",
		type: "slot"
	},
	{
		name: "files",
		label: "上传文件",
		type: "fileUpload",
		span: 14,
		api: UploadService.ossApi,
		hint: "注：请上传文件或者图片，支持pdf、docx、xls、xlsx、ceb、pptx等格式，最大限制30MB",
		otherAttr: { showUploadPlaceholder: false },
		imageOptions: {
			pathName: "system-apply",
			mode: "multiple",
			limitNum: 50,
			maxFileSize: 30,
			accept: ".pdf,.doc,.docx,.xls,.xlsx,.ceb,.pptx,image/*"
		}
	}
]);
const formConfig: FormConfigOptions = reactive({
	key: "editformContent-trade",
	items: formContent,
	detailKey: "uuid",
	closeFunction: close,
	closeFormAt: "both",
	hintMessage: ["创建成功", "保存成功"]
});
const ruleForm = reactive({
	signUsers: [
		{
			required: true,
			message: "请选择相关会签人员"
		}
	]
});
const inEditColumns: ColumnProps[] = [
	{
		prop: "name",
		label: "行号",
		type: "index"
	},
	{
		prop: "departType",
		label: "部门名称",
		type: "text",
		defaultValue: "相关责任人员/部门会签",
		mergeInSave: true,
		align: "center",
		width: "180px",
		colSpan: 0
	},
	{
		prop: "departName",
		label: "部门名称",
		type: "input",
		align: "center",
		width: "180px",
		colSpan: 2
	},
	{
		prop: "advice",
		label: "部门确认意见（签名/日期）",
		align: "center",
		type: "input"
	}
];
const canDelCondition = (row: any) => {
	return row.departType === "相关责任人员/部门会签";
};
// const formData = inject("formData") as Ref<{[key: string]: any }>;
const form = ref();

const beforeSubmit = (data: any) => {
	// 去除空的signUsers
	// data.signUsers = data.signUsers.filter((item: any) => item.departName || item.advice);
	return data;
};

onMounted(() => {
	// 新增or编辑 重新进入该类别页面 加载默认值
	nextTick(() => {
		if (!form.value.formData.signUsers) {
			form.value.formData.signUsers = [
				{ departType: "文件编制人员" },
				{ departType: "相关责任人员/部门会签" },
				{ departType: "相关责任人员/部门会签" },
				{ departType: "相关责任人员/部门会签" }
			];
		}
	});
});
defineExpose({
	beforeSubmit,
	form
});
</script>
<style lang="scss" scoped>
.table-container {
	::v-deep .el-descriptions__label.el-descriptions__cell.is-bordered-label {
		background-color: #f4f4f5;
	}
}
</style>
