<!-- B03-1 船岸联合应急演习记录表 -->
<template>
	<div class="table-container">
		<MetaForm
			mode="add"
			ref="form"
			:formConfig="formConfig"
			:styleConfig="{ mode: 'page', labelPosition: 'top', createBtnText: '确定' }"
			style="margin-bottom: -36px"
		>
			<template #records>
				<div v-if="form" style="width: 100%">
					<InEditTable :columns="columns" v-model="form.formData.records" />
				</div>
			</template>
			<template #from>
				<div v-if="form" class="from-radio-container">
					<span style="color: #606266">提出</span>
					<el-radio-group v-model="form.formData.fromType" @change="deleteFrom">
						<el-radio label="部门" value="部门">部门</el-radio>
						<el-radio label="船舶" value="船舶">船舶</el-radio>
						<el-radio label="人员" value="人员">人员</el-radio>
					</el-radio-group>
					<span style="color: #606266">：</span>
					<div class="from-input">
						<el-input v-if="form.formData.fromType === '人员'" v-model="form.formData.from" placeholder="请输入人员" clearable />
						<el-select
							value-key="id"
							v-else-if="form.formData.fromType === '船舶'"
							v-model="form.formData.from"
							placeholder="请选择船舶"
							clearable
						>
							<el-option
								v-for="shipOption in shipOptions"
								:key="shipOption.value"
								:label="shipOption.label"
								:value="shipOption.data"
							/>
						</el-select>
						<el-cascader
							style="width: 100%"
							v-else-if="form.formData.fromType === '部门'"
							v-model="form.formData.from"
							:options="departOptions"
							placeholder="请选择部门"
							clearable
						/>
					</div>
				</div>
			</template>
			<template #foot-text>
				<span style="color: #606266">注：本表一式二份，一份提出部门留存，一份报SMS办主管。</span>
			</template>
			<template #button> </template>
		</MetaForm>
	</div>
</template>
<script setup lang="ts">
import { ref, reactive, inject, Ref, onMounted } from "vue";

import MetaForm from "@/meta-components/MetaForm/index.vue";
import InEditTable, { ColumnProps } from "../../components/InEditTable.vue";
import { UploadService } from "@/api/modules/upload";

import { FormConfigOptions, FormItemOptions } from "@/meta-components/MetaForm/interface";
import { useShipOptions, useDepartOptions } from "@/views/system-module/hooks/useOptions";

const { options: shipOptions } = useShipOptions();
const { options: departOptions } = useDepartOptions();

const columns: ColumnProps[] = [
	{
		prop: "index",
		type: "index",
		label: "行号"
	},
	{
		prop: "colIndex",
		type: "input",
		label: "序号",
		width: "80px"
	},
	{
		prop: "project",
		label: "培训项目",
		type: "input"
	},
	{
		prop: "methods",
		label: "培训方式",
		type: "input"
	},
	{
		prop: "participant",
		label: "参加对象",
		type: "input"
	},
	{
		prop: "plannedTime",
		label: "计划实施时间",
		type: "dateTimePicker"
	}
];

const formContent: FormItemOptions[] = reactive([
	{
		label: "",
		name: "records",
		type: "slot"
	},
	{
		label: "备注",
		name: "remark",
		type: "textarea",
		otherAttr: {
			max: 5000,
			showWordLimit: false
		}
	},
	{
		label: "",
		name: "from",
		type: "slot",
		span: 24
	},
	{
		label: "提出时间",
		name: "exerciseTime",
		type: "dateTime"
	},
	{
		label: "审批人意见",
		name: "comment",
		type: "textarea",
		otherAttr: {
			max: 5000,
			showWordLimit: false
		}
	},
	{
		name: "foot-text",
		type: "slot"
	},
	{
		name: "files",
		label: "上传文件",
		type: "fileUpload",
		span: 14,
		api: UploadService.ossApi,
		hint: "注：请上传文件或者图片，支持pdf、doc、docx、xls、xlsx、ceb、pptx等格式，最大限制30MB",
		otherAttr: { showUploadPlaceholder: false },
		imageOptions: {
			pathName: "system-apply",
			mode: "multiple",
			limitNum: 50,
			maxFileSize: 30,
			accept: ".pdf,.doc,.docx,.xls,.xlsx,.ceb,.pptx,image/*"
		}
	}
]);
const formConfig: FormConfigOptions = reactive({
	key: "system-apply-B10-4",
	items: formContent,
	detailKey: "uuid",
	closeFunction: close,
	closeFormAt: "both",
	hintMessage: ["创建成功", "保存成功"]
});
const formData = inject("formData") as Ref<{ [key: string]: any }>;
const form = ref();
const deleteFrom = () => {
	if (form.value.formData.from) {
		delete form.value.formData.from;
	}
};
// beforeSubmit 修改提交前conetent数据
const beforeSubmit = (data: any) => {
	data.fromName = "";
	formData.value.shipIds = [];
	formData.value.departTypes = [];
	formData.value.departIds = [];

	const fromType = data.fromType;
	if (data.from) {
		if (fromType === "船舶") {
			formData.value.shipIds = data.from.id ? [data.from.id] : [];
			data.fromName = data.from.name;
		} else if (fromType === "部门") {
			formData.value.departIds = [data.from[data.from.length - 1].id];
			formData.value.departTypes = data.from[0] ? [data.from[0].id] : [];
			data.fromName = data.from.map((item: any) => item.name).join("/");
		} else if (fromType === "人员") {
			data.fromName = data.from;
		}
	}

	return data;
};
onMounted(() => {
	// 新增or编辑 重新进入该类别页面 加载默认值
	if (!form.value.formData.records) {
		form.value.formData.records = [{}, {}, {}];
	}
});
defineExpose({
	form,
	beforeSubmit
});
</script>
<style lang="scss" scoped>
.table-container {
	::v-deep .el-descriptions__label.el-descriptions__cell.is-bordered-label {
		background-color: #f4f4f5;
	}
	.from-radio-container {
		display: flex;
		justify-content: flex-start;
		width: 100%;
		::v-deep .el-radio {
			margin-right: 6px;
			margin-left: 12px;
		}
		.from-input {
			flex: 1;
			width: auto;
		}

		// ::v-deep .el-input {
		// 	flex: 1;
		// 	width: auto;
		// }
	}
}
</style>
