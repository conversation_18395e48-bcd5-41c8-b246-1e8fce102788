<!-- B10-1 文件修改申请审核表 -->
<template>
	<div class="table-container">
		<MetaForm
			mode="add"
			ref="form"
			:formConfig="formConfig"
			:styleConfig="{ mode: 'page', labelPosition: 'top', createBtnText: '确定' }"
			style="margin-bottom: -36px"
			:rule-form="ruleForm"
		>
			<template #info>
				<el-descriptions v-if="form" title="" border :column="4" size="large" style="width: 100%" direction="vertical">
					<el-descriptions-item label="文件名称" align="left">
						<el-input v-model="form.formData.fileName" />
					</el-descriptions-item>
					<el-descriptions-item label="编号（版次）" align="left">
						<el-input v-model="form.formData.version" />
					</el-descriptions-item>
					<el-descriptions-item label="拟修改页码" align="left">
						<el-input v-model="form.formData.pageNumber" />
					</el-descriptions-item>
					<el-descriptions-item label="申请部门" align="left">
						<el-input v-model="form.formData.department" />
					</el-descriptions-item>
				</el-descriptions>
			</template>
			<template #auditee>
				<div v-if="form" style="width: 100%">
					<InEditTable :columns="inEditColumns" v-model="form.formData.auditee" />
				</div>
			</template>
			<template #foot-text>
				<span style="color: #606266">注：本表一式一份，完成后SMS办留存。</span>
			</template>
			<template #button> </template>
		</MetaForm>
	</div>
</template>
<script setup lang="ts">
import {
	ref,
	reactive,
	onMounted
	// inject
} from "vue";

import MetaForm from "@/meta-components/MetaForm/index.vue";
import InEditTable, { ColumnProps } from "../../components/InEditTable.vue";
import { UploadService } from "@/api/modules/upload";

import { FormConfigOptions, FormItemOptions } from "@/meta-components/MetaForm/interface";
// import { useRoute } from "vue-router";

// const mode = useRoute().query.mode;
const formContent: FormItemOptions[] = reactive([
	{
		label: "编号",
		type: "input",
		name: "no",
		span: 24,
		otherAttr: {
			max: 100,
			showWordLimit: false
		}
	},
	{ name: "info", type: "slot" },
	{
		label: "更改前内容",
		name: "contentBefore",
		type: "textarea",
		span: 24,
		otherAttr: {
			max: 5000,
			showWordLimit: false
		}
	},
	{
		label: "更改后内容",
		name: "contentAfter",
		type: "textarea",
		span: 24,
		otherAttr: {
			max: 5000,
			showWordLimit: false
		}
	},
	{
		label: "申请更改原因",
		name: "reason",
		type: "textarea",
		span: 24,
		otherAttr: {
			max: 5000,
			showWordLimit: false
		}
	},
	{
		label: "更改审核意见",
		name: "advice",
		type: "textarea",
		span: 24,
		otherAttr: {
			max: 5000,
			showWordLimit: false
		}
	},
	{
		label: "批准",
		name: "approve",
		type: "textarea",
		span: 24,
		otherAttr: {
			max: 5000,
			showWordLimit: false
		}
	},
	{
		label: "备注",
		name: "remark",
		type: "textarea",
		span: 24,
		otherAttr: {
			max: 5000,
			showWordLimit: false
		}
	},
	{
		name: "foot-text",
		type: "slot"
	},
	{
		name: "files",
		label: "上传文件",
		type: "fileUpload",
		span: 14,
		api: UploadService.ossApi,
		hint: "注：请上传文件或者图片，支持pdf、docx、xls、xlsx、ceb、pptx等格式，最大限制30MB",
		otherAttr: { showUploadPlaceholder: false },
		imageOptions: {
			pathName: "system-apply",
			mode: "multiple",
			limitNum: 50,
			maxFileSize: 30,
			accept: ".pdf,.doc,.docx,.xls,.xlsx,.ceb,.pptx,image/*"
		}
	}
]);
const formConfig: FormConfigOptions = reactive({
	key: "editformContent-trade",
	items: formContent,
	detailKey: "uuid",
	closeFunction: close,
	closeFormAt: "both",
	hintMessage: ["创建成功", "保存成功"]
});
const ruleForm = reactive({
	auditee: [
		{
			required: true,
			message: "请填写被审核人员"
		}
	]
});
const inEditColumns: ColumnProps[] = [
	{
		prop: "name",
		label: "行号",
		type: "index"
	},
	{
		prop: "position",
		label: "职务",
		type: "input",
		align: "center"
	},
	{
		prop: "name",
		label: "姓名",
		type: "input",
		align: "center"
	}
];

// const formData = inject("formData") as Ref<{[key: string]: any }>;
const form = ref();

const beforeSubmit = (data: any) => {
	// 删除空的被审核人员
	// data.auditee = data.auditee.filter((item: any) => item.position || item.name);
	return data;
};

onMounted(() => {
	// 新增or编辑 重新进入该类别页面 加载默认值
	if (!form.value.formData.auditee) {
		form.value.formData.auditee = [{}, {}, {}];
	}
});

defineExpose({
	beforeSubmit,
	form
});
</script>
<style lang="scss" scoped>
.table-container {
	::v-deep .el-descriptions__label.el-descriptions__cell.is-bordered-label {
		background-color: #f4f4f5;
	}
}
</style>
