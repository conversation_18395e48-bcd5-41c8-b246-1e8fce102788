<!-- B07-1 不符合规定情况、事故和险情的报告、分析和纠正记录表 -->
<template>
	<div class="table-container">
		<MetaForm
			mode="add"
			ref="form"
			:formConfig="formConfig"
			:styleConfig="{ mode: 'page', labelPosition: 'top', createBtnText: '确定' }"
			style="margin-bottom: -36px"
			:rule-form="ruleForm"
		>
			<template #foot-text>
				<span style="color: #606266"
					>注：在对适用对象前的方框中打“√”，不适用打“—”。 本表一式二份，部门/船舶一份，指定人员留存一份。</span
				>
			</template>
			<template #button> </template>
		</MetaForm>
	</div>
</template>
<script setup lang="ts">
import { ref, reactive, inject, Ref } from "vue";

import MetaForm from "@/meta-components/MetaForm/index.vue";
import { UploadService } from "@/api/modules/upload";
import { DepartService } from "@/api/modules/depart";

import { FormConfigOptions, FormItemOptions } from "@/meta-components/MetaForm/interface";
import { useShipOptions } from "../../../../hooks/useOptions";
import { GlobalStore } from "@/store";
import { reformDataToCas } from "../../../utils";
import { Apply } from "@/api/interface/system.model";

const globalStore = GlobalStore();
const { getOption } = useShipOptions();

const getTypeOptions = async () => {
	const res = await getOption();
	const shipOptions = res.data.list;

	// 获取部门数据
	const departOptions: any[] = [];
	const departRes = await DepartService.getAll({ corpId: globalStore.userInfo.corpId });
	if (departRes.data && departRes.data.departs) {
		const { departs } = departRes.data;
		departs.forEach(item => {
			const casItem: any = {
				value: { id: item.id, name: item.name },
				label: item.name!,
				children: item.children && item.children.length > 0 ? reformDataToCas(item.children, "name", ["id", "name"]) : []
			};
			if (item.type === Apply.DepartType.ONSHORE) {
				departOptions.push(casItem);
			}
		});
	}
	const casOptions = [
		{ label: "岸基部门", value: { name: "岸基部门" }, children: departOptions },
		{ label: "船舶", value: { name: "船舶" }, children: shipOptions.map(item => ({ label: item.label, value: item.data })) }
	];
	res.data.list = casOptions;
	return res;
};
const formContent: FormItemOptions[] = reactive([
	{
		label: "类别",
		name: "type",
		type: "checkbox",
		staticOptions: [
			{ label: "不符合规定情况", value: 1 },
			{ label: "事故", value: 2 },
			{ label: "险情", value: 3 }
		]
	},
	{
		label: "岸基部门/船舶",
		name: "departShips",
		type: "cascader",
		api: getTypeOptions,
		placeholder: "可多选",
		otherAttr: {
			multiple: true
		}
	},
	{
		label: "不符合规定情况、事故和险情简述",
		name: "resume",
		type: "textarea",
		otherAttr: {
			max: 5000,
			showWordLimit: false
		}
	},
	{
		label: "已采取措施",
		name: "measures",
		type: "textarea",
		otherAttr: {
			max: 5000,
			showWordLimit: false
		}
	},
	{
		label: "评审意见",
		name: "opinions",
		type: "textarea",
		otherAttr: {
			max: 5000,
			showWordLimit: false
		}
	},
	{
		label: "原因分析",
		name: "reasonAnalysis",
		type: "textarea",
		otherAttr: {
			max: 5000,
			showWordLimit: false
		}
	},
	{
		label: "计划采取的纠正措施",
		name: "plannedMeasures",
		type: "textarea",
		otherAttr: {
			max: 5000,
			showWordLimit: false
		}
	},
	{
		label: "拟定完成日期",
		name: "plannedDate",
		type: "textarea",
		otherAttr: {
			max: 5000,
			showWordLimit: false
		}
	},
	{
		label: "指定人员意见",
		name: "personnelOpinions",
		type: "textarea",
		otherAttr: {
			max: 5000,
			showWordLimit: false
		}
	},
	{
		label: "纠正措施的验证和结论",
		name: "conclusion",
		type: "checkbox",
		staticOptions: [
			{ label: "1、纠正措施是否按规定期限内完成", value: 1 },
			{ label: "2、实施情况是否记录", value: 2 }
		]
	},
	{
		label: "验证人",
		name: "verifier",
		type: "input",
		otherAttr: {
			max: 100,
			showWordLimit: false
		}
	},
	{
		label: "验证日期",
		name: "verifierDate",
		type: "date"
	},
	{
		name: "foot-text",
		type: "slot"
	},
	{
		name: "files",
		label: "上传文件",
		type: "fileUpload",
		span: 14,
		api: UploadService.ossApi,
		hint: "注：请上传文件或者图片，支持pdf、doc、docx、xls、xlsx、ceb、pptx等格式，最大限制30MB",
		otherAttr: { showUploadPlaceholder: false },
		imageOptions: {
			pathName: "system-apply",
			mode: "multiple",
			limitNum: 50,
			maxFileSize: 30,
			accept: ".pdf,.doc,.docx,.xls,.xlsx,.ceb,.pptx,image/*"
		}
	}
]);
const formConfig: FormConfigOptions = reactive({
	key: "system-apply-B10-4",
	items: formContent,
	detailKey: "uuid",
	closeFunction: close,
	closeFormAt: "both",
	hintMessage: ["创建成功", "保存成功"]
});
const ruleForm = reactive({
	verifier: [
		{
			required: true,
			message: "请输入验证人"
		}
	]
});
const formData = inject("formData") as Ref<{ [key: string]: any }>;
const form = ref();

// beforeSubmit 修改提交数据
const beforeSubmit = (data: any) => {
	const departShips = data.departShips || [];
	const selectShips = departShips.filter((item: any[]) => item[0].name == "船舶");
	const selectDeparts = departShips.filter((item: any[]) => item[0].name == "岸基部门");
	formData.value.shipIds = selectShips.map((item: any) => item[1].id);
	formData.value.departTypes = selectDeparts.length ? [Apply.DepartType.ONSHORE] : [];
	formData.value.departIds = selectDeparts.map((item: any) => item[item.length - 1].id);
	data.shipDepartNames = departShips.map((item: any[]) => {
		return item
			.map(item => item.name)
			.filter(Boolean)
			.join("/");
	});

	return data;
};

defineExpose({
	form,
	beforeSubmit
});
</script>
<style lang="scss" scoped></style>
