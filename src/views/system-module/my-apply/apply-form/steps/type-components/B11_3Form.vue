<!-- B11-3 审核会议记录 -->
<template>
	<div class="table-container">
		<MetaForm
			mode="add"
			ref="form"
			:formConfig="formConfig"
			:styleConfig="{ mode: 'page', labelPosition: 'top', createBtnText: '确定' }"
			style="margin-bottom: -36px"
			:rule-form="ruleForm"
		>
			<template #info>
				<el-descriptions v-if="form" title="" border :column="2" size="large" style="width: 100%">
					<el-descriptions-item label="岸基/船舶" align="left">
						<el-cascader v-model="form.formData.type" :props="{ multiple: true }" :options="typeOptions" style="width: 100%" />
					</el-descriptions-item>
					<el-descriptions-item label="审核性质" align="left">
						<el-input v-model="form.formData.property" />
					</el-descriptions-item>
					<el-descriptions-item label="实施时间" align="left">
						<el-input v-model="form.formData.effectiveDate" />
					</el-descriptions-item>
					<el-descriptions-item label="编号" align="left">
						<el-input v-model="form.formData.no" />
					</el-descriptions-item>
					<el-descriptions-item label="审核组长" align="left">
						<el-input v-model="form.formData.auditLeader" />
					</el-descriptions-item>
					<el-descriptions-item label="审核员" align="left">
						<el-input v-model="form.formData.auditor" />
					</el-descriptions-item>
				</el-descriptions>
			</template>
			<template #auditFinding>
				<el-descriptions v-if="form" title="" border :column="1" size="large" style="width: 100%">
					<el-descriptions-item label="共发现问题" align="left">
						<el-input v-model="form.formData.probleamCount" style="width: 240px">
							<template #append> 个。 </template>
						</el-input>
					</el-descriptions-item>
					<el-descriptions-item label="不符合规定情况" align="left">
						<el-input v-model="form.formData.notConformCount" style="width: 240px">
							<template #append> 个。 </template>
						</el-input>
					</el-descriptions-item>
				</el-descriptions>
			</template>
			<template #foot-text>
				<span style="color: #606266">注：本表完成后由指定人员存档。</span>
			</template>
			<template #button> </template>
		</MetaForm>
	</div>
</template>
<script setup lang="ts">
import { ref, reactive, computed, inject, Ref } from "vue";

import MetaForm from "@/meta-components/MetaForm/index.vue";
import { UploadService } from "@/api/modules/upload";

import { FormConfigOptions, FormItemOptions } from "@/meta-components/MetaForm/interface";
import { useShipOptions } from "@/views/system-module/hooks/useOptions";
import { Apply } from "@/api/interface/system.model";
// import { useRoute } from "vue-router";

// const mode = useRoute().query.mode;
const { options: shipOptions } = useShipOptions();
const typeOptions = computed(() => {
	return [
		{
			value: "岸基",
			label: "岸基"
		},
		{
			value: "船舶",
			label: "船舶",
			children: shipOptions.value?.map(item => ({ value: item.data, label: item.label })) ?? []
		}
	];
});
const formContent: FormItemOptions[] = reactive([
	{ name: "info", type: "slot" },
	{
		label: "首次会议开始时间",
		type: "dateTime",
		name: "firstStartTime",
		placeholder: "请选择日期时间",
		span: 24
	},
	{
		label: "首次会议结束时间",
		type: "dateTime",
		name: "firstEndTime",
		placeholder: "请选择日期时间",
		span: 24
	},
	{
		label: "首次会议参加人员",
		name: "participants",
		type: "input",
		span: 24,
		otherAttr: {
			max: 100,
			showWordLimit: false
		}
	},
	{
		label: "现场审核开始时间",
		type: "dateTime",
		name: "auditStartTime",
		placeholder: "请选择日期时间",
		span: 24
	},
	{
		label: "结束时间",
		type: "dateTime",
		name: "auditEndTime",
		placeholder: "请选择日期时间",
		span: 24
	},
	{
		label: "审核目的",
		type: "input",
		name: "purpose",
		span: 24,
		otherAttr: {
			max: 100,
			showWordLimit: false
		}
	},
	{
		label: "审核依据",
		type: "input",
		name: "basis",
		span: 24,
		otherAttr: {
			max: 100,
			showWordLimit: false
		}
	},
	{
		label: "审核范围",
		type: "input",
		name: "scope",
		span: 24,
		otherAttr: {
			max: 100,
			showWordLimit: false
		}
	},
	{
		label: "审核方法",
		type: "input",
		name: "method",
		span: 24,
		otherAttr: {
			max: 100,
			showWordLimit: false
		}
	},
	{
		label: "其它",
		type: "input",
		name: "other",
		span: 24,
		otherAttr: {
			max: 100,
			showWordLimit: false
		}
	},
	{
		label: "末次会议开始时间",
		type: "dateTime",
		name: "lastStartTime",
		placeholder: "请选择日期时间",
		span: 24
	},
	{
		label: "末次会议结束时间",
		type: "dateTime",
		name: "lastEndTime",
		placeholder: "请选择日期时间",
		span: 24
	},
	{
		label: "末次会议参加人员",
		name: "lastParticipants",
		type: "input",
		span: 24,
		otherAttr: {
			max: 100,
			showWordLimit: false
		}
	},
	{
		label: "审核结果",
		name: "auditFinding",
		type: "slot",
		span: 24
	},
	{
		name: "foot-text",
		type: "slot"
	},
	{
		name: "files",
		label: "上传文件",
		type: "fileUpload",
		span: 14,
		api: UploadService.ossApi,
		hint: "注：请上传文件或者图片，支持pdf、docx、xls、xlsx、ceb、pptx等格式，最大限制30MB",
		otherAttr: { showUploadPlaceholder: false },
		imageOptions: {
			pathName: "system-apply",
			mode: "multiple",
			limitNum: 50,
			maxFileSize: 30,
			accept: ".pdf,.doc,.docx,.xls,.xlsx,.ceb,.pptx,image/*"
		}
	}
]);
const formConfig: FormConfigOptions = reactive({
	key: "editformContent-trade",
	items: formContent,
	detailKey: "uuid",
	closeFunction: close,
	closeFormAt: "both",
	hintMessage: ["创建成功", "保存成功"]
});
const ruleForm = reactive({
	participants: [
		{
			required: true,
			message: "请填写首次会议参加人员"
		}
	],
	lastParticipants: [
		{
			required: true,
			message: "请填写末次会议参加人员"
		}
	]
});

const formData = inject("formData") as Ref<{ [key: string]: any }>;
const form = ref();
// beforeSubmit 修改提交前conetent数据
const beforeSubmit = (data: any) => {
	const type = data.type || [];
	const selectShips = type.filter((item: any[]) => item[0] === "船舶");
	const selectDeparts = type.filter((item: any[]) => item[0] === "岸基");
	const shipIds = selectShips.map((item: any[]) => item[1]?.id);
	// 将shipIds关联到主表单
	formData.value.shipIds = shipIds;
	// 选择了岸基 关联岸基type
	if (selectDeparts?.length) {
		formData.value.departTypes = [Apply.DepartType.ONSHORE];
	} else {
		formData.value.departTypes = [];
	}
	data.typeNames = type.map((item: any[]) => {
		if (item[1]) {
			// 存储对应船舶名称
			return item[1].name;
		} else {
			//存储"岸基"
			return item[0];
		}
	});

	return data;
};
defineExpose({
	beforeSubmit,
	form
});
</script>
<style lang="scss" scoped>
.table-container {
	::v-deep .el-descriptions__label.el-descriptions__cell.is-bordered-label {
		background-color: #f4f4f5;
	}
}
</style>
