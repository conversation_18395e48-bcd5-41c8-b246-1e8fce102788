<!-- B10-3 文件收发登记表 -->
<template>
	<div class="table-container">
		<MetaForm
			mode="add"
			ref="form"
			:formConfig="formConfig"
			:styleConfig="{ mode: 'page', labelPosition: 'top', createBtnText: '确定' }"
			style="margin-bottom: -36px"
		>
			<template #foot-text>
				<span style="color: #606266"
					>文件类别：1、SMS文件；2、规范规则、守则等；3、海图；4、航海通告；5、航海出版物；6、其他文件。 注：
					本表一式一份，SMS办保存。</span
				>
			</template>
			<template #records>
				<div v-if="form" style="width: 100%">
					<InEditTable :columns="inEditColumns" v-model="form.formData.records" />
				</div>
			</template>
			<template #button> </template>
		</MetaForm>
	</div>
</template>
<script setup lang="ts">
import {
	ref,
	reactive,
	onMounted
	//  inject,
} from "vue";

import MetaForm from "@/meta-components/MetaForm/index.vue";
import InEditTable, { ColumnProps } from "../../components/InEditTable.vue";
import { UploadService } from "@/api/modules/upload";

import { FormConfigOptions, FormItemOptions } from "@/meta-components/MetaForm/interface";

const inEditColumns: ColumnProps[] = [
	{
		prop: "index",
		type: "index",
		label: "行号"
	},
	{
		prop: "colIndex",
		type: "input",
		label: "序号"
	},
	{
		prop: "sendDate",
		label: "时间",
		type: "datePicker"
	},
	{
		prop: "nameType",
		label: "文件名称、类别",
		type: "input"
	},
	{
		prop: "sendUser",
		label: "发放部门/人员",
		type: "input"
	},
	{
		prop: "count",
		label: "份数",
		type: "input"
	},
	{
		prop: "receiveUser",
		label: "接收部门/人员",
		type: "input"
	},
	{
		prop: "sendMethod",
		label: "发放方式",
		type: "input"
	},
	{
		prop: "remark",
		label: "备注",
		type: "input"
	}
];
const formContent: FormItemOptions[] = reactive([
	{
		label: "",
		name: "records",
		type: "slot"
	},
	{
		name: "foot-text",
		type: "slot"
	},
	{
		name: "files",
		label: "上传文件",
		type: "fileUpload",
		span: 14,
		api: UploadService.ossApi,
		hint: "注：请上传文件或者图片，支持pdf、doc、docx、xls、xlsx、ceb、pptx等格式，最大限制30MB",
		otherAttr: { showUploadPlaceholder: false },
		imageOptions: {
			pathName: "system-apply",
			mode: "multiple",
			limitNum: 50,
			maxFileSize: 30,
			accept: ".pdf,.doc,.docx,.xls,.xlsx,.ceb,.pptx,image/*"
		}
	}
]);
const formConfig: FormConfigOptions = reactive({
	key: "system-apply-B10-4",
	items: formContent,
	detailKey: "uuid",
	closeFunction: close,
	closeFormAt: "both",
	hintMessage: ["创建成功", "保存成功"]
});
// const formData = inject("formData") as Ref<{[key: string]: any }>;
const form = ref();

// beforeSubmit 修改提交前conetent数据
// const beforeSubmit = () => {
// };
onMounted(() => {
	// 新增or编辑 重新进入该类别页面 加载默认值
	if (!form.value.formData.records) {
		form.value.formData.records = [{}, {}, {}];
	}
});
defineExpose({
	form
	// beforeSubmit
});
</script>
<style lang="scss" scoped>
.table-container {
	::v-deep .el-descriptions__label.el-descriptions__cell.is-bordered-label {
		background-color: #f4f4f5;
	}
}
</style>
