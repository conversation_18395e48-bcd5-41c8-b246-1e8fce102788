<!-- B12-1 SMS有效性评价/管理复查通知单 -->
<template>
	<div class="table-container">
		<MetaForm
			mode="add"
			ref="form"
			:formConfig="formConfig"
			:styleConfig="{ mode: 'page', labelPosition: 'top', createBtnText: '确定' }"
			style="margin-bottom: -36px"
			:rule-form="ruleForm"
		>
			<template #foot-text>
				<span style="color: #606266"
					>注：1、有效性评价本表由SMS办主管编制，完成后发送SMS有关各部门相关人员留存；
					2、管理复查本表由指定人员编制，完成后交SMS办主管发送SMS有关各部门相关人员留存。</span
				>
			</template>
			<template #button> </template>
		</MetaForm>
	</div>
</template>
<script setup lang="ts">
import { ref, reactive, inject, Ref } from "vue";

import MetaForm from "@/meta-components/MetaForm/index.vue";
import { UploadService } from "@/api/modules/upload";

import { FormConfigOptions, FormItemOptions } from "@/meta-components/MetaForm/interface";
import { useShipOptions } from "../../../../hooks/useOptions";

const { getOption } = useShipOptions();
const formContent: FormItemOptions[] = reactive([
	{
		label: "类别",
		name: "type",
		type: "checkbox",
		staticOptions: [
			{ label: "有效性评价", value: 1 },
			{ label: "管理复查", value: 2 }
		]
	},
	{
		label: "拟召开时间",
		name: "startTime",
		type: "input",
		placeholder: "请输入日期",
		otherAttr: {
			max: 100,
			showWordLimit: false
		}
	},
	{
		label: "有效性评价或复查的原因",
		name: "reason",
		type: "textarea",
		placeholder: "请输入",
		otherAttr: {
			max: 5000,
			showWordLimit: false
		}
	},
	{
		label: "参加人员",
		name: "participants",
		type: "textarea",
		placeholder: "请输入",
		otherAttr: {
			max: 5000,
			showWordLimit: false
		}
	},
	{
		label: "评价/复查主要内容",
		name: "reviewContent",
		type: "textarea",
		placeholder: "请输入",
		otherAttr: {
			max: 5000,
			showWordLimit: false
		}
	},
	{
		label: "备注",
		name: "remark",
		type: "textarea",
		placeholder: "请输入",
		otherAttr: {
			max: 5000,
			showWordLimit: false
		}
	},
	{
		label: "制定人",
		name: "developer",
		type: "input",
		otherAttr: {
			max: 100,
			showWordLimit: false
		}
	},
	{
		label: "审核人",
		name: "reviewer",
		type: "input",
		otherAttr: {
			max: 100,
			showWordLimit: false
		}
	},
	{
		label: "批准人",
		name: "approver",
		type: "input",
		otherAttr: {
			max: 100,
			showWordLimit: false
		}
	},
	{
		label: "日期",
		name: "time",
		type: "date",
		otherAttr: {
			max: 100,
			showWordLimit: false
		}
	},
	{
		label: "选择船舶",
		name: "shipIds",
		type: "singleSelect",
		placeholder: "请选择船舶",
		api: getOption,
		otherAttr: {
			multiple: true,
			labelKey: "label",
			valueKey: "value",
			appendLabelToFormDataWithKey: "shipNames"
		}
	},
	{
		name: "foot-text",
		type: "slot"
	},
	{
		name: "files",
		label: "上传文件",
		type: "fileUpload",
		span: 14,
		api: UploadService.ossApi,
		hint: "注：请上传文件或者图片，支持pdf、doc、docx、xls、xlsx、ceb、pptx等格式，最大限制30MB",
		otherAttr: { showUploadPlaceholder: false },
		imageOptions: {
			pathName: "system-apply",
			mode: "multiple",
			limitNum: 50,
			maxFileSize: 30,
			accept: ".pdf,.doc,.docx,.xls,.xlsx,.ceb,.pptx,image/*"
		}
	}
]);
const formConfig: FormConfigOptions = reactive({
	key: "system-apply-B10-4",
	items: formContent,
	detailKey: "uuid",
	closeFunction: close,
	closeFormAt: "both",
	hintMessage: ["创建成功", "保存成功"]
});
const ruleForm = reactive({
	signUser: [
		{
			required: true,
			message: "下发相关人员"
		}
	]
});
const formData = inject("formData") as Ref<{ [key: string]: any }>;
const form = ref();

// beforeSubmit 修改提交数据
const beforeSubmit = (data: any) => {
	//将船舶id保存到formData中
	formData.value.shipIds = data.shipIds;
	return data;
};

defineExpose({
	form,
	beforeSubmit
});
</script>
<style lang="scss" scoped>
.table-container {
	::v-deep .el-descriptions__label.el-descriptions__cell.is-bordered-label {
		background-color: #f4f4f5;
	}
}
</style>
