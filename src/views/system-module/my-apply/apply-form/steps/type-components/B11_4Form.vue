<!-- B11-4审核实施记录 -->
<template>
	<div class="table-container">
		<MetaForm
			mode="add"
			ref="form"
			:formConfig="formConfig"
			:styleConfig="{ mode: 'page', labelPosition: 'top', createBtnText: '确定' }"
			style="margin-bottom: -36px"
			:rule-form="ruleForm"
		>
			<template #info>
				<el-descriptions v-if="form" title="" border :column="2" size="large" style="width: 100%">
					<el-descriptions-item label="审核性质" align="left">
						<el-input v-model="form.formData.property" />
					</el-descriptions-item>
					<el-descriptions-item label="编号" align="left">
						<el-input v-model="form.formData.approvalNo" />
					</el-descriptions-item>
					<el-descriptions-item label="审核时间" align="left">
						<el-date-picker
							type="datetime"
							v-model="form.formData.approvalTime"
							placeholder="请选择日期时间"
							style="width: 100%"
							value-format="YYYY-MM-DD HH:mm:ss"
						/>
					</el-descriptions-item>
					<el-descriptions-item> </el-descriptions-item>
				</el-descriptions>
			</template>
			<template #auditee>
				<div v-if="form" style="width: 100%">
					<InEditTable :columns="inEditColumns" v-model="form.formData.auditee" />
				</div>
			</template>
			<template #foot-text>
				<span style="color: #606266">注：本表由审核员记录，完成后报指定人员存档。</span>
			</template>
			<template #button> </template>
		</MetaForm>
	</div>
</template>
<script setup lang="ts">
import {
	ref,
	reactive,
	onMounted
	// inject
} from "vue";

import MetaForm from "@/meta-components/MetaForm/index.vue";
import InEditTable, { ColumnProps } from "../../components/InEditTable.vue";
import { UploadService } from "@/api/modules/upload";

import { FormConfigOptions, FormItemOptions } from "@/meta-components/MetaForm/interface";
// import { useRoute } from "vue-router";

// const mode = useRoute().query.mode;
const formContent: FormItemOptions[] = reactive([
	{
		label: "受审核部门",
		type: "input",
		name: "departName",
		placeholder: "请输入受审核部门，可多个",
		span: 24,
		otherAttr: {
			max: 100,
			showWordLimit: false
		}
	},
	{ name: "info", type: "slot" },
	{
		label: "审核内容",
		name: "reviewContent",
		type: "textarea",
		placeholder: "请输入审核内容",
		span: 24,
		otherAttr: {
			max: 5000,
			showWordLimit: false
		}
	},
	{
		label: "发现问题描述",
		name: "problemDesc",
		type: "textarea",
		placeholder: "请输入发现问题描述",
		span: 24,
		otherAttr: {
			max: 5000,
			showWordLimit: false
		}
	},
	{
		label: "不符合规定描述",
		name: "notConformDesc",
		type: "textarea",
		placeholder: "请输入不符合规定描述",
		span: 24,
		otherAttr: {
			max: 5000,
			showWordLimit: false
		}
	},
	{
		label: "被审核人员",
		name: "auditee",
		type: "slot",
		placeholder: "请输入被审核人员",
		span: 24,
		otherAttr: {
			max: 100,
			showWordLimit: false
		}
	},
	{
		label: "审核员意见",
		name: "reviewerOpinion",
		type: "textarea",
		placeholder: "请输入审核员意见",
		span: 24,
		otherAttr: {
			max: 5000,
			showWordLimit: false
		}
	},
	{
		name: "foot-text",
		type: "slot"
	},
	{
		name: "files",
		label: "上传文件",
		type: "fileUpload",
		span: 14,
		api: UploadService.ossApi,
		hint: "注：请上传文件或者图片，支持pdf、docx、xls、xlsx、ceb、pptx等格式，最大限制30MB",
		otherAttr: { showUploadPlaceholder: false },
		imageOptions: {
			pathName: "system-apply",
			mode: "multiple",
			limitNum: 50,
			maxFileSize: 30,
			accept: ".pdf,.doc,.docx,.xls,.xlsx,.ceb,.pptx,image/*"
		}
	}
]);
const formConfig: FormConfigOptions = reactive({
	key: "editformContent-trade",
	items: formContent,
	detailKey: "uuid",
	closeFunction: close,
	closeFormAt: "both",
	hintMessage: ["创建成功", "保存成功"]
});
// 判断被审核人数据是否全空
const checkAuditeeEmpty = (rule: any, value: any, callback: any) => {
	const emptyAuditees = value.filter((item: { position: string; name: string }) => !item.position && !item.name);
	if (emptyAuditees.length === value.length) {
		callback(new Error("请填写被审核人员"));
	} else {
		callback();
	}
};
const ruleForm = reactive({
	auditee: [
		{
			required: true,
			message: "请填写被审核人员"
		},
		{ validator: checkAuditeeEmpty, trigger: "blur" }
	]
});
const inEditColumns: ColumnProps[] = [
	{
		prop: "name",
		label: "行号",
		type: "index"
	},
	{
		prop: "position",
		label: "职务",
		type: "input",
		align: "center"
	},
	{
		prop: "name",
		label: "姓名",
		type: "input",
		align: "center"
	}
];

// const formData = inject("formData") as Ref<{[key: string]: any }>;
const form = ref();

const beforeSubmit = (data: any) => {
	// 删除空的被审核人员
	// data.auditee = data.auditee.filter((item: any) => item.position || item.name);
	return data;
};

onMounted(() => {
	// 新增or编辑 重新进入该类别页面 加载默认值
	if (!form.value.formData.auditee) {
		form.value.formData.auditee = [{}, {}, {}];
	}
});

defineExpose({
	beforeSubmit,
	form
});
</script>
<style lang="scss" scoped>
.table-container {
	::v-deep .el-descriptions__label.el-descriptions__cell.is-bordered-label {
		background-color: #f4f4f5;
	}
}
</style>
