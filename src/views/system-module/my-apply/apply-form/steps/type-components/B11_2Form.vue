<!-- B11-2 审核通知书 -->
<template>
	<div class="table-container">
		<MetaForm
			mode="add"
			ref="form"
			:formConfig="formConfig"
			:styleConfig="{ mode: 'page', labelPosition: 'top', createBtnText: '确定' }"
			style="margin-bottom: -36px"
			:rule-form="ruleForm"
		>
			<template #notice>
				<el-descriptions direction="vertical" v-if="form" title="" border :column="2" size="large" style="width: 100%">
					<el-descriptions-item label="审核通知" label-align="center">
						兹定于上述审核时间内对你部门所涉及的SMS运行情况进行内部审核，现将本次《审核计划》的安排通知你们，希确定陪同人员并作好必要的准备工作（体系文件、体系活动记录、资料信息等）。
					</el-descriptions-item>
				</el-descriptions>
			</template>
			<template #audit>
				<el-table
					v-if="form"
					:data="form.formData.auditList"
					style="width: 100%"
					border
					:header-cell-style="{ textAlign: 'center' }"
				>
					<el-table-column prop="no" label="审核编号" align="center" />
					<el-table-column prop="name" label="姓名" align="center">
						<template #default="{ row }">
							<el-input v-model="row.name" placeholder="请输入" />
						</template>
					</el-table-column>
					<el-table-column prop="position" label="职务" align="center">
						<template #default="{ row }">
							<el-input v-model="row.position" placeholder="请输入" />
						</template>
					</el-table-column>
				</el-table>
			</template>
			<template #plan>
				<div v-if="form" style="width: 100%">
					<InEditTable :columns="inEditColumns" v-model="form.formData.plan" />
				</div>
			</template>
			<template #foot-text>
				<span style="color: #606266"
					>注：本通知和计划由审核组长下发，通知应发于被审人员、审核员及其它必要的人员并存档，书面不方便下发时，可以用电话或传真等形式先行通知，但实施时仍需相关人员确认。</span
				>
			</template>
			<template #button> </template>
		</MetaForm>
	</div>
</template>
<script setup lang="ts">
import { ref, reactive, onMounted, inject, Ref } from "vue";

import MetaForm from "@/meta-components/MetaForm/index.vue";
import { UploadService } from "@/api/modules/upload";
import InEditTable, { ColumnProps } from "../../components/InEditTable.vue";
import { useShipOptions } from "@/views/system-module/hooks/useOptions";

import { FormConfigOptions, FormItemOptions } from "@/meta-components/MetaForm/interface";
import { Apply } from "@/api/interface/system.model";
// import { useRoute } from "vue-router";

// const mode = useRoute().query.mode;
const { getOption } = useShipOptions();
const inEditColumns: ColumnProps[] = [
	{
		prop: "index",
		type: "index",
		label: "行号"
	},
	{
		prop: "subject",
		label: "被审对象",
		type: "input",
		align: "center"
	},

	{
		prop: "auditor",
		label: "审核员",
		type: "input",
		align: "center"
	},
	{
		prop: "time",
		label: "时间",
		align: "center",
		type: "input"
	}
];
const getTypeOptions = async () => {
	const res = await getOption();
	const shipOptions = res.data.list;
	const casOptions = [
		{ label: "岸基", value: "岸基" },
		{ label: "船舶", value: "船舶", children: shipOptions.map(item => ({ label: item.label, value: item.data })) }
	];
	res.data.list = casOptions;
	return res;
};
const formContent: FormItemOptions[] = reactive([
	{
		label: "发至岸基/船舶",
		name: "type",
		type: "cascader",
		placeholder: "可多选",
		api: getTypeOptions,
		span: 24,
		otherAttr: {
			multiple: true
		}
	},
	{
		label: "接收人/日期",
		name: "receiver",
		type: "input",
		span: 24,
		otherAttr: {
			max: 100,
			showWordLimit: false
		}
	},
	{
		label: "审核时间",
		name: "auditTime",
		type: "input",
		span: 24,
		otherAttr: {
			max: 100,
			showWordLimit: false
		}
	},
	// 审核通知
	{ name: "notice", type: "slot" },
	// 审核人
	{ name: "audit", type: "slot" },
	// 审核计划安排
	{ label: "审核计划安排", name: "plan", type: "slot" },
	{
		label: "被审核人应准备的资料",
		name: "auditInfo",
		type: "input",
		span: 24,
		otherAttr: {
			max: 100,
			showWordLimit: false
		}
	},
	{
		label: "备注",
		name: "remark",
		type: "textarea",
		span: 24,
		otherAttr: {
			max: 5000,
			showWordLimit: false
		}
	},

	{
		name: "foot-text",
		type: "slot"
	},
	{
		name: "files",
		label: "上传文件",
		type: "fileUpload",
		span: 14,
		api: UploadService.ossApi,
		hint: "注：请上传文件或者图片，支持pdf、docx、xls、xlsx、ceb、pptx等格式，最大限制30MB",
		otherAttr: { showUploadPlaceholder: false },
		imageOptions: {
			pathName: "system-apply",
			mode: "multiple",
			limitNum: 50,
			maxFileSize: 30,
			accept: ".pdf,.doc,.docx,.xls,.xlsx,.ceb,.pptx,image/*"
		}
	}
]);
const formConfig: FormConfigOptions = reactive({
	key: "editformContent-trade",
	items: formContent,
	detailKey: "uuid",
	closeFunction: close,
	closeFormAt: "both",
	hintMessage: ["创建成功", "保存成功"]
});
const ruleForm = reactive({
	type: [
		{
			required: true,
			message: "请填写发至岸基/船舶"
		}
	]
});

const formData = inject("formData") as Ref<{ [key: string]: any }>;
const form = ref();
// beforeSubmit 修改提交前conetent数据
const beforeSubmit = (data: any) => {
	const type = data.type || [];
	const selectShips = type.filter((item: any[]) => item[0] == "船舶");
	const selectDeparts = type.filter((item: any[]) => item[0] == "岸基");
	// 选择了船舶 关联船舶id
	formData.value.shipIds = selectShips.map((item: any) => item[1].id);
	// 选择了岸基 关联岸基type
	if (selectDeparts.length > 0) {
		formData.value.departTypes = [Apply.DepartType.ONSHORE];
	} else {
		formData.value.departTypes = [];
	}
	data.typeNames = type.map((item: any[]) => {
		if (item[1]) {
			return item[1].name;
		} else {
			return item[0];
		}
	});
	return data;
};
defineExpose({
	beforeSubmit,
	form
});
onMounted(() => {
	// 新增or编辑 重新进入该类别页面 加载默认值
	if (!form.value.formData.plan) {
		form.value.formData.plan = [{}, {}, {}];
	}
	if (!form.value.formData.auditList) {
		form.value.formData.auditList = [
			{ no: "审核组长A" },
			{ no: "审核员B" },
			{ no: "审核员C" },
			{ no: "审核员D" },
			{ no: "审核员E" }
		];
	}
});
</script>
<style lang="scss" scoped>
.table-container {
	::v-deep .el-descriptions__label.el-descriptions__cell.is-bordered-label {
		background-color: #f4f4f5;
	}
}
</style>
