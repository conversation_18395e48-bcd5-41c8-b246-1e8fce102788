<!-- 申请详情页 -->
<template>
	<div>
		<component
			v-if="props.formData.type&&oprCom[props.formData.type as ApplyType]"
			:is="oprCom[props.formData.type as ApplyType]"
			ref="infoRef"
			mode="detail"
		/>
	</div>
	<!-- <div v-else>未找到对应的组件</div> -->
</template>
<script setup lang="ts">
import { ref, onMounted, provide, PropType } from "vue";
import { ApplyType, Apply } from "@/api/interface/system.model";
import B03_1 from "../../../approval-detail/type-components/B03_1Detail.vue";
import B06_5 from "../../../approval-detail/type-components/B06_5Detail.vue";
import B07_1 from "../../../approval-detail/type-components/B07_1Detail.vue";
import B10_1 from "../../../approval-detail/type-components/B10_1Detail.vue";
import B10_2 from "../../../approval-detail/type-components/B10_2Detail.vue";
import B10_3 from "../../../approval-detail/type-components/B10_3Detail.vue";
import B10_4 from "../../../approval-detail/type-components/B10_4Detail.vue";
import B10_5 from "../../../approval-detail/type-components/B10_5Detail.vue";
import B10_6 from "../../../approval-detail/type-components/B10_6Detail.vue";
import C07_1 from "../../../approval-detail/type-components/C07_1Detail.vue";
import B12_1 from "../../../approval-detail/type-components/B12_1Detail.vue";
import B11_4 from "../../../approval-detail/type-components/B11_4Detail.vue";
import B11_3 from "../../../approval-detail/type-components/B11_3Detail.vue";
import B11_2 from "../../../approval-detail/type-components/B11_2Detail.vue";

// const route = useRoute();
// const formData = ref<Apply.Detail>();
const props = defineProps({
	formData: {
		type: Object as PropType<Apply.Detail>,
		default: () => ({})
	}
});
provide("formData", props.formData);
provide("showApproval", false);
provide("showFile", false);
const oprCom: { [key: string]: any } = {
	"B10-4": B10_4,
	"B03-1": B03_1,
	"B06-5": B06_5,
	"B07-1": B07_1,
	"B10-1": B10_1,
	"B10-2": B10_2,
	"B10-3": B10_3,
	"B10-5": B10_5,
	"B10-6": B10_6,
	"B11-2": B11_2,
	"B11-3": B11_3,
	"B11-4": B11_4,
	"B12-1": B12_1,
	"C07-1": C07_1
};
const loading = ref(false);
onMounted(() => {
	loading.value = true;
});
</script>
<style scoped lang="scss"></style>
