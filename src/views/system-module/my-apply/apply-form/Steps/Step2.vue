<!-- 发起申请-填写信息 -->
<template>
	<div style="width: 70%">
		<ApplyForm
			v-if="formData.type&&oprCom[formData.type as ApplyType]"
			:title="formData.typeName"
			ref="applyRef"
			:formData="formData"
			@process-inited="handleSavedData"
		>
			<Component :is="oprCom[formData.type as ApplyType]" ref="infoRef" />
		</ApplyForm>
		<div v-else>未找到对应的组件</div>
		<div class="detail-container">
			<Detail :formData="detailData" />
		</div>
	</div>
</template>
<script setup lang="ts">
import { inject, onMounted, ref, Ref, computed } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import ApplyForm from "../components/ApplyLayout.vue";
import B11_4 from "./type-components/B11_4Form.vue";
import B11_3 from "./type-components/B11_3Form.vue";
import B11_2 from "./type-components/B11_2Form.vue";
import B12_1 from "./type-components/B12_1Form.vue";
import C07_1 from "./type-components/C07_1Form.vue";
import B10_4 from "./type-components/B10_4Form.vue";
import B10_6 from "./type-components/B10_6Form.vue";
import B10_5 from "./type-components/B10_5Form.vue";
import B10_3 from "./type-components/B10_3Form.vue";
import B10_2 from "./type-components/B10_2Form.vue";
import B10_1 from "./type-components/B10_1Form.vue";
import B07_1 from "./type-components/B07_1Form.vue";
import B06_5 from "./type-components/B06_5Form.vue";
import B03_1 from "./type-components/B03_1Form.vue";

import Detail from "./Detail.vue";

import { Apply, ApplyType } from "@/api/interface/system.model";
import { ApplyService } from "@/api/modules/system";
import { onBeforeRouteLeave, useRoute, useRouter } from "vue-router";
import { isEqual, cloneDeep } from "lodash";
import { snapdom } from "@zumer/snapdom";
import { UploadService } from "@/api/modules/upload";
import OSS from "ali-oss";

const route = useRoute();
const router = useRouter();
const formData = inject("formData") as Ref<{ [key: string]: any }>;
// 流程组件实例
const applyRef = ref();
// 类型表单组件实例
const infoRef = ref();
const oprCom = ref<{ [key: string]: any }>({
	"B11-2": B11_2,
	"B11-3": B11_3,
	"B11-4": B11_4,
	"B12-1": B12_1,
	"C07-1": C07_1,
	"B10-6": B10_6,
	"B10-5": B10_5,
	"B10-4": B10_4,
	"B10-3": B10_3,
	"B10-2": B10_2,
	"B10-1": B10_1,
	"B07-1": B07_1,
	"B06-5": B06_5,
	"B03-1": B03_1
});
const loading = ref(false);
// formData动态值
const detailData = computed(() => {
	const data = formData.value as Apply.Detail;
	data.content = JSON.stringify(infoRef.value?.form.formData || {});
	return data;
});
// base64 转 blob
function dataURLToBlob(dataURL: string) {
	// 分离 MIME 类型和 base64 数据
	const arr = dataURL.split(",");
	const mime = arr[0].match(/:(.*?);/)[1];
	const bstr = atob(arr[1]); // 解码 base64

	let n = bstr.length;
	const u8arr = new Uint8Array(n);

	// 将字符串转换为 Uint8Array
	while (n--) {
		u8arr[n] = bstr.charCodeAt(n);
	}

	return new Blob([u8arr], { type: mime });
}

const save = async (saveType: Apply.SaveType) => {
	if (loading.value) return;
	loading.value = true;
	let valid = true;

	const formRef = infoRef.value?.form;
	// 只对提交校验，保存草稿跳过
	if (saveType === Apply.SaveType.SUBMIT) {
		const formValid = await formRef.doValidate();
		let applyValid = true;
		applyRef.value.validateAndSave((valid: boolean) => {
			applyValid = valid;
		});
		valid = formValid && applyValid;
	}
	if (valid) {
		//保存流程数据
		applyRef.value.save();

		//保存表单数据 拷贝一份数据用于beforeSubmit 不影响infoRef下原来的formData
		const contentData =
			infoRef.value.beforeSubmit && saveType === Apply.SaveType.SUBMIT
				? infoRef.value.beforeSubmit(cloneDeep(formRef.formData))
				: formRef.formData;
		formData.value.content = JSON.stringify(contentData);

		//保存详情图片url
		const el = document.querySelector(".detail-container");
		if (!el) {
			throw new Error("Detail container element not found");
		}
		const snapRes = await snapdom(el as HTMLElement, { scale: 1 });
		// await snapRes.download({ format: "jpg", filename: "my-capture" });
		const img = await snapRes.toPng();
		// 将Blob对象转换为File对象
		const file = new File([dataURLToBlob(img.src)], `${formData.value.type}.png`, {
			type: "image/png"
		});
		const { data } = await UploadService.ossApi({ fileName: file.name });
		if (!data) return;
		const client = new OSS({
			accessKeyId: data!.accessKeyId,
			accessKeySecret: data!.accessKeySecret,
			stsToken: data!.securityToken,
			bucket: data!.bucketName,
			region: data!.region
		});
		let url = import.meta.env.VITE_OSS_PATH + `/${Date.now() + "_" + file.name}`;
		let result = (await client.put(url, file)) as any;
		formData.value.detailImg = result.url;

		let res;
		try {
			if (route.query.mode === "edit") {
				res = await ApplyService.update({ ...formData.value, saveType } as Apply.ApplyUpdateInfo);
			} else {
				res = await ApplyService.create({ ...formData.value, saveType } as Apply.ApplyInfo);
			}
			if (res.code === 0) {
				savedFormData = cloneDeep(formData.value);
				if (saveType === Apply.SaveType.DRAFT) {
					return ElMessage.success({
						message: "保存草稿成功",
						onClose: () => {
							if (route.query.mode === "add") {
								router.back();
							}
							loading.value = false;
						}
					});
				}
				return true;
			}
		} catch (err) {
			console.error(err, "err");
			loading.value = false;
		}
	}
	loading.value = false;
	return false;
};
// 离开页面时，判断表单是否变化，有则提示保存
// 存储被保存过的表单内容
let savedFormData: any = {};
onBeforeRouteLeave((to, from, next) => {
	if (applyRef.value && infoRef.value) {
		// 离开页面时获取审批、表单最新值
		applyRef.value.save();
		const content = JSON.stringify(infoRef.value.form.formData);
		const currentFormData = cloneDeep({ ...formData.value, content });
		const isFormChange = !isEqual(savedFormData, currentFormData);
		if (isFormChange) {
			setTimeout(() => {
				ElMessageBox.confirm("内容已修改，是否保存草稿？", "提示", {
					distinguishCancelAndClose: true,
					confirmButtonText: "是",
					cancelButtonText: "否",
					type: "warning"
				})
					.then(async () => {
						await save(Apply.SaveType.DRAFT);
						next();
					})
					.catch(action => {
						// 取消时，不保存草稿，直接离开页面
						next(action === "cancel");
					});
			}, 16);
		} else {
			next();
		}
	} else {
		next();
	}
});
// 获取表单、审批数据，存储已保存状态数据
const handleSavedData = () => {
	applyRef.value.save();
	savedFormData.approvals = cloneDeep(formData.value.approvals);
	console.log("存储初始值");
};

const firstLoaded = inject("firstLoaded") as Ref<Boolean>;
onMounted(() => {
	if (infoRef.value) {
		if (["edit", "resubmit"].includes(route.query.mode as string) && !firstLoaded.value) {
			//首次进入时 回显内部表单数据
			const formRef = infoRef.value.form;
			formRef.formData = JSON.parse(formData.value.content);
			firstLoaded.value = true;
		}
		const content = JSON.stringify(infoRef.value.form.formData);
		savedFormData = cloneDeep({ ...formData.value, content });
	}
});

const saveDisabled = computed(() => !infoRef.value || infoRef.value.form.submitDisabled);

defineExpose({
	saveDraft: () => save(Apply.SaveType.DRAFT),
	tryToNext: () => save(Apply.SaveType.SUBMIT),
	saveDisabled
});
</script>
<style scoped lang="scss">
.detail-container {
	/* 不占用页面布局空间 */
	position: absolute;
	width: 70%;

	// top: -9999px;
	// left: -9999px;
}
</style>
