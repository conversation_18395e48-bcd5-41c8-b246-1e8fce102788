<!-- 申请表单 -->
<template>
	<Layout title="发起申请" :can-back="true" page-size="100%">
		<template #tabs-area>
			<el-steps style="padding: 24px 24px 12px" :active="nowStep" finish-status="success" space="30%" process-status="process">
				<el-step title="选择申请" />
				<el-step title="填写信息" />
				<el-step title="完成创建" />
			</el-steps>
		</template>
		<template #body>
			<transition appear name="fade-transform" mode="out-in">
				<Step1 ref="step1Ref" v-if="nowStep === StepStatus.setType"></Step1>
				<Step2 ref="step2Ref" v-else-if="nowStep === StepStatus.setInfo"></Step2>
				<Step3 ref="step3Ref" v-else-if="nowStep === StepStatus.finished"></Step3>
			</transition>
		</template>

		<template #bottom-button>
			<div style="display: flex">
				<!-- 第一步 -->
				<el-button v-if="StepStatus.setType === nowStep" @click="stepToNext()" type="primary">下一步</el-button>
				<!-- 第二步 -->
				<el-button v-if="StepStatus.setInfo === nowStep" @click="stepToNext(-1)">上一步</el-button>
				<el-button :disabled="step2Ref?.saveDisabled" v-if="StepStatus.setInfo === nowStep" @click="stepToNext(1, 'draft')"
					>存草稿</el-button
				>
				<el-button :disabled="step2Ref?.saveDisabled" v-if="StepStatus.setInfo === nowStep" @click="stepToNext()" type="primary"
					>提交</el-button
				>
				<!-- 第三步 -->
				<el-button v-if="StepStatus.finished === nowStep" @click="finish">完成</el-button>
			</div>
		</template>
	</Layout>
</template>

<script setup lang="ts" name="EditShip">
import { ref, provide, onMounted, watch } from "vue";
import Layout from "@/meta-components/MetaLayout/SinglePage.vue";
import Step1 from "./steps/Step1.vue";
import Step2 from "./steps/Step2.vue";
import Step3 from "./steps/Step3.vue";
import { Apply, ApprovalType, TimeLabelType } from "@/api/interface/system.model";
import { ElMessageBox } from "element-plus";
import { useRouter, useRoute } from "vue-router";
import { ApplyService, ApprovalService } from "@/api/modules/system";
import { MemberService } from "@/api/modules/member";
import { PositionService } from "@/api/modules/depart";
import { YN } from "@/enums/global-enums";

const router = useRouter();
const route = useRoute();
const step1Ref = ref();
const step2Ref = ref();
const step3Ref = ref();
const emit = defineEmits(["success"]);

const mode = ref(route.query.mode);
/**表格信息 */
const formData = ref({} as Apply.ApplyInfo);
provide("formData", formData);

/**步骤枚举 */
enum StepStatus {
	/**选择申请 */
	setType = 0,
	/**填写信息 */
	setInfo = 1,
	/**完成创建 */
	finished = 2
}
const nowStep = ref(StepStatus.setType);

/**尝试下一步 */
async function stepToNext(step: number = 1, type?: string) {
	/**每次下一步的开始都要重置表单高度 */
	if (step === -1) {
		ElMessageBox.confirm("返回并修改类别将导致已填写内容丢失，是否继续？", "提示", {
			confirmButtonText: "确认",
			cancelButtonText: "取消",
			type: "warning"
		}).then(() => {
			nowStep.value += step;
		});
		return;
	}
	let canGoNext = false;
	switch (nowStep.value) {
		case StepStatus.setType:
			canGoNext = await step1Ref.value.tryToNext();
			if (canGoNext) nowStep.value += 1;
			break;
		case StepStatus.setInfo:
			if (type === "draft") {
				step2Ref.value.saveDraft();
				break;
			}
			canGoNext = await step2Ref.value.tryToNext();
			if (canGoNext) nowStep.value += 1;
			break;
	}
	if (canGoNext) {
		backToTop();
	}
}

/**回到父组件的顶部 每次切换步骤都得重置高度 */
function backToTop() {
	const box = document.getElementsByClassName("page-container")[0];
	box.scrollTo({ top: 0 });
}
function finish() {
	emit("success");
	router.back();
}
const initApprovals = ref();
provide("initApprovals", initApprovals);

// 是否在step2首次加载了content数据
const loadStep2 = ref<Boolean>();
provide("firstLoaded", loadStep2);

/** 获取默认抄送人 */
const defaultCCApprovals = ref();
const getDefaultCC = () => {
	const CC_POSITION_NANE = ["海务经理", "机务经理", "指定人员", "SMS办公主管"];
	PositionService.getList({}).then(res => {
		let positionNameIdMap: Map<string, number>;
		if (res.data && res.data.positions) {
			positionNameIdMap = new Map(res.data.positions.map((item: any) => [item.name, item.id]));
			MemberService.getListByPosition({
				positionIds: CC_POSITION_NANE.map(name => positionNameIdMap.get(name)).filter(id => id) as number[]
			}).then(res => {
				if (res.data && res.data.list) {
					defaultCCApprovals.value = res.data.list.map(item => ({
						type: ApprovalType.COPY,
						corpUserId: item.id,
						corpUserName: item.name
					}));
				}
			});
		}
	});
};
onMounted(() => {
	if (["edit", "resubmit"].includes(mode.value as string)) {
		const query: Apply.DetailReq = {
			uuid: route.query.uuid as string
		};
		if (mode.value === "resubmit") {
			query.isApplyAgain = YN.Yes;
		}
		ApplyService.detail(query).then(res => {
			if (res.data) {
				//流程数据 去除后端返回的不必要字段
				const approvals =
					res.data.approvals?.map(({ type, corpUserId, corpUserName }: Apply.ApplyApprovalInfo) => ({
						type,
						corpUserId,
						corpUserName
					})) || [];
				formData.value = { ...res.data, approvals } as Apply.ApplyInfo;
				initApprovals.value = approvals;
				nowStep.value = StepStatus.setInfo;
			}
		});
	}
	getDefaultCC();
});

watch(
	() => formData.value?.type,
	(curType, oldType) => {
		// 新增或类别改变时
		// 清空数据 回填流程、表单默认值
		if (curType && ((oldType && curType !== oldType) || mode.value === "add")) {
			//	默认审批流程
			ApprovalService.getDefaultApproval({ applyType: curType }).then(res => {
				if (res.data) {
					// 若不存在默认审批流程 则填充默认抄送人
					if (!res.data.approvals?.length) {
						formData.value.approvals = defaultCCApprovals.value || [];
					} else {
						formData.value.approvals = res.data.approvals;
					}
					initApprovals.value = formData.value.approvals;
				}
				// 恢复到默认值
				formData.value.timeLabelType = TimeLabelType.MONTH;
				formData.value.isSaveAsDef = YN.No;
				formData.value.content = "{}";
				delete formData.value.timeLabel;
				delete formData.value.keyword;
			});
		}
	}
);
</script>

<style lang="scss" scoped></style>
