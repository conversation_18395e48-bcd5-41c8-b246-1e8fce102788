/** 将树级结构转为级联所需的数据格式 */
export const reformDataToCas = (
	childrens: any[],
	labelKey: string,
	ValueKey: string[] | string
): Array<{ label: string; value: any; children: any[] }> => {
	return childrens.map((item: any) => {
		const { children } = item;
		return {
			label: item[labelKey],
			value:
				typeof ValueKey === "string"
					? item[ValueKey]
					: ValueKey.reduce((pre, cur) => {
							pre[cur] = item[cur];
							return pre;
					  }, {} as any),
			children: children && children.length > 0 ? reformDataToCas(children, labelKey, ValueKey) : []
		};
	});
};
