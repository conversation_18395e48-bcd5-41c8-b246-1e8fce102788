<!-- 体系台账列表 -->
<template>
	<div style="height: 100%">
		<TwoPartsPage title="体系台账" expand-hint="展开体系台账目录" :left-width="480">
			<template #left>
				<LeftPart ref="shipListRef" />
			</template>
			<template #right>
				<RightPart v-if="type"></RightPart>
			</template>
		</TwoPartsPage>
	</div>
</template>

<script setup lang="ts" name="ApprovalRecordList">
import LeftPart from "./components/Menu.vue";
import RightPart from "./components/List.vue";
import TwoPartsPage from "@/meta-components/MetaLayout/TwoPartsPage.vue";
import { provide, ref } from "vue";

/**选中菜单type */
const type = ref(undefined as string | undefined);

provide("type", type);
</script>

<style scoped></style>
