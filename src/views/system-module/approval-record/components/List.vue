<!-- 台账列表 -->
<template>
	<div class="page-container">
		<div style="height: 100%" v-if="tabsConfig.tabList.length">
			<TableLayout
				ref="LedgerTable"
				title="体系台账"
				:table-config="tableConfig"
				:filter-config="filterConfig"
				:tabs-config="tabsConfig"
				@on-tab-change="handleTabChange"
			>
				<template #depart="{ row }">
					{{row.depart && row.depart.length > 0 ? row.depart.map((i: any) => i.name).join("、") : "-"}}
				</template>
				<template #corpUserName="{ row }">
					{{(row.position?.length ? `${row.position.map((i: any) => i.name).join("、")}：` : "") + (row.corpUserName || "-")}}
				</template>
				<template #opr="{ row }">
					<el-link
						v-if="hasAuth('台账详情')"
						style="margin-right: 12px"
						:underline="false"
						type="primary"
						@click="jumpTo('detail', row)"
						>查看详情</el-link
					>
					<div v-if="hasAuth('台账删除')">
						<OprDelete @on-delete="handleBtnDelete(row)"></OprDelete>
					</div>
				</template>
			</TableLayout>
		</div>
	</div>
</template>

<script setup lang="ts" name="systemApprovalTable">
import { ref, reactive, onMounted, inject, Ref, watch } from "vue";
import TableLayout from "@/meta-components/MetaTable/index.vue";
import { ElMessage } from "element-plus";
import { ColumnProps, FilterConfigOptions, TableConfig, TabsConfig } from "@/meta-components/MetaTable/interface";
import OprDelete from "@/meta-components/MetaTableComponents/OprDelete.vue";

import { LedgerService } from "@/api/modules/system";
import { hasAuth } from "@/utils/util";
import { useTableMemory } from "@/meta-components/MetaHooks/usePageMemory";

import { useTableScrollMemory } from "@/hooks/useTableScrollMemory";
import router from "@/routers";

import { ShipService } from "@/api/modules/ship";
import { TAB_SHIP_NAMES } from "../constants";
import { YN } from "@/enums/global-enums";

let LedgerTable = ref();
// const globalStore = GlobalStore();
const type = inject("type") as Ref<string | undefined>;

let tabsConfig: TabsConfig = reactive({
	tabList: [],
	showCount: 7,
	filterAttr: "shipId"
});

/**筛选框配置数量 */
let filterConfig: Array<FilterConfigOptions> = reactive([
	// { name: "typeName", type: "input", span: 4, placeholder: "搜索流程名称" },
	{ name: "departName", type: "input", span: 4, placeholder: "搜索所属部门" },
	{ name: "corpUserName", type: "input", span: 4, placeholder: "搜索创建人" },
	{
		name: "completedTime",
		type: "dateRange",
		span: 6,
		placeholder: "创建时间"
	}
]);

let columnsConfig: Array<ColumnProps> = [
	{
		label: "序号",
		type: "index"
	},
	{
		label: "流程名称",
		prop: "typeName"
	},
	{
		label: "关键词",
		prop: "keyword"
	},
	{
		label: "时间标签",
		prop: "timeLabel"
	},
	{
		label: "流程所属部门",
		slotName: "depart"
	},
	{
		label: "创建人",
		slotName: "corpUserName"
	},
	{
		label: "创建时间",
		prop: "completedTime",
		width: 160,
		sortable: true,
		sortAttr: "completedTime"
	},
	{
		label: "操作",
		slotName: "opr",
		fixed: "right",
		width: "180px"
	}
];

let tableConfig: TableConfig = reactive({
	key: "system-ledger-mgr",
	columns: columnsConfig,
	requestApi: LedgerService.getList,
	// dataCallback: data => {
	// 	injectSailorCertStatus(data);
	// 	return reformTrainContents(data);
	// },
	selectType: "none",
	pagination: true,
	staticParam: { type: type.value },
	defaultExpandAll: false,
	needRefreshOnActivated: true
});

// 切换标签时判断属于船舶/其他tab
function handleTabChange({ name }: { index: number; value: any; name: string }) {
	if (name === "其他") {
		tableConfig.staticParam!.ledgerType = YN.Yes;
		delete tableConfig.staticParam!.shipId;
	} else {
		tableConfig.staticParam!.ledgerType = YN.No;
	}
}
useTableMemory(LedgerTable, tableConfig);

//页面跳转
function jumpTo(mode: "detail", rowData?: any) {
	router.push({ name: "ApplyDetail", params: { mode }, query: { uuid: rowData?.uuid } });
}

//删除
async function handleBtnDelete(rowData: { [key: string]: any }) {
	let { code } = await LedgerService.delete({ uuid: rowData.uuid });
	if (code !== 0) return;
	ElMessage.success("删除成功");
	refresh();
}
/**刷新 */
function refresh() {
	// emits("onRefresh");
	LedgerTable.value.search();
}
watch(
	() => type.value,
	() => {
		if (tableConfig.staticParam) {
			tableConfig.staticParam.type = type.value;
		} else {
			tableConfig.staticParam = {
				type: type.value
			};
		}
		LedgerTable.value.search();
	}
);
/**获取标签列表 */
async function fetchTabs() {
	let { code, data } = await ShipService.getAllSearchList({});
	if (data && code == 0) {
		const shipNameIdMap = new Map(
			data.list.map((item: any) => {
				return [item.name, item.id];
			})
		);
		tabsConfig.tabList.splice(
			0,
			tabsConfig.tabList.length,
			...TAB_SHIP_NAMES.map((shipName: string) => ({ label: shipName, value: shipNameIdMap.get(shipName) || 0 })).filter(
				(item: any) => item.value !== 0
			),
			{ label: "其他", value: null }
		);
	}
}
onMounted(() => {
	fetchTabs();
	useTableScrollMemory(LedgerTable, "SystemLedgerMgr");
});
</script>

<style scoped></style>
