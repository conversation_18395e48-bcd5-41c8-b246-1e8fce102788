<!-- 台账菜单 -->
<template>
	<div class="type-menu">
		<el-menu text-color="#333333" @select="handleSelect" default-active="B03-1">
			<!-- 一级菜单 -->
			<template v-for="item in menuList">
				<el-sub-menu
					v-if="item.children && item.children.length"
					:key="item.value + '-sub'"
					:index="item.value"
					:expand-close-icon="Folder"
					:expand-open-icon="FolderOpened"
				>
					<template #title>
						<span>{{ item.title }}</span>
					</template>
					<!-- 二级菜单 -->
					<template v-for="child in item.children">
						<el-sub-menu
							v-if="child.children && child.children.length"
							:key="child.value + '-sub'"
							:index="child.value"
							:expand-close-icon="Folder"
							:expand-open-icon="FolderOpened"
						>
							<template #title>
								<span>{{ child.title }}</span>
							</template>
							<!-- 三级菜单 -->
							<el-menu-item v-for="grandChild in child.children" :key="grandChild.value" :index="grandChild.value">
								<el-icon><Document /></el-icon>{{ grandChild.title }}
							</el-menu-item>
						</el-sub-menu>
						<el-menu-item v-else :key="child.value" :index="child.value">
							<el-icon><Document /></el-icon>{{ child.title }}
						</el-menu-item>
					</template>
				</el-sub-menu>
				<el-menu-item v-else :key="item.value" :index="item.value">
					<el-icon><Document /></el-icon>{{ item.title }}
				</el-menu-item>
			</template>
		</el-menu>
	</div>
</template>
<script setup lang="ts" name="systemRecordMenu">
import { reactive, inject, Ref, onMounted } from "vue";
import { Folder, FolderOpened } from "@element-plus/icons-vue";

const selectType = inject("type") as Ref<string | undefined>;
// 菜单数据
const menuList = reactive([
	{
		title: "MY-B公司程序文件",
		value: "B",
		children: [
			{
				title: "B03-SMS培训程序",
				value: "B03",
				children: [
					{
						title: "B03-1培训需求表",
						value: "B03-1"
					}
				]
			},
			{
				title: "B06-船上紧急情况的标明、阐述和反应程序",
				value: "B06",
				children: [
					{
						title: "B06-5船岸联合应急演习记录表",
						value: "B06-5"
					}
				]
			},
			{
				title: "B07-不符合规定情况、事故和险情的报告、分析和纠正程序",
				value: "B07",
				children: [
					{
						title: "B07-1不符合规定情况、事故和险情的报告、分析和纠正表",
						value: "B07-1"
					}
				]
			},
			{
				title: "B10-SMS文件控制程序",
				value: "B10",
				children: [
					{
						title: "B10-1文件修改申请审核表",
						value: "B10-1"
					},
					{
						title: "B10-2文件修改通知单",
						value: "B10-2"
					},
					{
						title: "B10-3文件收发登记表",
						value: "B10-3"
					},
					{
						title: "B10-4外来文件评审表",
						value: "B10-4"
					},
					{
						title: "B10-5文件借阅登记表",
						value: "B10-5"
					},
					{
						title: "B10-6文件回收、作废处理单",
						value: "B10-6"
					}
				]
			},
			{
				title: "B11-内审程序",
				value: "B11",
				children: [
					{
						title: "B11-2审核通知书",
						value: "B11-2"
					},
					{
						title: "B11-3审核会议记录",
						value: "B11-3"
					},
					{
						title: "B11-4审核实施记录",
						value: "B11-4"
					}
				]
			},
			{
				title: "B12-SMS有效性评价和复查程序",
				value: "B12",
				children: [
					{
						title: "B12-1SMS有效性评价/管理复查通知单",
						value: "B12-1"
					}
				]
			}
		]
	},
	{
		title: "MY-C公司操作手册",
		value: "C",
		children: [
			{
				title: "C07-SMS文件编写须知",
				value: "C07",
				children: [
					{
						title: "C07-1SMS文件审查会签表",
						value: "C07-1"
					}
				]
			}
		]
	}
]);
// 菜单选中事件
const handleSelect = (value: string) => {
	selectType.value = value;
};
onMounted(() => {
	selectType.value = "B03-1";
});
</script>
<style scoped lang="scss">
.type-menu {
	z-index: 100;
	margin: 0 -12px;
	.el-menu {
		border-right: none;
		::v-deep .el-sub-menu__icon-arrow {
			// display: none !important;
			font-size: 18px;
		}
	}
	.is-active {
		color: #40a9ff !important;
		background-color: #ffffff !important;
		&::before {
			position: absolute;
			left: 0;
			display: none !important;
			content: "";
		}
	}
}
</style>
