<!--我的审批/抄送给我列表-->
<template>
	<div class="page-container">
		<div style="height: 100%">
			<TableLayout
				ref="myApprovalTable"
				title="审批抄送"
				:table-config="tableConfig"
				:filter-config="filterConfig"
				:tabs-config="tabsConfig"
				@on-tab-change="handleTabChange"
			>
				<template #status="{ row }">
					<div v-if="row.status === Approval.ApprovalStatus.WAITING">
						<el-tag type="primary">待审批</el-tag>
					</div>
					<div v-else-if="row.status === Approval.ApprovalStatus.AGREE">
						<el-tag type="success">同意</el-tag>
					</div>
					<div v-else-if="row.status === Approval.ApprovalStatus.DISAGREE">
						<el-tag type="danger">不同意</el-tag>
					</div>
				</template>
				<template #position="{ row }">
					{{ row.position && row.position.length > 0 ? row.position.map((i:any) => i.name).join("、") : "-" }}
				</template>
				<template #opr="{ row }">
					<el-link
						v-if="hasAuth('审批详情') && row.status !== Approval.ApprovalStatus.WAITING"
						style="margin-right: 12px"
						:underline="false"
						type="primary"
						@click="jumpTo('detail', row)"
						>查看详情</el-link
					>
					<el-link
						v-if="hasAuth('审批') && row.status === Approval.ApprovalStatus.WAITING"
						style="margin-right: 12px"
						:underline="false"
						type="primary"
						@click="jumpTo('approval', row)"
						>审批</el-link
					>
				</template>
			</TableLayout>
		</div>
	</div>
</template>

<script setup lang="ts" name="ApprovalMgr">
import { ref, reactive, onMounted } from "vue";
import TableLayout from "@/meta-components/MetaTable/index.vue";
import { ColumnProps, FilterConfigOptions, TableConfig, TabsConfig } from "@/meta-components/MetaTable/interface";

import { ApprovalService } from "@/api/modules/system";
import { Approval } from "@/api/interface/system.model";
import { hasAuth } from "@/utils/util";
import { useTableMemory } from "@/meta-components/MetaHooks/usePageMemory";

import { useTableScrollMemory } from "@/hooks/useTableScrollMemory";
import router from "@/routers";
import { MenuStore } from "@/store/modules/menu";
import { useMenuRedCount } from "@/hooks/useMenuRedCount";

const { freshSystemRedCount } = useMenuRedCount();

let myApprovalTable = ref();
const menuStore = MenuStore();

const redCount = menuStore.redCount;

const tabValue = ref(Approval.ListType.APPROVAL);
let tabsConfig: TabsConfig = reactive({
	tabList: [
		{
			label: "我的审批",
			value: Approval.ListType.APPROVAL,
			// 红点
			badge: {
				// isDot: true,
				count: () => redCount["system-mgr-approval"] || 0
			}
		},
		{
			label: "抄送给我",
			value: Approval.ListType.COPY,
			badge: {
				// isDot: true,
				count: () => redCount["system-mgr-copy"] || 0
			}
		}
	],
	showCount: 2,
	filterAttr: "listType"
});
/**切换tab时修改结束时间的字段名 */
const handleTabChange = (e: any) => {
	tabValue.value = e.value;
};
/**筛选框配置数量 */
let filterConfig: Array<FilterConfigOptions> = reactive([
	{ name: "typeName", type: "input", span: 3, placeholder: "筛选申请名称" },
	{
		name: "status",
		type: "singleSelect",
		span: 3,
		placeholder: "筛选审批状态",
		staticOptions: [
			{ label: "待审批", value: Approval.ApprovalStatus.WAITING },
			{ label: "同意", value: Approval.ApprovalStatus.AGREE },
			{ label: "不同意", value: Approval.ApprovalStatus.DISAGREE }
		],
		visible: () => tabValue.value === Approval.ListType.APPROVAL
	},
	{
		name: "positionName",
		type: "input",
		span: 3,
		placeholder: "筛选流程所属"
	},
	{
		name: "applyTime",
		type: "dateRange",
		span: 4,
		placeholder: "申请时间"
	},
	{
		name: "approvalTime",
		type: "dateRange",
		span: 4,
		placeholder: "审批时间",
		visible: () => tabValue.value === Approval.ListType.APPROVAL
	},
	{
		name: "completedTime",
		type: "dateRange",
		span: 4,
		placeholder: "抄送时间",
		visible: () => tabValue.value === Approval.ListType.COPY
	}
]);

let columnsConfig: Array<ColumnProps> = [
	{
		label: "审批状态",
		slotName: "status",
		visible: () => tabValue.value === Approval.ListType.APPROVAL
	},
	{
		label: "申请名称",
		prop: "typeName"
	},
	{
		label: "关键词",
		prop: "keyword"
	},
	{
		label: "时间标签",
		prop: "timeLabel"
	},
	{
		label: "申请人职务",
		slotName: "position"
	},
	{
		label: "申请时间",
		prop: "applyTime",
		width: 160,
		sortable: true,
		key: "applyTime",
		sortAttr: "applyTime"
	},
	{
		label: "审批时间",
		prop: "approvalTime",
		width: 160,
		sortable: true,
		key: "approvalTime",
		sortAttr: "approvalTime",
		visible: () => tabValue.value === Approval.ListType.APPROVAL
	},
	{
		label: "抄送时间",
		prop: "completedTime",
		width: 160,
		sortable: true,
		key: "completedTime",
		sortAttr: "completedTime",
		visible: () => tabValue.value === Approval.ListType.COPY
	},
	{
		label: "操作",
		slotName: "opr",
		fixed: "right",
		width: "150px"
	}
];

let tableConfig: TableConfig = reactive({
	key: "system-approval-mgr",
	columns: columnsConfig,
	requestApi: ApprovalService.getList,
	// 获取列表后 刷新红点数
	callBack: handleFreshCount,
	// 设置唯一键
	selectId: "approvalId",
	selectType: "none",
	pagination: true,
	staticParam: {},
	defaultExpandAll: false,
	needRefreshOnActivated: true
});
useTableMemory(myApprovalTable, tableConfig);

// 审批/详情
function jumpTo(mode: "approval" | "detail", rowData?: any) {
	router.push({ name: "ApplyDetail", query: { uuid: rowData?.uuid }, params: { mode, fromApproval: "true" } });
}
// 刷新红点数
function handleFreshCount() {
	freshSystemRedCount();
}
onMounted(() => {
	useTableScrollMemory(myApprovalTable, "system-approval-mgr");
});
</script>

<style scoped></style>
