// 获取固定六条船舶数据
import { ref } from "vue";
import { TAB_SHIP_NAMES } from "../approval-record/constants";
import { ShipService } from "@/api/modules/ship";
import { SelectOption } from "@/meta-components/MetaForm/interface";

import { DepartService } from "@/api/modules/depart";
import { GlobalStore } from "@/store";
import { reformDataToCas } from "../my-apply/utils";
import { Apply } from "@/api/interface/system.model";

const options = ref<SelectOption[]>([]);
let loaded = false;

export function useShipOptions() {
	const getOption = async () => {
		if (!loaded) {
			const res = await ShipService.getAllSearchList({ name: "" });
			if (res.data) {
				const shipNameIdMap = new Map(res.data.list.map((item: any) => [item.name, item]));
				options.value = TAB_SHIP_NAMES.map(name => {
					const item = shipNameIdMap.get(name);
					return { label: name, value: item?.id, data: item } as SelectOption;
				}).filter(i => i.value);
				loaded = true;
			}
		}
		return { code: 0, data: { list: options.value } };
	};
	// 只请求一次
	getOption();

	return { getOption, options };
}

const globalStore = GlobalStore();
const departOptions = ref();
let departLoaded = false;
export function useDepartOptions() {
	const getOption = async () => {
		if (!departLoaded) {
			// 获取部门数据
			const options: any[] = [];
			const departRes = await DepartService.getAll({ corpId: globalStore.userInfo.corpId });
			if (departRes.data && departRes.data.departs) {
				const { departs } = departRes.data;
				const departChildren = [[] as any[], [] as any[]];
				departs.forEach(item => {
					const casItem: any = {
						value: { id: item.id, name: item.name },
						label: item.name!,
						children: item.children && item.children.length > 0 ? reformDataToCas(item.children, "name", ["id", "name"]) : []
					};
					if (item.type === 1) {
						departChildren[0].push(casItem);
					} else if (item.type === 2) {
						departChildren[1].push(casItem);
					}
				});
				if (departChildren[0].length > 0) {
					options.push({
						value: { name: "离岸部门", id: Apply.DepartType.OFFSHORE },
						label: "离岸部门",
						children: departChildren[0]
					});
				}
				if (departChildren[1].length > 0) {
					options.push({
						value: { name: "岸基部门", id: Apply.DepartType.ONSHORE },
						label: "岸基部门",
						children: departChildren[1]
					});
				}
				departOptions.value = options;
			}
		}
		return { code: 0, data: { list: options.value } };
	};

	if (!departLoaded) {
		getOption();
	}

	return { getOption, options: departOptions };
}
