<!-- 体系文件 -->
<template>
	<div style="height: 100%">
		<TwoPartsPage title="体系文件" expand-hint="展开体系文件目录" :left-width="480">
			<template #left>
				<LeftPart />
			</template>
			<template #right>
				<RightPart v-if="currentMenu"></RightPart>
			</template>
		</TwoPartsPage>
	</div>
</template>

<script setup lang="ts" name="SystemFileList">
import LeftPart from "./components/Menu.vue";
import RightPart from "./components/List.vue";
import TwoPartsPage from "@/meta-components/MetaLayout/TwoPartsPage.vue";
import { provide, ref } from "vue";

/**选中菜单Item */
const currentMenu = ref();

provide("currentMenu", currentMenu);
</script>

<style scoped></style>
