<!-- 体系文件列表 -->
<template>
	<div class="page-container">
		<div style="height: 100%">
			<TableLayout ref="SystemFileTable" title="体系文件" :table-config="tableConfig">
				<template #opr="{ row }">
					<el-link style="margin-right: 12px" :underline="false" type="primary" @click="hamdlePreview(row)">预览</el-link>
					<el-link :underline="false" type="primary" @click="download(row)">下载</el-link>
				</template>
			</TableLayout>
		</div>
		<Preview ref="previewRef" />
	</div>
</template>

<script setup lang="ts" name="systemApprovalTable">
import { ref, reactive, onMounted, inject, Ref, watch } from "vue";
import TableLayout from "@/meta-components/MetaTable/index.vue";
import Preview from "@/components/Preview/Preview.vue";
import { ColumnProps, TableConfig } from "@/meta-components/MetaTable/interface";
import { SourceItem, ossFolderPath } from "../constants";

import { useTableMemory } from "@/meta-components/MetaHooks/usePageMemory";

import { useTableScrollMemory } from "@/hooks/useTableScrollMemory";

let SystemFileTable = ref();
// const globalStore = GlobalStore();
const currentMenu = inject("currentMenu") as Ref<SourceItem>;

let columnsConfig: Array<ColumnProps> = [
	{
		label: "序号",
		type: "index"
	},
	{
		label: "文件名称",
		prop: "name"
	},
	{
		label: "操作",
		slotName: "opr",
		fixed: "right",
		width: "180px"
	}
];

let tableConfig: TableConfig = reactive({
	key: "system-file-mgr",
	columns: columnsConfig,
	requestApi: () => {
		return Promise.resolve({
			code: 0,
			data: {
				list: currentMenu.value.list,
				total: currentMenu.value.list.length
			}
		});
	},
	selectType: "none",
	pagination: false,
	defaultExpandAll: false,
	needRefreshOnActivated: true
});

useTableMemory(SystemFileTable, tableConfig);

// 文件预览
let previewRef = ref();
/** 预览 */
function hamdlePreview(row: FileUrl) {
	previewRef.value.open(ossFolderPath + row.url);
}

// 文件下载
function download(rowData: any) {
	rowData.url && window.open(`${ossFolderPath}${rowData.url}`, "_blank");
}

watch(
	() => currentMenu.value.key,
	() => {
		SystemFileTable.value.search();
	}
);

onMounted(() => {
	useTableScrollMemory(SystemFileTable, "SystemFileMgr");
});
</script>

<style scoped></style>
