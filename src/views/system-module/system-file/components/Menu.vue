<!-- 体系文件目录 -->
<template>
	<div class="type-menu">
		<el-menu text-color="#333333" @select="handleSelect" default-active="MY-A">
			<!-- 一级菜单 -->
			<template v-for="item in sourceList">
				<el-sub-menu
					v-if="item.children && item.children.length"
					:key="item.key + '-sub'"
					:index="item.key"
					:expand-close-icon="Folder"
					:expand-open-icon="FolderOpened"
				>
					<template #title>
						<span>{{ item.name }}</span>
					</template>
					<!-- 二级菜单 -->
					<template v-for="child in item.children">
						<el-sub-menu
							v-if="child.children && child.children.length"
							:key="child.key + '-sub'"
							:index="child.key"
							:expand-close-icon="Folder"
							:expand-open-icon="FolderOpened"
						>
							<template #title>
								<span>{{ child.name }}</span>
							</template>
							<!-- 三级菜单 -->
							<el-menu-item v-for="grandChild in child.children" :key="grandChild.key" :index="grandChild.key">
								<el-icon><Document /></el-icon>{{ grandChild.name }}
							</el-menu-item>
						</el-sub-menu>
						<el-menu-item v-else :key="child.key" :index="child.key">
							<el-icon><Document /></el-icon>{{ child.name }}
						</el-menu-item>
					</template>
				</el-sub-menu>
				<el-menu-item v-else :key="item.key" :index="item.key">
					<el-icon><Folder /></el-icon>{{ item.name }}
				</el-menu-item>
			</template>
		</el-menu>
	</div>
</template>
<script setup lang="ts" name="systemRecordMenu">
import { inject, Ref, onMounted } from "vue";
import { Folder, FolderOpened } from "@element-plus/icons-vue";
import { sourceList, type SourceItem } from "../constants";

const currentMenu = inject("currentMenu") as Ref<SourceItem>;

// 菜单选中事件
const handleSelect = (value: string) => {
	currentMenu.value = sourceList.find(item => item.key === value) as SourceItem;
};

onMounted(() => {
	!currentMenu.value && handleSelect(sourceList[0].key);
});
</script>
<style scoped lang="scss">
.type-menu {
	z-index: 100;
	margin: 0 -12px;
	.el-menu {
		border-right: none;
		::v-deep .el-sub-menu__icon-arrow {
			// display: none !important;
			font-size: 18px;
		}
	}
	.is-active {
		color: #40a9ff !important;
		background-color: #ffffff !important;
		&::before {
			position: absolute;
			left: 0;
			display: none !important;
			content: "";
		}
	}
}
</style>
