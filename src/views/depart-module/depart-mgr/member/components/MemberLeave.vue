<!--操作离职弹窗-->
<template>
	<el-dialog
		ref="dialog"
		title="操作离职"
		v-model="_showMemberLeave"
		width="40%"
		@open="iniData"
		@closed="handleClosed"
		draggable
	>
		<div class="hint-text">离职后，此账号将无法使用大船师访问本企业，请做好交接工作后再离职</div>
		<MetaForm
			mode="add"
			ref="form"
			:formConfig="formConfig"
			:styleConfig="{ mode: 'dialog', labelWidth: '120px', createBtnText: '确定离职' }"
			:rule-form="ruleForm"
			@on-submit="handleSubmit"
			@on-cancel="handleCancel"
		>
			<template #member>
				<div class="flx-align-center">
					<AvatarImgVue :user-info="editData"></AvatarImgVue>
					<div style="font-weight: bold" class="member-name">{{ editData.name }}</div>
					<div class="member-name">{{ editData.contactMobile }}</div>
				</div>
			</template>
		</MetaForm>
	</el-dialog>
</template>

<script setup lang="ts" name="MemberLeave">
import MetaForm from "@/meta-components/MetaForm/index.vue";
import { FormConfigOptions, FormItemOptions } from "@/meta-components/MetaForm/interface";
import { MemberService } from "@/api/modules/member";
import { ShipService } from "@/api/modules/ship";
import { reactive, computed, ref } from "vue";
import AvatarImgVue from "@/layouts/components/Header/components/AvatarImg.vue";
import { YN } from "@/enums/global-enums";
// import { hasAuth } from "@/utils/util";

let { leave } = MemberService;
let isPrincipal = false;
let props = defineProps({
	showMemberLeave: { type: Boolean, required: true },
	editData: { type: Object, required: true }
});

let ruleForm = reactive({
	leaveTime: [{ required: true, message: "请选择离职时间" }],
	exchangePrincipal: [{ required: true, message: "请选择交接船舶负责人" }]
});

const emits = defineEmits(["update:showMemberLeave", "onSuccess"]);

const _showMemberLeave = computed({
	get() {
		return props.showMemberLeave;
	},
	set(value) {
		emits("update:showMemberLeave", value);
	}
});

let formContent: Array<FormItemOptions> = [
	{
		label: "操作对象",
		type: "slot",
		name: "member"
	},
	{
		label: "离职时间",
		type: "date",
		placeholder: "请输入",
		name: "leaveTime",
		span: 24
	},
	{
		label: "交接船舶负责人",
		type: "singleSelect",
		name: "exchangePrincipal",
		span: 24,
		api: MemberService.getSimpleList,
		visible: () => {
			return isPrincipal;
		},
		otherAttr: {
			remote: true,
			remoteLabelKey: "userName",
			subLabelKey: "contactMobile",
			labelKey: "name",
			valueKey: "id",
			disabledOption: item => item.value === props.editData.id
		},
		placeholder: "请输入"
	},
	{
		label: "备注",
		type: "textarea",
		placeholder: "",
		name: "remark",
		span: 24,
		otherAttr: { max: 200 }
	}
];

const formConfig: FormConfigOptions = {
	key: "edit-depart",
	items: formContent,
	addApi: leave,
	// beforeSubmit: beforeSubmit,
	successCallBack: handleSuccess,
	closeFunction: close,
	closeFormAt: "both",
	hintMessage: ["操作离职成功", ""]
};

//手动调用metaForm的初始化
let form = ref();
async function iniData() {
	//判断是否为负责人
	if (props.editData.id) {
		const res = await ShipService.isPrincipal({ corpUserId: props.editData.id });
		if (res.data) {
			isPrincipal = res.data.isPrincipal === YN.Yes;
		}
	}
	form.value.iniForm("add", props.editData, { uuid: props.editData.uuid });
}

/**提交逻辑已在useForm中完成，不需要额外处理。此处仅用作其他额外需要拓展的情况使用 */
function handleSubmit() {}

/**提交成功回调。多为关闭弹窗、提示成功 */
function handleSuccess() {
	emits("onSuccess");
}

/**取消逻辑已在useForm中完成，不需要额外处理。此处仅用作其他额外需要拓展的情况使用 */
function handleCancel() {}

/**本组件中的dialog关闭不代表父组件控制弹窗用的变量改为了false。需要手动emit一下 */
function handleClosed() {
	close();
}

/**关闭弹窗，通知父组件 */
function close() {
	_showMemberLeave.value = false;
}
</script>

<style scoped>
.hint-text {
	font-size: 14px;
	color: var(--el-color-warning);
	margin-top: -12px;
	margin-bottom: 12px;
}

.member-name {
	font-size: 14px;
	margin-left: 16px;
}
</style>
