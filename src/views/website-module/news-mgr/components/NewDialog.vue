<!-- 新增/编辑新闻弹窗 -->
<template>
	<el-dialog
		v-model="dialogVisible"
		:title="mode === 'add' ? '新增新闻' : '编辑新闻'"
		width="1000"
		:before-close="close"
		:destroy-on-close="true"
	>
		<MetaForm
			v-if="mode === 'add' || mode === 'edit'"
			:mode="mode"
			ref="form"
			:formConfig="formConfig"
			:styleConfig="{ mode: 'dialog', labelWidth: '120px' }"
			:rule-form="ruleForm"
		>
		</MetaForm>
	</el-dialog>
</template>
<script lang="ts" setup>
import { ref, reactive, nextTick } from "vue";
import MetaForm from "@/meta-components/MetaForm/index.vue";
import { FormConfigOptions, FormItemOptions } from "@/meta-components/MetaForm/interface";
import { NewsService } from "@/api/modules/website";
import { UploadService } from "@/api/modules/upload";
import { cloneDeep } from "lodash";

const emits = defineEmits(["onSuccess"]);
const dialogVisible = ref(false);
const detailData = ref();
let mode = ref<string>();
let form = ref();

const open = (modeStr: "add" | "edit", data: any) => {
	mode.value = modeStr;
	const tempData = cloneDeep(data);
	if (modeStr === "edit") {
		detailData.value = tempData;
	}
	nextTick(() => {
		setTimeout(() => {
			if (form.value) {
				form.value.iniForm(modeStr, {}, {});
			}
		});
	});
	dialogVisible.value = true;
};
const close = () => {
	dialogVisible.value = false;
};
defineExpose({ open, close });

const formContent: Array<FormItemOptions> = [
	{
		label: "标题（中文）",
		type: "input",
		placeholder: "请输入",
		name: "titleZh",
		span: 12,
		otherAttr: {
			max: 50
		}
	},
	{
		label: "标题（英文）",
		type: "input",
		placeholder: "请输入",
		name: "titleEn",
		span: 12,
		otherAttr: {
			max: 200
		}
	},
	{
		label: "作者（中文）",
		type: "input",
		placeholder: "请输入",
		name: "authorZh",
		span: 12,
		otherAttr: {
			max: 10
		}
	},
	{
		label: "作者（英文）",
		type: "input",
		placeholder: "请输入",
		name: "authorEn",
		span: 12,
		otherAttr: {
			max: 25
		}
	},
	{
		label: "文章链接",
		type: "input",
		placeholder: "请输入",
		name: "articleLink",
		span: 14,
		otherAttr: {
			max: 80
		}
	},
	{
		label: "发布时间",
		type: "date",
		placeholder: "请选择",
		name: "viewDate",
		span: 14
	},
	{
		label: "封面图",
		placeholder: "请上传",
		name: "coverImg",
		type: "imageUpload",
		hint: "请上传jpg/png文件，且不超过5MB，建议比例为3:2",
		api: UploadService.ossApi,
		otherAttr: { showUploadPlaceholder: false },
		imageOptions: {
			pathName: "news",
			mode: "single",
			limitNum: 1,
			maxFileSize: 5,
			width: 270,
			editDisabled: false,
			editOptions: {
				aspectRatio: 3 / 2,
				components: ["crop"]
			}
		}
	}
];
const ruleForm = reactive({
	titleZh: [{ required: true, message: "请输入文章标题" }],
	titleEn: [{ required: true, message: "请输入文章标题" }],
	authorZh: [{ required: true, message: "请输入文章作者" }],
	authorEn: [{ required: true, message: "请输入文章作者" }],
	viewDate: [{ required: true, message: "请输入文章发布时间" }],
	coverImg: [{ required: true, message: "请上传封面图" }],
	articleLink: [{ required: true, message: "请输入文章链接" }]
});

function handleSuccess() {
	emits("onSuccess");
}
const formConfig: FormConfigOptions = {
	key: "edit-warehouse",
	items: formContent,
	addApi: NewsService.add,
	editApi: NewsService.update,
	successCallBack: handleSuccess,
	detailApi: () => Promise.resolve({ code: 0, data: detailData.value }),
	detailKey: "uuid",
	closeFunction: close,
	closeFormAt: "both",
	hintMessage: ["创建新闻成功", "保存新闻成功"]
};
</script>
<style lang="scss" scoped>
.contact-item {
	width: 60%;
	margin-bottom: 4px !important;
}
.button {
	cursor: pointer;
}
.add-button {
	width: 60%;
	height: 32px;
	text-align: center;
	border: 1px dashed #c0c4cc;
	border-radius: 5px;
}
.icon-button {
	margin-left: 10px;
}
:global(.warehouse-dialog > .el-dialog__body) {
	padding-bottom: 0;
}
</style>
