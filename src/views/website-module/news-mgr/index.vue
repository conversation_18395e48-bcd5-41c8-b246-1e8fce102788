<!--新闻管理-->
<template>
	<div class="page-container">
		<div style="height: 100%">
			<TableLayout ref="newsTableRef" title="新闻管理" :table-config="tableConfig" :filter-config="filterConfig">
				<template #button>
					<AddBtn :btn-config="addBtnConfig" />
				</template>
				<template #coverImg="{ row }">
					<el-image
						v-if="row.coverImg"
						:src="row.coverImg"
						fit="contain"
						class="img-in-table"
						:lazy="true"
						preview-teleported
						:hide-on-click-modal="true"
						:preview-src-list="[row.coverImg]"
					/>
					<span v-else>-</span>
				</template>
				<template #articleLink="{ row }">
					<el-link :underline="false" :href="row.articleLink" target="_blank">
						{{ row.articleLink }}
					</el-link>
				</template>
				<template #opr="scope">
					<el-link
						v-if="hasAuth('编辑新闻')"
						style="margin-right: 12px"
						:underline="false"
						type="primary"
						@click="handleNewsBtn('edit', scope.row)"
						>编辑</el-link
					>
					<div v-if="hasAuth('删除新闻')">
						<OprDelete @on-delete="handleBtnDelete(scope.row)"></OprDelete>
					</div>
				</template>
				<template #viewDate="{ row }">
					{{ row.viewDate ? format(new Date(row.viewDate), "yyyy-MM-dd") : "-" }}
				</template>
			</TableLayout>
			<NewDialog ref="newDialogRef" @on-success="refresh" />
		</div>
	</div>
</template>

<script setup lang="ts" name="NewsMgr">
import TableLayout from "@/meta-components/MetaTable/index.vue";
import { ref, reactive, onMounted } from "vue";
import { ColumnProps, FilterConfigOptions, TableConfig } from "@/meta-components/MetaTable/interface";
import { NewsService } from "@/api/modules/website";
import { Website } from "@/api/interface/website.model";
import OprDelete from "@/meta-components/MetaTableComponents/OprDelete.vue";
import { ElMessage } from "element-plus";
import { hasAuth } from "@/utils/util";
import AddBtn, { AddBtnConfig } from "@/components/AddBtn/index.vue";
import { useTableMemory } from "@/meta-components/MetaHooks/usePageMemory";
import NewDialog from "./components/NewDialog.vue";

import { useTableScrollMemory } from "@/hooks/useTableScrollMemory";
import { format } from "date-fns";

let newsTableRef = ref();

/**筛选框配置数量 */
let filterConfig: Array<FilterConfigOptions> = reactive([
	{ name: "title", type: "input", span: 3, placeholder: "请输入新闻标题" },
	{ name: "author", type: "input", span: 3, placeholder: "请输入作者" },
	{ name: "ViewDate", type: "dateRange", span: 8, placeholder: "选择新闻发布时间" }
]);

let columnsConfig: Array<ColumnProps> = [
	{
		label: "新闻标题（中文）",
		prop: "titleZh"
	},
	{
		label: "新闻标题（英文）",
		prop: "titleEn"
	},
	{
		label: "作者（中文）",
		width: 120,
		prop: "authorZh"
	},
	{
		label: "作者（英文）",
		width: 120,
		prop: "authorEn"
	},
	{ label: "新闻链接", slotName: "articleLink" },
	{
		label: "发布时间",
		slotName: "viewDate",
		width: 180
	},
	{
		label: "封面图",
		width: 100,
		slotName: "coverImg"
	},
	{
		label: "操作",
		slotName: "opr",
		fixed: "right"
	}
];

let tableConfig: TableConfig = reactive({
	key: "website-news-mgr",
	columns: columnsConfig,
	requestApi: NewsService.getList,
	selectType: "none",
	selectId: "uuid",
	childrenKey: "contents",
	pagination: true,
	staticParam: {},
	defaultExpandAll: false
});
useTableMemory(newsTableRef, tableConfig);

const addBtnConfig = reactive<AddBtnConfig>({
	label: "新建新闻",
	clickFn: () => {
		return handleNewsBtn("add");
	},
	visible: () => {
		return hasAuth("新增新闻");
	}
});

/**弹窗 */
function handleNewsBtn(mode: any, data?: Website.CompanyNewsUpdateReq) {
	newDialogRef.value.open(mode, data);
}

defineExpose({ handleNewsBtn });
const emits = defineEmits(["onRefresh"]);
/**删除 */
async function handleBtnDelete(rowData: { [key: string]: any }) {
	let { code } = await NewsService.delete({ uuid: rowData.uuid });
	if (code !== 0) return;
	ElMessage.success("删除成功");
	refresh();
}

/**刷新 */
function refresh() {
	emits("onRefresh");
	newsTableRef.value.search();
}

let newDialogRef = ref();

onMounted(() => {
	useTableScrollMemory(newsTableRef, "NewsMgr");
});
</script>
