<!-- 培训记录页面 -->
<template>
	<div style="height: calc(100% - 24px); overflow-y: overlay" class="page-container">
		<MetaTable title="培训记录" :filter-config="filterConfig" :table-config="tableConfig">
			<template #departType="{ row }">
				{{ DepartTypeText[row.departType] }}
			</template>
			<template #srcType="{ row }">
				{{ SrcTypeText[row.srcType] }}
			</template>
			<template #sign="{ row }">
				<el-image
					v-if="row.sign"
					:src="row.sign"
					fit="cover"
					class="img-in-table"
					:lazy="true"
					preview-teleported
					:hide-on-click-modal="true"
					:preview-src-list="[row.sign]"
				/>
				<span v-else>-</span>
			</template>
		</MetaTable>
	</div>
</template>
<script setup lang="ts" name="TrainRecord">
import { reactive } from "vue";
import { FilterConfigOptions, ColumnProps, TableConfig } from "@/meta-components/MetaTable/interface";
import MetaTable from "@/meta-components/MetaTable/index.vue";
import { TrainRecordService } from "@/api/modules/course";
import { DepartTypeText, SrcTypeText } from "@/api/interface/course";
/**筛选框配置数量 */
let filterConfig: Array<FilterConfigOptions> = reactive([
	{
		name: "departType",
		type: "singleSelect",
		span: 2,
		placeholder: "分类",
		staticOptions: [
			{ label: "全部", value: 1 },
			{ label: "甲板部", value: 2 },
			{ label: "轮机部", value: 3 },
			{ label: "责任部", value: 4 },
			{ label: "岗前培训", value: 5 }
		]
	},
	{
		name: "srcType",
		type: "singleSelect",
		span: 2,
		placeholder: "类型",
		staticOptions: [
			{ label: "图文课程", value: 1 },
			{ label: "视频课程", value: 2 }
		]
	},
	{
		name: "name",
		type: "input",
		span: 3,
		placeholder: "课程名称"
	},
	{
		name: "trainer",
		type: "input",
		span: 3,
		placeholder: "完成人"
	},
	{
		name: "completedAt",
		type: "dateRange",
		span: 4,
		placeholder: "完成时间"
	}
]);

let columnsConfig: Array<ColumnProps> = reactive([
	{
		label: "uuid",
		prop: "uuid",
		isShow: false
	},
	{
		label: "课程分类",
		width: 100,
		slotName: "departType"
	},

	{
		label: "课程类型",
		width: 100,
		slotName: "srcType"
	},
	{
		label: "课程名称",
		minWidth: 180,
		prop: "name"
	},
	{
		label: "签名",
		minWidth: 200,
		slotName: "sign"
	},
	{
		label: "完成人",
		width: 120,
		prop: "trainerName"
	},
	{
		label: "完成时间",
		prop: "completedAt",
		width: 180,
		sortAttr: "completedAt",
		sortable: true
	}
]);

columnsConfig = columnsConfig.map(item => {
	return {
		...item,
		isShow: item.isShow ?? true
	};
});

/**表格配置 */
let tableConfig: TableConfig = reactive({
	key: "trainRecordKey",
	columns: columnsConfig,
	requestApi: TrainRecordService.getList,
	selectType: "none",
	showTitleArea: true,
	canChangeHead: false,
	pagination: true,
	staticParam: { status: 1 },
	initParam: {}
});
</script>

<style scoped lang="scss"></style>
