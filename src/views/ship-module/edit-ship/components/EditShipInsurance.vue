<template>
	<el-drawer
		ref="dialog"
		:title="mode === 'add' ? '添加船舶保险' : mode === 'edit' ? '编辑船舶保险' : '船舶保险详情'"
		v-model="_showEditShipInsurance"
		width="50%"
		@open="iniData"
		:before-close="confirmClose"
		@closed="handleClosed"
		draggable
	>
		<MetaForm
			v-if="mode === 'add' || mode === 'edit'"
			:mode="mode"
			ref="form"
			:formConfig="formConfig"
			:styleConfig="{ mode: 'drawer', labelWidth: '100px' }"
			:rule-form="ruleForm"
		>
		</MetaForm>
		<MetaDetail v-if="mode === 'detail'" :detail-config="detailConfig"> </MetaDetail>
	</el-drawer>
</template>

<script setup lang="ts" name="EditShipInsurance">
//LATER OCR
import MetaForm from "@/meta-components/MetaForm/index.vue";
import MetaDetail from "@/meta-components/MetaDetail/index.vue";
import { PropType, ref, reactive, computed, inject, Ref } from "vue";
import { FormConfigOptions, FormItemOptions } from "@/meta-components/MetaForm/interface";
import { ShipOptions } from "@/api/interface/ship";
import { DetailConfigOptions, DetailItemOptions } from "@/meta-components/MetaDetail/interface";
import _ from "lodash";
import { UploadService } from "@/api/modules/upload";
import { useDialog } from "@/meta-components/MetaHooks/useDialog";
// let { add, edit, getDetail } = ShipInsuranceService;

let props = defineProps({
	mode: { type: String as PropType<"add" | "edit" | "detail">, requeired: true },
	showEditShipInsurance: { type: Boolean, required: true },
	editData: { type: Object, required: true },
	staticParams: { type: Object, required: true }
});

let ruleForm = reactive({
	// no: [{ required: true, message: "请输入保单编号" }],
	name: [{ required: true, message: "请输入保险名称" }],
	invalidTime: [{ required: true, message: "请选择失效日期" }]
	// validTime: [{ required: true, message: "请选择生效日期" }]
});

const emits = defineEmits(["update:showEditShipInsurance", "onSuccess"]);

const _showEditShipInsurance = computed({
	get() {
		return props.showEditShipInsurance;
	},
	set(value) {
		emits("update:showEditShipInsurance", value);
	}
});

const formContent: Array<FormItemOptions> = [
	{ label: "基础信息", type: "title", name: "" },
	{
		label: "保险名称",
		type: "input",
		placeholder: "请输入",
		name: "name",
		span: 24,
		otherAttr: {
			max: 50
		}
	},
	{
		label: "保单编号",
		type: "input",
		placeholder: "请输入",
		name: "no",
		span: 24,
		otherAttr: {
			max: 50
		}
	},
	//TODO 完整添加保险处需要
	// {
	// 	label: "所属船舶",
	// 	type: "singleSelect",
	// 	placeholder: "请输入",
	// 	name: "no",
	// 	span: 24,
	// 	otherAttr: {
	// 		max: 20
	// 	}
	// },
	{
		label: "保险周期",
		type: "input",
		name: "insuranceCycle",
		placeholder: "请输入",
		otherAttr: {
			inputOnlyNumber: true,
			showWordLimit: false,
			append: "年"
		}
	},
	{
		label: "生效日期",
		type: "date",
		placeholder: "请选择",
		name: "validTime",
		span: 24
	},
	{
		label: "有效期至",
		type: "date",
		placeholder: "请选择",
		name: "invalidTime",
		span: 24
	},
	{
		label: "保费",
		type: "input",
		placeholder: "请输入",
		name: "insurancePremium",
		span: 24,
		otherAttr: {
			inputOnlyNumber: true,
			showWordLimit: false,
			precision: 2,
			prepend: "¥"
		}
	},
	{
		label: "保额",
		type: "input",
		placeholder: "请输入",
		name: "insuranceAmount",
		span: 24,
		otherAttr: {
			inputOnlyNumber: true,
			showWordLimit: false,
			precision: 2,
			prepend: "¥"
		}
	},
	{
		label: "保险机构",
		type: "input",
		placeholder: "请输入",
		name: "insuranceAgency",
		otherAttr: {
			max: 20
		}
	},
	{
		label: "备注",
		type: "textarea",
		placeholder: "请输入",
		name: "remark",
		otherAttr: {
			max: 200
		}
	},
	{ label: "保险原件", type: "title", name: "" },
	{
		label: "上传附件",
		type: "fileUpload",
		name: "attachs",
		api: UploadService.ossApi,
		imageOptions: {
			pathName: "ship-insurance",
			mode: "multiple",
			limitNum: 100
		}
	}
];

//TODO 附件上传

const detailContent: Array<DetailItemOptions> = [
	{ label: "基础信息", type: "title", name: "" },
	{
		label: "保险名称",
		type: "detail",
		name: "name",
		span: 24
	},
	{
		label: "保单编号",
		type: "detail",
		name: "no",
		span: 24
	},
	//TODO 完整添加保险处需要
	// {
	// 	label: "所属船舶",
	// 	type: "singleSelect",
	// 	placeholder: "请输入",
	// 	name: "no",
	// 	span: 24,
	// 	otherAttr: {
	// 		max: 20
	// 	}
	// },
	{
		label: "保险周期",
		type: "detail",
		name: "insuranceCycle",
		otherAttr: {
			append: "年"
		}
	},
	{
		label: "生效日期",
		type: "detail",
		name: "validTime",
		span: 24,
		otherAttr: {
			isDate: true
		}
	},
	{
		label: "有效期至",
		type: "detail",
		name: "invalidTime",
		span: 24,
		otherAttr: {
			isDate: true
		}
	},
	{
		label: "保费",
		type: "detail",
		name: "insurancePremium",
		span: 24,
		otherAttr: {
			precision: 2,
			prepend: "¥"
		}
	},
	{
		label: "保额",
		type: "detail",
		name: "insuranceAmount",
		span: 24,
		otherAttr: {
			precision: 2,
			prepend: "¥"
		}
	},
	{
		label: "保险机构",
		type: "detail",
		name: "insuranceAgency"
	},
	{
		label: "备注",
		type: "detail",
		name: "remark"
	},
	{ label: "保险原件", type: "title", name: "" },
	{ label: "附件列表", type: "fileList", name: "attachs" }
];

const detailConfig: DetailConfigOptions = {
	detailData: props.editData,
	labelPosition: "left",
	items: detailContent
};

let shipFormData = inject("shipFormData") as Ref<ShipOptions.ShipDetail>;

function addData(params: { [key: string]: any }) {
	return new Promise(resolve => {
		//手动添加的数据，id为负
		params.id = -Number(new Date().getTime() + "" + Math.floor(Math.random() * 1000000));
		shipFormData.value.insurances!.push(_.cloneDeep(Object.assign(params, props.staticParams)));
		resolve({ code: 0 });
	});
}

/**本地编辑数据 */
function editData(params: { [key: string]: any }) {
	return new Promise(resolve => {
		shipFormData.value.insurances![shipFormData.value.insurances!.findIndex((i: { [key: string]: any }) => i.id === params.id)] =
			_.cloneDeep(params);
		resolve({ code: 0 });
	});
}

const formConfig: FormConfigOptions = {
	key: "edit-shipInsurance",
	items: formContent,
	addApi: addData,
	editApi: editData,
	successCallBack: handleSuccess,
	// detailApi: getDetail,
	// detailKey: "uuid",
	closeFunction: close,
	closeFormAt: "both",
	hintMessage: ["创建船舶保险成功", "保存船舶保险成功"]
};

//手动调用metaForm的初始化
let form = ref();

const { confirmClose } = useDialog(form);

function iniData() {
	if (props.mode === "detail") return;
	form.value.iniForm(props.mode, props.editData);
}

/**提交成功回调。多为关闭弹窗、提示成功 */
function handleSuccess() {
	emits("onSuccess");
}

/**本组件中的dialog关闭不代表父组件控制弹窗用的变量改为了false。需要手动emit一下 */
function handleClosed() {
	close();
}

/**关闭弹窗，通知父组件 */
function close() {
	_showEditShipInsurance.value = false;
}
</script>

<style scoped></style>
