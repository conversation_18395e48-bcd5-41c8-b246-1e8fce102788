<template>
	<el-drawer
		ref="dialog"
		:title="mode === 'add' ? '添加船舶证书' : mode === 'edit' ? '编辑船舶证书' : '船舶证书详情'"
		v-model="_showEditShipCert"
		width="50%"
		@open="iniData"
		:before-close="confirmClose"
		@closed="handleClosed"
		draggable
	>
		<MetaForm
			v-if="mode === 'add' || mode === 'edit'"
			:mode="mode"
			ref="form"
			:formConfig="formConfig"
			:styleConfig="{ mode: 'drawer', labelWidth: '100px' }"
			:rule-form="ruleForm"
		>
		</MetaForm>
		<MetaDetail v-if="mode === 'detail'" :detail-config="detailConfig"> </MetaDetail>
	</el-drawer>
</template>

<script setup lang="ts" name="EditShipCert">
//LATER OCR
import MetaForm from "@/meta-components/MetaForm/index.vue";
import MetaDetail from "@/meta-components/MetaDetail/index.vue";
import { PropType, ref, reactive, computed, inject, Ref } from "vue";
import { FormConfigOptions, FormItemOptions } from "@/meta-components/MetaForm/interface";
import { ShipOptions } from "@/api/interface/ship";
import _ from "lodash";
import { DetailConfigOptions, DetailItemOptions } from "@/meta-components/MetaDetail/interface";
import { UploadService } from "@/api/modules/upload";
// let { add, edit, getDetail } = ShipCertService;
import { useDialog } from "@/meta-components/MetaHooks/useDialog";

let props = defineProps({
	mode: {
		type: String as PropType<"add" | "edit" | "detail">,
		requeired: true
	},
	showEditShipCert: { type: Boolean, required: true },
	editData: { type: Object, required: true },
	staticParams: { type: Object, required: true }
});

let ruleForm = reactive({
	// no: [{ required: true, message: "请输入证书编号" }],
	shipCertType: [{ required: true, message: "请输入证书类型" }],
	// signTime: [{ required: true, message: "请输入签发时间" }],
	validTime: [{ required: true, message: "请输入到期时间" }]
});

const emits = defineEmits(["update:showEditShipCert", "onSuccess"]);

const _showEditShipCert = computed({
	get() {
		return props.showEditShipCert;
	},
	set(value) {
		emits("update:showEditShipCert", value);
	}
});

const formContent: Array<FormItemOptions> = [
	{ label: "基础信息", type: "title", name: "" },
	{
		label: "证书类型",
		type: "input",
		placeholder: "请输入",
		name: "shipCertType",
		span: 24,
		otherAttr: {
			max: 60
		}
	},
	{
		label: "证书编号",
		type: "input",
		placeholder: "请输入",
		name: "no",
		span: 24,
		otherAttr: {
			max: 50
		}
	},
	{
		label: "签发日期",
		type: "date",
		placeholder: "请选择",
		name: "signTime",
		span: 24
	},
	// {
	// 	label: "永久有效",
	// 	type: "switch",
	// 	placeholder: "请选择",
	// 	name: "isNoValidTime",
	// 	otherAttr: { activeText: "是", inactiveText: "否" },
	// 	span: 8
	// },
	{
		label: "有效期至",
		type: "date",
		placeholder: "请选择",
		name: "validTime",
		visible: () => !form?.value?.formData?.isNoValidTime,
		span: 24
	},
	{
		label: "签发机构",
		type: "input",
		placeholder: "请输入",
		name: "signAgency",
		span: 24
	},
	{
		label: "备注",
		type: "textarea",
		placeholder: "请输入",
		name: "remark",
		otherAttr: {
			max: 200
		}
	},
	{ label: "证书原件", type: "title", name: "" },
	{
		label: "上传附件",
		type: "fileUpload",
		name: "attachs",
		api: UploadService.ossApi,
		imageOptions: {
			pathName: "ship-cert",
			mode: "multiple",
			limitNum: 100
		}
	}
];

//TODO 附件上传

const detailContent: Array<DetailItemOptions> = [
	{ label: "基础信息", type: "title", name: "" },
	{
		label: "证书类型",
		type: "detail",
		name: "shipCertType",
		span: 24
	},
	{
		label: "证书编号",
		type: "detail",
		name: "no",
		span: 24
	},
	//TODO 完整添加证书处需要
	// {
	// 	label: "所属船舶",
	// 	type: "singleSelect",
	// 	placeholder: "请输入",
	// 	name: "no",
	// 	span: 24,
	// 	otherAttr: {
	// 		max: 20
	// 	}
	// },

	{
		label: "签发日期",
		type: "detail",
		name: "signTime",
		span: 24,
		otherAttr: {
			isDate: true
		}
	},
	{
		label: "有效期至",
		type: "detail",
		name: "validTime",
		span: 24,
		otherAttr: {
			isDate: true
		}
	},
	{
		label: "签发机构",
		type: "detail",
		name: "signAgency"
	},
	{
		label: "备注",
		type: "detail",
		name: "remark"
	},
	{ label: "证书原件", type: "title", name: "" },
	{ label: "附件列表", type: "fileList", name: "attachs" }
];

const detailConfig: DetailConfigOptions = {
	detailData: props.editData,
	labelPosition: "left",
	items: detailContent
};

let shipFormData = inject("shipFormData") as Ref<ShipOptions.ShipDetail>;

function addData(params: { [key: string]: any }) {
	return new Promise(resolve => {
		//手动添加的数据，id为负
		params.id = -Number(new Date().getTime() + "" + Math.floor(Math.random() * 1000000));
		shipFormData.value.certs!.push(_.cloneDeep(Object.assign(params, props.staticParams)));
		resolve({ code: 0 });
	});
}

/**本地编辑数据 */
function editData(params: { [key: string]: any }) {
	return new Promise(resolve => {
		shipFormData.value.certs![shipFormData.value.certs!.findIndex((i: { [key: string]: any }) => i.id === params.id)] =
			_.cloneDeep(params);
		resolve({ code: 0 });
	});
}

const formConfig: FormConfigOptions = {
	key: "edit-shipCert",
	items: formContent,
	addApi: addData,
	editApi: editData,
	successCallBack: handleSuccess,
	beforeSubmit: beforeSubmit,
	dataCallBack: dataCallBack,
	// detailApi: getDetail,
	// detailKey: "uuid",
	closeFunction: close,
	closeFormAt: "both",
	hintMessage: ["创建船舶证书成功", "保存船舶证书成功"]
};

/**若长期有效，则手动修改时间 */
function beforeSubmit(data: any) {
	if (data.isNoValidTime) {
		data.validTime = "9999-12-31 00:00:00";
	}
	return data;
}

/**编辑信息获取到数据后，若到期时间为9999，则记为长期有效 */
function dataCallBack(data: any) {
	if (data.validTime === "9999-12-31 00:00:00") {
		data.isNoValidTime = true;
		delete data.validTime;
	}
	return data;
}

//手动调用metaForm的初始化
let form = ref();

const { confirmClose } = useDialog(form);

function iniData() {
	if (props.mode === "detail") return;
	form.value.iniForm(props.mode, props.editData);
}

/**提交成功回调。多为关闭弹窗、提示成功 */
function handleSuccess() {
	emits("onSuccess");
}

/**本组件中的dialog关闭不代表父组件控制弹窗用的变量改为了false。需要手动emit一下 */
function handleClosed() {
	close();
}

/**关闭弹窗，通知父组件 */
function close() {
	_showEditShipCert.value = false;
}
</script>

<style scoped></style>
