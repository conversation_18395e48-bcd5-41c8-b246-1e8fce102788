<!--船舶管理-->
<template>
	<div style="height: 100%">
		<router-view v-slot="{ Component }" v-if="$route.name !== 'ShipMgr'">
			<transition appear name="fade-transform" mode="out-in">
				<component :is="Component" @on-success="handleSuccess" />
			</transition>
		</router-view>
		<div style="height: 100%" v-show="$route.name === 'ShipMgr'">
			<KeepAlive>
				<TwoPartsPage title="船舶管理" expand-hint="展开船舶列表">
					<template #left>
						<LeftPart ref="shipListRef" />
					</template>
					<template #right>
						<transition appear name="fade-transform" mode="out-in">
							<RightPart :key="selectedShipUuid" v-model:selectedTabs="selectedTabs"></RightPart>
						</transition>
					</template> </TwoPartsPage
			></KeepAlive>
		</div>
	</div>
</template>

<script setup lang="ts" name="ShipMgr">
import LeftPart from "./ship-list/index.vue";
import RightPart from "./ship-detail/index.vue";
import TwoPartsPage from "@/meta-components/MetaLayout/TwoPartsPage.vue";
// const keys = [	"船舶列表",	"船舶筛选",	"添加船舶",	"编辑船舶",	"导出船舶",	"船舶详情",	"顶部信息",	"船舶信息",	"在船船员",	"设备信息",	"调度信息",	"证书保险",	"报修记录",	"备件档案"];
import { provide, ref, onActivated } from "vue";
import { hasAuth } from "@/utils/util";

/**船舶列表 */
const shipList = ref([] as any[]);
/**选中船舶id */
const selectedShipUuid = ref(undefined as string | undefined);
const shipListRef = ref();
/**选中的导航栏 */
const selectedTabs = ref(setDefaultTabsByAuth());

provide("shipList", shipList);
provide("selectedShipUuid", selectedShipUuid);

// 根据权限设置tabs默认值
function setDefaultTabsByAuth() {
	if (hasAuth("调度信息")) {
		return "调度信息";
	} else if (hasAuth("船舶信息")) {
		return "船舶信息";
	} else if (hasAuth("在船船员")) {
		return "在船船员";
	} else if (hasAuth("设备信息")) {
		return "设备信息";
	} else if (hasAuth("证书保险")) {
		return "证书保险";
	} else if (hasAuth("维护记录")) {
		return "维护记录";
	} else if (hasAuth("备件记录")) {
		return "备件记录";
	} else if (hasAuth("附件资料")) {
		return "附件资料";
	} else if (hasAuth("变更记录")) {
		return "变更记录";
	} else if (hasAuth("体系台账")) {
		return "体系台账";
	} else {
		return "";
	}
}

/**船舶创建完成 */
function handleSuccess() {
	shipListRef.value.search();
}

// 在船舶库组件被唤醒时需要刷新船舶库船舶列表 并重置右侧标签栏值
onActivated(() => {
	selectedTabs.value = setDefaultTabsByAuth();
	shipListRef.value.search();
});
</script>

<style scoped></style>
