<!--锚设备管理in船舶信息-->
<template>
	<div style="width: 100%; height: auto">
		<div class="table-outer-container">
			<div class="table-before">
				<h4>锚</h4>
				<el-link
					v-if="hasAuth('锚新建') && isSelfSail !== 0 && mode === 'add'"
					class="title-hint-pos"
					:underline="false"
					type="primary"
					@click="addDevice(AnchorType.Anchor)"
					>添加锚</el-link
				>
			</div>
			<MetaTable ref="anchorRef" title="锚" :table-config="tableConfig(AnchorType.Anchor)">
				<template #opr="scope">
					<el-link
						v-if="hasAuth('锚更新') && mode === 'add'"
						style="margin-right: 12px"
						:underline="false"
						type="primary"
						@click="inlineEdit(AnchorType.Anchor, scope.row)"
						>编辑</el-link
					>
					<div v-if="hasAuth('锚删除') && mode === 'add'">
						<OprDelete @on-delete="deleteData(AnchorType.Anchor, scope.row)"></OprDelete>
					</div>
				</template>
			</MetaTable>
		</div>
		<div class="table-outer-container">
			<div class="table-before">
				<h4>锚机</h4>
				<el-link
					v-if="hasAuth('锚机新建') && isSelfSail !== 0 && mode === 'add'"
					class="title-hint-pos"
					:underline="false"
					type="primary"
					@click="addDevice(AnchorType.AnchorMachine)"
					>添加锚机</el-link
				>
			</div>
			<MetaTable ref="anchorMachineRef" title="锚机" :table-config="tableConfig(AnchorType.AnchorMachine)">
				<template #opr="scope">
					<el-link
						v-if="hasAuth('锚机更新') && mode !== 'detail'"
						style="margin-right: 12px"
						:underline="false"
						type="primary"
						@click="inlineEdit(AnchorType.AnchorMachine, scope.row)"
						>编辑</el-link
					>
					<div v-if="hasAuth('锚机删除') && mode !== 'detail'">
						<OprDelete @on-delete="deleteData(AnchorType.AnchorMachine, scope.row)"></OprDelete>
					</div>
				</template>
			</MetaTable>
		</div>
		<div class="table-outer-container">
			<div class="table-before">
				<h4>锚链</h4>
				<el-link
					v-if="hasAuth('锚链新建') && isSelfSail !== 0 && mode === 'add'"
					class="title-hint-pos"
					:underline="false"
					type="primary"
					@click="addDevice(AnchorType.AnchorChain)"
					>添加锚链</el-link
				>
			</div>
			<MetaTable ref="anchorChainRef" title="锚链" :table-config="tableConfig(AnchorType.AnchorChain)">
				<template #opr="scope">
					<el-link
						v-if="hasAuth('锚链更新') && mode !== 'detail'"
						style="margin-right: 12px"
						:underline="false"
						type="primary"
						@click="inlineEdit(AnchorType.AnchorChain, scope.row)"
						>编辑</el-link
					>
					<div v-if="hasAuth('锚链删除') && mode !== 'detail'">
						<OprDelete @on-delete="deleteData(AnchorType.AnchorChain, scope.row)"></OprDelete>
					</div>
				</template>
			</MetaTable>
		</div>
	</div>
</template>

<script setup lang="ts">
import MetaTable from "@/meta-components/MetaTable/index.vue";
import { ref, reactive, inject, PropType, Ref } from "vue";
import { ColumnProps, TableConfig } from "@/meta-components/MetaTable/interface";
import OprDelete from "@/meta-components/MetaTableComponents/OprDelete.vue";
import { ElMessage } from "element-plus";
import { DeviceOptions, ShipOptions } from "@/api/interface/ship";
import { DeviceService, ShipService } from "@/api/modules/ship";
import { hasAuth } from "@/utils/util";
import _ from "lodash";
import DeviceChangeLog, { DeviceTypeEnum } from "./DeviceChangeLog";

enum AnchorType {
	/** 锚 */
	"Anchor" = 1,
	/** 锚机 */
	"AnchorMachine" = 2,
	/** 锚链 */
	"AnchorChain" = 3
}
const AnchorTextMap = new Map([
	["Anchor", "锚"],
	["AnchorMachine", "锚机"],
	["AnchorChain", "锚链"]
]);

let devicesData = inject("detailData") as Ref<ShipOptions.ResDevices>;
const shipDetail = inject("shipDetail") as Ref<ShipOptions.ResBriefInfo>;
const emits = defineEmits(["needRefresh"]);

let anchorRef = ref();
let anchorMachineRef = ref();
let anchorChainRef = ref();

let props = defineProps({
	staticParams: { type: Object, required: true },
	mode: { type: String as PropType<"add" | "edit" | "detail">, required: true },
	isSelfSail: { type: Number as PropType<number | undefined | null> }
});

let columnsAnchor: Array<ColumnProps> = [
	{
		label: "名称",
		prop: "name",
		minWidth: "80px",
		inlineEdit: {
			type: "input",
			placeholder: "请输入",
			name: "name",
			otherAttr: {
				showWordLimit: false
			}
		}
	},
	{
		label: "型式",
		prop: "anchorType",
		minWidth: "80px",
		inlineEdit: {
			type: "input",
			placeholder: "请输入",
			name: "anchorType",
			otherAttr: {
				showWordLimit: false
			}
		}
	},
	{
		label: "重量(kg)",
		prop: "weight",
		minWidth: "80px",
		inlineEdit: {
			type: "input",
			placeholder: "请输入",
			name: "weight",
			otherAttr: {
				showWordLimit: false,
				inputOnlyNumber: true,
				precision: 2
			}
		}
	},
	{
		label: "数量",
		prop: "num",
		minWidth: "80px",
		inlineEdit: {
			type: "number",
			placeholder: "请输入",
			name: "num",
			otherAttr: {
				showWordLimit: false,
				inputOnlyNumber: true,
				precision: 0
			}
		}
	},
	{
		label: "操作",
		slotName: "opr",
		width: "100px"
	}
];
let columnsMachine: Array<ColumnProps> = [
	{
		label: "名称",
		prop: "name",
		minWidth: "80px",
		inlineEdit: {
			type: "input",
			placeholder: "请输入",
			name: "name",
			otherAttr: {
				showWordLimit: false
			}
		}
	},
	{
		label: "型号",
		prop: "model",
		minWidth: "80px",
		inlineEdit: {
			type: "input",
			placeholder: "请输入",
			name: "model",
			otherAttr: {
				showWordLimit: false
			}
		}
	},
	{
		label: "功率(kW)",
		prop: "power",
		minWidth: "80px",
		inlineEdit: {
			type: "input",
			placeholder: "请输入",
			name: "power",
			otherAttr: {
				showWordLimit: false,
				inputOnlyNumber: true,
				precision: 0
			}
		}
	},
	{
		label: "数量",
		prop: "num",
		minWidth: "80px",
		inlineEdit: {
			type: "number",
			placeholder: "请输入",
			name: "num",
			otherAttr: {
				showWordLimit: false,
				inputOnlyNumber: true,
				precision: 0
			}
		}
	},
	{
		label: "操作",
		slotName: "opr",
		width: "100px"
	}
];
let columnsChain: Array<ColumnProps> = [
	{
		label: "名称",
		prop: "name",
		minWidth: "80px",
		inlineEdit: {
			type: "input",
			placeholder: "请输入",
			name: "name",
			otherAttr: {
				showWordLimit: false
			}
		}
	},
	{
		label: "直径(mm)",
		prop: "diameter",
		minWidth: "80px",
		inlineEdit: {
			type: "input",
			placeholder: "请输入",
			name: "diameter",
			otherAttr: {
				showWordLimit: false,
				inputOnlyNumber: true,
				precision: 2
			}
		}
	},
	{
		label: "长度(m)",
		prop: "long",
		minWidth: "80px",
		inlineEdit: {
			type: "input",
			placeholder: "请输入",
			name: "long",
			otherAttr: {
				showWordLimit: false,
				inputOnlyNumber: true,
				precision: 2
			}
		}
	},
	{
		label: "等级",
		prop: "level",
		minWidth: "80px",
		inlineEdit: {
			type: "input",
			placeholder: "请输入",
			name: "level",
			otherAttr: {
				showWordLimit: false
			}
		}
	},
	{
		label: "材料",
		prop: "material",
		minWidth: "80px",
		inlineEdit: {
			type: "input",
			placeholder: "请输入",
			name: "material",
			otherAttr: {
				showWordLimit: false
			}
		}
	},
	{
		label: "操作",
		slotName: "opr",
		width: "100px"
	}
];

function validatorAnchor(p: any) {
	let res = { success: false, msg: "请正确填写全部信息" };
	if (!p.name || !p.anchorType || !p.weight || !p.num) {
		return res;
	}

	res.success = true;
	res.msg = "";
	return res;
}

function validatorMachine(p: any) {
	let res = { success: false, msg: "请正确填写全部信息" };
	if (!p.name || !p.model || !p.power || !p.num) {
		return res;
	}

	res.success = true;
	res.msg = "";
	return res;
}

function validatorChain(p: any) {
	let res = { success: false, msg: "请正确填写全部信息" };
	if (!p.name || !p.diameter || !p.long || !p.level || !p.material) {
		return res;
	}

	res.success = true;
	res.msg = "";
	return res;
}
const commonTableConfig: TableConfig = reactive({
	key: "anchor-device-mgr",
	mode: "mini",
	resizable: false,
	columns: columnsAnchor,
	requestApi: () => {
		return new Promise(resolve => {
			resolve({ code: 0, data: { list: devicesData?.value?.anchors ?? [] } });
		});
	},
	selectType: "none",
	selectId: "id",
	pagination: true,
	// staticParam: props.staticParams,
	canInlineEdit: true,
	inlineStaticParams: props.staticParams,
	inlineSubmitValidator: validatorAnchor,
	showInlineAddBtn: false,
	canChangeHead: false
});
const diffTableConfig = [
	{
		title: "锚",
		inlineAddText: "添加锚",
		columns: columnsAnchor,
		inlineSubmitValidator: validatorAnchor,
		requestApi: () => {
			return new Promise(resolve => {
				resolve({ code: 0, data: { list: devicesData?.value?.anchors ?? [] } });
			});
		}
	},
	{
		title: "锚机",
		inlineAddText: "添加锚机",
		columns: columnsMachine,
		inlineSubmitValidator: validatorMachine,
		requestApi: () => {
			return new Promise(resolve => {
				resolve({ code: 0, data: { list: devicesData?.value?.anchorMachines ?? [] } });
			});
		}
	},
	{
		title: "锚链",
		inlineAddText: "添加锚链",
		columns: columnsChain,
		inlineSubmitValidator: validatorChain,
		requestApi: () => {
			return new Promise(resolve => {
				resolve({ code: 0, data: { list: devicesData?.value?.anchorChains ?? [] } });
			});
		}
	}
];

const tableConfig = (type: AnchorType) => {
	const config = {
		...commonTableConfig,
		...diffTableConfig[type - 1],
		inlineAddApi: (params: DeviceOptions.ReqDetailParam) => addData(type, params),
		inlineEditApi: (params: { [key: string]: string }) => editData(type, params)
	};
	return config;
};

/**添加设备 */
function addDevice(type: AnchorType) {
	switch (type) {
		case AnchorType.Anchor:
			anchorRef.value.enterInlineAdd();
			break;
		case AnchorType.AnchorMachine:
			anchorMachineRef.value.enterInlineAdd();
			break;
		case AnchorType.AnchorChain:
			anchorChainRef.value.enterInlineAdd();
			break;
	}
}

function refreshTable() {
	anchorRef.value.search();
	anchorMachineRef.value.search();
	anchorChainRef.value.search();
}

function checkNowEditing() {
	return (
		anchorRef.value.inlineEditProps.open ||
		anchorMachineRef.value.inlineEditProps.open ||
		anchorChainRef.value.inlineEditProps.open
	);
}
defineExpose({ refreshTable, checkNowEditing });

async function addData(type: AnchorType, params: DeviceOptions.ReqDetailParam) {
	let addapi =
		type === AnchorType.Anchor
			? DeviceService.addAnchor
			: type === AnchorType.AnchorMachine
			? DeviceService.addAnchorMachine
			: DeviceService.addAnchorChain;
	return new Promise(async (resolve, reject) => {
		await addChangeLog("add", {}, params, type);
		let { code } = await addapi({ shipId: shipDetail.value.id!, ...params });
		if (code !== 0) reject("error");
		ElMessage.success("添加成功");
		emits("needRefresh");
		resolve({ code: 0 });
	});
}

async function editData(type: AnchorType, params: any) {
	let editapi =
		type === AnchorType.Anchor
			? DeviceService.editAnchor
			: type === AnchorType.AnchorMachine
			? DeviceService.editAnchorMachine
			: DeviceService.editAnchorChain;

	return new Promise(async (resolve, reject) => {
		let { code } = await editapi(params);
		if (code !== 0) reject("error");
		ElMessage.success("保存成功");
		const deviceList: any[] =
			type === AnchorType.Anchor
				? devicesData.value.anchors
				: type === AnchorType.AnchorMachine
				? devicesData.value.anchorMachines
				: devicesData.value.anchorChains;

		const beforeData = deviceList?.find((e: any) => e.uuid === params.uuid) || {};
		await addChangeLog("edit", beforeData, params, type);
		emits("needRefresh");
		resolve({ code: 0 });
	});
}

/**删除 */
async function deleteData(type: AnchorType, rowData: { [key: string]: any }) {
	let delapi =
		type === AnchorType.Anchor
			? DeviceService.delAnchor
			: type === AnchorType.AnchorMachine
			? DeviceService.delAnchorMachine
			: DeviceService.delAnchorChain;
	let { code } = await delapi({ uuid: rowData.uuid });
	if (code !== 0) return;
	ElMessage.success("删除成功");
	const deviceList: any[] =
		type === AnchorType.Anchor
			? devicesData.value.anchors
			: type === AnchorType.AnchorMachine
			? devicesData.value.anchorMachines
			: devicesData.value.anchorChains;

	const beforeData = deviceList?.find((e: any) => e.uuid === rowData.uuid) || {};
	await addChangeLog("delete", beforeData, {}, type);
	emits("needRefresh");
}

/**行内编辑内容 */
function inlineEdit(type: AnchorType, rowData: any) {
	const table = type === AnchorType.Anchor ? anchorRef : type === AnchorType.AnchorMachine ? anchorMachineRef : anchorChainRef;
	table.value.setInlineEdit(true, rowData.id, rowData);
}

async function addChangeLog(
	mode: "add" | "edit" | "delete",
	dataBefore: { [key: string]: any },
	dataAfter: { [key: string]: any },
	type: AnchorType
) {
	dataBefore.type = AnchorTextMap.get(AnchorType[type]);
	const formContents: { [key: string]: any } = {
		Anchor: [
			{
				n: "name",
				l: "名称",
				t: "input"
			},
			{
				n: "anchorType",
				l: "型式",
				t: "input"
			},
			{
				n: "weight",
				l: "重量",
				t: "input"
			},
			{
				n: "num",
				l: "数量",
				t: "input"
			}
		],
		AnchorMachine: [
			{
				n: "name",
				l: "名称",
				t: "input"
			},
			{
				n: "model",
				l: "型号",
				t: "input"
			},
			{
				n: "power",
				l: "功率",
				t: "input"
			},
			{
				n: "num",
				l: "数量",
				t: "input"
			}
		],
		AnchorChain: [
			{
				n: "name",
				l: "名称",
				t: "input"
			},
			{
				n: "diameter",
				l: "直径",
				t: "input"
			},
			{
				n: "long",
				l: "长度",
				t: "input"
			},
			{
				n: "level",
				l: "等级",
				t: "input"
			},
			{
				n: "material",
				l: "材料",
				t: "input"
			}
		]
	};
	const deviceChnagelog = new DeviceChangeLog(
		mode,
		_.cloneDeep(dataBefore),
		_.cloneDeep(dataAfter),
		formContents[AnchorType[type]],
		DeviceTypeEnum.ANCHOR
	);
	await ShipService.addShipChangeRecord({
		type: ShipOptions.ShipChangeRecordTypeEnum.DEIVCE,
		content: deviceChnagelog.genLog(),
		shipId: shipDetail.value.id
	});
}
</script>

<style lang="scss" scoped>
.table-outer-container {
	margin-bottom: 12px;
	.table-before {
		display: flex;
		h4 {
			min-width: 48px;
			margin: 0;
		}
	}
}
</style>
