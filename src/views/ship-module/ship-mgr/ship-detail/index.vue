<!--船舶详情框架index-->
<template>
	<div v-auth="'船舶详情'" style="position: relative; display: flex; flex-direction: column; height: 100%">
		<template v-if="shipDetail.id">
			<el-empty v-if="selectedShipUuid === undefined" class="empty-img" description="暂无数据" />
			<div v-if="selectedShipUuid !== undefined" class="top-area">
				<ShipItem_AvatarVue :can-preview="true" :src="shipDetail.img ?? ''" :width="216" :height="144" />

				<div class="top-right">
					<!--船舶标题行-->
					<div class="flx-align-center">
						<ShipItem_StatusIconVue class="title-item" :status="shipDetail.status ?? 0" :is-selected="false" :size="20" />
						<text class="ship-title title-item">{{ shipDetail.name }}</text>
						<ShipItem_StatusVue class="title-item" :status="shipDetail.status ?? 0" />
						<ShipItem_WarningInDetailVue style="flex-grow: 1" class="title-item" :detail="shipDetail" />
					</div>
					<!-- 顶部信息区域 -->
					<el-row class="top-info-area" :gutter="20">
						<el-col class="top-item" :span="12"
							><text> 负责人：{{ shipDetail.principalName }} {{ shipDetail.contactMobile }}</text></el-col
						>
						<el-col class="top-item" :span="12"
							><text> 存油量：{{ shipDetail.oil ? parseFloat(shipDetail.oil).toFixed(3) + "t" : "暂无" }} </text></el-col
						>
						<el-col class="top-item" :span="12">
							<text v-if="shipDetail.taskName && shipDetail.projectName">
								<ToolTipOver :content="'当前调度：' + shipDetail.projectName + '-' + shipDetail.taskName"></ToolTipOver>
							</text>
							<text v-else>
								<ToolTipOver :content="'当前调度：' + (shipDetail.taskName ? shipDetail.taskName : '暂无')"></ToolTipOver>
							</text>
						</el-col>
						<el-col class="top-item" :span="12"
							><text v-if="shipDetail.endTime"> 截止时间：{{ format(parseISO(shipDetail.endTime), "yyyy年MM月dd日") }} </text>
							<text v-else> 截止时间：暂无 </text>
						</el-col>
						<el-col v-showTip class="top-item" :span="12">
							<text v-if="shipDetail.lon && shipDetail.lat">
								<ToolTipOver
									:content="
										'当前位置：' +
										LatlngCnvTool(Number(shipDetail.lat), Number(shipDetail.lon)) +
										(shipDetail.seaArea ? shipDetail.seaArea : '')
									"
								></ToolTipOver>
							</text>
							<!-- 当前位置：{{ LatlngCnvTool(Number(shipDetail.lat), Number(shipDetail.lon)) }}
							当前位置：{{ "经度" + DDDToDMS(Number(shipDetail.lon)) + " " + "纬度" + DDDToDMS(Number(shipDetail.lat)) + " " }}
							<text>{{ shipDetail.seaArea ? shipDetail.seaArea : "" }}</text></text
						> -->
							<text v-else> 当前位置：暂无 </text></el-col
						>
						<el-col class="top-item" :span="12">
							<WeatherBrief :data="shipDetail?.weather" />
						</el-col>
						<el-col v-if="hasAuth('电子证书')" class="top-item" :span="12">
							<ShipItem_ECert />
						</el-col>
						<el-col class="top-item" :span="12">
							<text> 24H航行油耗：{{ shipDetail?.dailyOil ? shipDetail?.dailyOil + "t" : "暂无" }} </text>
						</el-col>
					</el-row>
				</div>
			</div>
			<!-- 底部区域 -->
			<div v-if="selectedShipUuid !== undefined && activeTab" class="bottom-area">
				<div class="scroll-tab">
					<el-tabs v-model="activeTab" style="flex: 1; overflow: hidden">
						<el-tab-pane v-if="hasAuth('调度信息')" label="调度信息" name="调度信息"></el-tab-pane>
						<el-tab-pane v-if="hasAuth('船舶信息')" label="船舶信息" name="船舶信息"></el-tab-pane>
						<el-tab-pane v-if="hasAuth('在船船员')" label="在船船员" name="在船船员"></el-tab-pane>
						<el-tab-pane v-if="hasAuth('设备信息')" label="设备信息" name="设备信息"></el-tab-pane>
						<el-tab-pane v-if="hasAuth('证书保险')" label="证书保险" name="证书保险"></el-tab-pane>
						<el-tab-pane v-if="hasAuth('维护记录')" label="维护记录" name="维护记录"></el-tab-pane>
						<el-tab-pane v-if="hasAuth('备件记录')" label="备件记录" name="备件记录"></el-tab-pane>
						<el-tab-pane v-if="hasAuth('附件资料')" label="附件资料" name="附件资料"></el-tab-pane>
						<el-tab-pane v-if="hasAuth('变更记录')" label="变更记录" name="变更记录"></el-tab-pane>
						<el-tab-pane
							v-if="hasAuth('体系台账') && TAB_SHIP_NAMES.includes(shipDetail.name)"
							label="体系台账"
							name="体系台账"
						></el-tab-pane>
					</el-tabs>
					<div class="btn-group">
						<el-button
							v-if="hasAuth('船舶信息更新') && activeTab === '船舶信息'"
							@click="detailInfoRef?.editShipInfo"
							type="primary"
							size="small"
							><el-icon class="el-icon--left"><Edit /></el-icon>编辑信息</el-button
						>
						<el-button
							v-if="hasAuth('调度信息') && activeTab === '调度信息'"
							@click="dispatchInfoRef?.editDispatchInfo()"
							type="primary"
							size="small"
							><el-icon class="el-icon--left"><Edit /></el-icon>发起调度</el-button
						>

						<el-button
							:underline="false"
							v-if="hasAuth('维护记录新建') && activeTab === '维护记录'"
							@click="repairRef?.handleBtnNewShipRepair"
							type="primary"
							size="small"
							><el-icon class="el-icon--left"><Plus /></el-icon>添加记录</el-button
						>
						<el-button
							:underline="false"
							v-if="hasAuth('管理附件') && activeTab === '附件资料'"
							@click="attachmentsRef.edit()"
							type="primary"
							size="small"
							><el-icon class="el-icon--left"><Setting /></el-icon>管理附件</el-button
						>
						<el-button
							:underline="false"
							v-if="hasAuth('备件记录新建') && activeTab === '备件记录'"
							@click="materialRef?.handleBtnNewShipMaterial"
							type="primary"
							size="small"
							><el-icon class="el-icon--left"><Plus /></el-icon>添加记录</el-button
						>
						<el-button
							:underline="false"
							v-if="hasAuth('上船') && activeTab === '在船船员'"
							@click="sailorRef?.handleBtnSailorInfo()"
							size="small"
							><el-icon class="el-icon--left"><Refresh /></el-icon>同步船员信息</el-button
						>
						<el-button
							:underline="false"
							v-if="hasAuth('上船') && activeTab === '在船船员'"
							@click="sailorRef?.handleBtnBatchOnBoard()"
							type="primary"
							size="small"
							><el-icon class="el-icon--left"><Ship /></el-icon>批量上船</el-button
						>

						<!-- <el-button>//TODO导出</el-button> -->
					</div>
				</div>

				<transition name="fade" mode="out-in">
					<div style="height: 100%" v-if="hasAuth('船舶信息') && activeTab === '船舶信息'">
						<DetailShipInfoVue @on-edit-info="handleEditedInfo" ref="detailInfoRef" />
					</div>
					<div style="height: 100%" v-else-if="hasAuth('在船船员') && activeTab === '在船船员'">
						<DetailMemberListVue ref="sailorRef" />
					</div>
					<div style="height: 100%" v-else-if="hasAuth('设备信息') && activeTab === '设备信息'">
						<DetailDeviceMgrVue ref="deviceRef" mode="add" />
					</div>
					<div style="height: 100%" v-else-if="activeTab === '调度信息'">
						<div style="height: 100%" v-auth="'调度信息'">
							<DetialDispatchMgrVue :ship-ais-status="shipAisStatus" @need-refresh="handleEditedInfo" ref="dispatchInfoRef" />
						</div>
					</div>
					<div style="height: 100%" v-else-if="hasAuth('证书保险') && activeTab === '证书保险'">
						<DetailInsuranceCertMgrVue @need-refresh="handleEditedInfo" />
					</div>
					<div style="height: calc(100% - 54px)" v-else-if="hasAuth('维护记录') && activeTab === '维护记录'">
						<DetailRepairMgr ref="repairRef" :is-in-ship-detail="true" />
					</div>
					<div style="height: calc(100% - 54px)" v-else-if="hasAuth('备件记录') && activeTab === '备件记录'">
						<DetailMaterialMgr ref="materialRef" :is-in-ship-detail="true" />
					</div>
					<div style="height: 100%" v-else-if="hasAuth('附件资料') && activeTab === '附件资料'">
						<DetailAttachmentsMgr ref="attachmentsRef" @on-refresh="getShipDetail" :is-in-ship-detail="true" />
					</div>
					<div style="height: calc(100% - 45px)" v-else-if="hasAuth('变更记录') && activeTab === '变更记录'">
						<ChangeRecordMgr ref="changeRecordRef" :is-in-ship-detail="true" />
					</div>
					<div style="height: calc(100% - 45px)" v-else-if="hasAuth('体系台账') && activeTab === '体系台账'">
						<SystemRecordMgr ref="systemRecordRef" :is-in-ship-detail="true" />
					</div>
				</transition>
			</div>
			<template v-if="!activeTab"> </template>
		</template>
	</div>
</template>

<script setup lang="ts">
import { inject, Ref, ref, onMounted, provide, defineAsyncComponent, onBeforeUnmount, computed, watch, onActivated } from "vue";
import { ShipService } from "@/api/modules/ship";
import { ShipOptions } from "@/api/interface/ship";
import ShipItem_StatusVue from "../components/ShipItem_Status.vue";
import ShipItem_StatusIconVue from "../components/ShipItem_StatusIcon.vue";
import ShipItem_AvatarVue from "../components/ShipItem_Avatar.vue";
import ShipItem_WarningInDetailVue from "../components/ShipItem_WarningInDetail.vue";
import DetialDispatchMgrVue from "./dispatch-mgr/index.vue";
// const DetialDispatchMgrVue = defineAsyncComponent(() => import("./dispatch-mgr/index.vue"));
import WeatherBrief from "../../../../components/Windy/components/WeatherBrief.vue";
import { hasAuth, LatlngCnvTool } from "@/utils/util";
import { format, parseISO } from "date-fns";
import ShipItem_ECert from "../components/ShipItem_ECert.vue";
import ToolTipOver from "../components/ToolTipOver.vue";
import { TAB_SHIP_NAMES } from "@/views/system-module/approval-record/constants";

// import DetailInsuranceCertMgrVue from "./insurance-cert-mgr/index.vue";
const DetailInsuranceCertMgrVue = defineAsyncComponent(() => import("./insurance-cert-mgr/index.vue"));
// import DetailRepairMgr from "./repair-mgr/index.vue";
const DetailRepairMgr = defineAsyncComponent(() => import("./repair-mgr/index.vue"));
// import DetailShipInfoVue from "./ship-detail/index.vue";
const DetailShipInfoVue = defineAsyncComponent(() => import("./ship-detail/index.vue"));
// import DetailMemberListVue from "./member-mgr/index.vue";
const DetailMemberListVue = defineAsyncComponent(() => import("./member-mgr/index.vue"));
// import DetailDeviceMgrVue from "./device-mgr/index.vue";
const DetailDeviceMgrVue = defineAsyncComponent(() => import("./device-mgr/index.vue"));
// import DetailMaterialMgr from "./material-mgr/index.vue";
const DetailMaterialMgr = defineAsyncComponent(() => import("./material-mgr/index.vue"));
// import DetailAttachmentsMgr from "./attachments-mgr/index.vue";
const DetailAttachmentsMgr = defineAsyncComponent(() => import("./attachments-mgr/index.vue"));
// import ChangeRecordMgr from "./change-record-mgr/index.vue";
const ChangeRecordMgr = defineAsyncComponent(() => import("./change-record-mgr/index.vue"));
const SystemRecordMgr = defineAsyncComponent(() => import("./system-record-mgr/index.vue"));

const selectedShipUuid = inject("selectedShipUuid") as Ref<string | undefined>;
const shipDetail = ref({} as ShipOptions.ResBriefInfo);
const repairRef = ref();
const sailorRef = ref();
const materialRef = ref();
const deviceRef = ref();
const attachmentsRef = ref();
const changeRecordRef = ref();
const systemRecordRef = ref();
provide("shipDetail", shipDetail);
/**船舶ais信息情况，由brief获取，传入dispatch分页 */
let shipAisStatus = ref<{ [key: string]: any }>({});

const shipList = inject("shipList") as Ref<any[]>;
const detailInfoRef = ref();
const dispatchInfoRef = ref();

const props = defineProps({
	selectedTabs: { type: String }
});

const emits = defineEmits(["update:selectedTabs"]);

watch(
	() => props.selectedTabs,
	value => {
		console.log(value);
	},
	{ deep: true }
);

const _selectedTabs = computed({
	get() {
		return props.selectedTabs;
	},
	set(value) {
		emits("update:selectedTabs", value);
	}
});

/**获取船舶详情 */
async function getShipDetail(needRefreshList: boolean = false) {
	if (!selectedShipUuid.value) return;
	let { data } = await ShipService.getBriefInfo({ uuid: selectedShipUuid.value! });
	if (!data) return;
	shipDetail.value = data;
	shipAisStatus.value.mmsi = data.mmsi;
	shipAisStatus.value.lat = data.lat;
	if (needRefreshList) {
		refreshShipInList(
			shipList.value.find(s => s.uuid === data!.uuid!),
			data
		);
	}
}
/**刷新列表中船舶数据 */
function refreshShipInList(targetShip: any, detailData: any) {
	if (!targetShip) return;
	targetShip.name = detailData.name;
	targetShip.status = detailData.status;
	targetShip.img = detailData.img;
	targetShip.principalName = detailData.principalName;
	targetShip.certValidTime = detailData.certValidTime;
	targetShip.insuranceInvalidTime = detailData.insuranceInvalidTime;
}

/**清空上一个所选遗留的mmsi和位置信息 */
// onUnmounted(() => {
// 	shipAisStatus.value = {};
// }),

/**定位到上一个页面选中的导航栏 */
function initTabs() {
	activeTab.value = _selectedTabs.value || setDefaultTabsByAuth();
}

function saveSelectedTabs() {
	_selectedTabs.value = activeTab.value;
}

onMounted(() => {
	getShipDetail();
	initTabs();
});

onActivated(() => {
	initTabs();
});

// 在离开组件时 保留当前选中导航栏值
onBeforeUnmount(() => {
	saveSelectedTabs();
});

async function handleEditedInfo() {
	await getShipDetail(true);
	// 更新左侧列表当前船舶的预警信息
	const target = shipList.value.find(e => e.uuid === shipDetail.value.uuid);
	if (target) {
		target.certValidTime = shipDetail.value.certValidTime;
		target.registerValidTimes = shipDetail.value.registerValidTimes;
		target.insuranceInvalidTime = shipDetail.value.insuranceInvalidTime;
	}
}

const activeTab = ref(setDefaultTabsByAuth());

// 根据权限设置tabs默认值
function setDefaultTabsByAuth() {
	if (hasAuth("调度信息")) {
		return "调度信息";
	} else if (hasAuth("船舶信息")) {
		return "船舶信息";
	} else if (hasAuth("在船船员")) {
		return "在船船员";
	} else if (hasAuth("设备信息")) {
		return "设备信息";
	} else if (hasAuth("证书保险")) {
		return "证书保险";
	} else if (hasAuth("维护记录")) {
		return "维护记录";
	} else if (hasAuth("备件记录")) {
		return "备件记录";
	} else if (hasAuth("附件资料")) {
		return "附件资料";
	} else if (hasAuth("变更记录")) {
		return "变更记录";
	} else {
		return "";
	}
}
</script>

<style scoped lang="scss">
.ship-info-container {
	height: calc(100% - 54px);
	padding: 0 12px;
	overflow: overlay;
}
.empty-img {
	position: absolute;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
	display: flex;
	flex-direction: column;
	align-items: center;
	width: 500px;
	height: 380px;
	margin: auto;
	img {
		width: 100%;
	}
}
.top-area {
	display: flex;
	padding-bottom: 8px;

	// border-bottom: $border-color 1px solid;
	.top-right {
		flex-grow: 1;
		margin-left: 20px;
		.ship-title {
			height: 20px;
			font-size: 20px;
			font-weight: bold;
			line-height: 20px;
			color: $primary-text-color;
		}
		.title-item + .title-item {
			margin-left: 16px;
		}
	}
	.top-info-area {
		margin-top: 16px;
	}
	.top-item {
		height: 20px;
		margin: 4px 0;
		font-size: 14px;
		line-height: 20px;
		color: $sub-text-color;
	}
}
.bottom-area {
	position: relative;
	flex-grow: 1;
	overflow: hidden;
	.btn-group {
		width: fit-content;
		margin-top: 8px;
	}
}
:deep(.el-tabs__nav-wrap::after) {
	background-color: transparent;
}
.scroll-tab {
	display: flex;
	height: 40px;
	margin-bottom: 14px;
	border-bottom: 1px solid var(--el-border-color-light);
}
</style>
