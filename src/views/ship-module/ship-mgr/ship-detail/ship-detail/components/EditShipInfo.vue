<!--编辑船舶信息-->
<template>
	<el-drawer
		ref="dialog"
		title="编辑船舶基本信息"
		v-model="_showShipInfo"
		size="40%"
		@open="iniData"
		:before-close="confirmClose"
		@closed="handleClosed"
	>
		<MetaForm
			ref="form"
			mode="edit"
			:formConfig="formConfig"
			:styleConfig="{ mode: 'drawer', labelWidth: '140px' }"
			:rule-form="ruleForm"
		>
		</MetaForm>
	</el-drawer>
</template>

<script setup lang="ts">
import MetaForm from "@/meta-components/MetaForm/index.vue";
import { FormConfigOptions, FormItemOptions } from "@/meta-components/MetaForm/interface";
import { reactive, ref, computed } from "vue";
import { ShipOptions } from "@/api/interface/ship";
import { ShipService } from "@/api/modules/ship";
import { getDictPairList, StorageService } from "@/api/modules/power";
// import { UploadService } from "@/api/modules/upload";
import countryList from "@/assets/json/country.json";
import { MemberService } from "@/api/modules/member";
import { GlobalStore } from "@/store";
import { useDialog } from "@/meta-components/MetaHooks/useDialog";

const globalStore = GlobalStore();

let ruleForm = reactive({
	name: [{ required: true, message: "请输入船舶名称" }],
	shipType: [{ required: true, message: "请选择船舶类型" }],
	principalId: [{ required: true, message: "请选择负责人" }]
});

let props = defineProps({
	showShipInfo: { type: Boolean, required: true },
	infoData: { type: Object, required: true }
});
const emits = defineEmits(["update:showShipInfo", "onSuccess"]);

const _showShipInfo = computed({
	get() {
		return props.showShipInfo;
	},
	set(value) {
		emits("update:showShipInfo", value);
	}
});
const formContent: Array<FormItemOptions> = [
	{ label: "基本信息", type: "title", name: "" },
	{
		label: "船名（中文）",
		type: "input",
		name: "name",
		otherAttr: { max: 14 },
		span: 12,
		placeholder: "请输入"
	},
	{
		label: "船舶类型",
		type: "cascader",
		name: "shipType",
		span: 12,
		api: () => {
			return StorageService.getDetail({ key: "ShipType" });
		},
		otherAttr: {
			labelKey: "name",
			listKey: "value",
			props: { emitPath: false } //只返回级联最后一级的string，而非数组
		},
		placeholder: "请选择"
	},
	{
		label: "船舶类型说明",
		type: "input",
		name: "shipTypeDescription",
		span: 24,
		placeholder: "请输入",
		otherAttr: { max: 50 }
	},
	{
		label: "负责人",
		type: "singleSelect",
		name: "principalId",
		span: 12,
		api: MemberService.getSimpleList,
		otherAttr: {
			remote: true,
			labelKey: "name",
			valueKey: "id",
			subLabelKey: "contactMobile",
			remoteLabelKey: "principalName",
			appendLabelToFormDataWithKey: "_principalName"
		},
		hint: "负责人将获得移动端船只管理、汇报等权限",
		placeholder: "输入员工名称进行搜索"
	},
	// {
	// 	label: "船舶图片",
	// 	type: "imageUpload",
	// 	name: "img",
	// 	hint: "请上传jpg/png文件，建议比例为3:2。",
	// 	span: 24,
	// 	api: UploadService.ossApi,
	// 	imageOptions: {
	// 		mode: "single",
	// 		limitNum: 1,
	// 		width: 270
	// 	}
	// },
	{
		//自动英文名
		label: "船名（英文）",
		type: "input",
		name: "engName",
		otherAttr: { max: 42 },
		span: 12,
		placeholder: "请输入"
	},
	{
		label: "曾用名",
		type: "input",
		name: "historyName",
		otherAttr: { max: 42 },
		span: 12,
		placeholder: "请输入"
	},
	{
		label: "船级",
		type: "singleSelect",
		name: "registerType",
		span: 12,
		api: () => {
			return getDictPairList({ dictCode: "RegisterType" });
		},
		otherAttr: {
			labelKey: "name"
		},
		placeholder: "请选择"
	},
	{
		label: "船检登记号",
		type: "input",
		name: "registerNo",
		span: 12,
		otherAttr: {
			max: 12
		},
		placeholder: "请输入"
	},
	{
		label: "船舶登记号",
		type: "input",
		name: "shipNo",
		span: 12,
		otherAttr: {
			max: 20
		},
		placeholder: "请输入"
	},
	{
		label: "船舶识别号",
		type: "input",
		name: "discernNo",
		span: 12,
		otherAttr: {
			max: 13
		},
		placeholder: "请输入"
	},
	{
		label: "MMSI码",
		type: "input",
		name: "mmsi",
		span: 12,
		otherAttr: {
			max: 9,
			inputOnlyNumber: true
		},
		placeholder: "请输入"
	},
	{
		label: "船旗国",
		type: "singleSelect",
		name: "nation",
		span: 12,
		staticOptions: countryList,
		placeholder: "请选择"
	},
	{
		label: "船籍港",
		type: "singleSelect",
		name: "harbor",
		span: 12,
		api: () => {
			return getDictPairList({ dictCode: "Harbor" });
		},
		otherAttr: {
			labelKey: "name"
		},
		placeholder: "请选择"
	},
	{
		label: "船舶所有人",
		type: "input",
		name: "owner",
		span: 12,
		otherAttr: {
			max: 50
		},
		placeholder: "请输入"
	},
	{
		label: "船舶呼号",
		type: "input",
		name: "callSign",
		span: 12,
		otherAttr: {
			max: 5
		},
		placeholder: "请输入"
	},
	{
		label: "IMO No.",
		type: "input",
		name: "imoNo",
		span: 12,
		otherAttr: {
			max: 7
		},
		placeholder: "请输入"
	},
	{ type: "divider", name: "" },
	{ type: "title", label: "建造信息", name: "" },
	{
		type: "date",
		label: "龙骨安放时间",
		name: "keelLayTime",
		placeholder: "请选择",
		span: 12
	},
	{ type: "date", label: "建造完工日期", name: "buildTime", placeholder: "请选择", span: 12 },
	{
		type: "input",
		label: "船舶建造厂",
		name: "buildFactory",
		placeholder: "请输入",
		otherAttr: { max: 100 },
		span: 12
	},
	{ type: "text", label: "", name: "", placeholder: "", span: 12 },
	{
		type: "radio",
		label: "是否改建",
		name: "isReBuild",
		staticOptions: [
			{ label: "否", value: 2 },
			{ label: "是", value: 1 }
		],
		span: 24
	},
	{
		type: "date",
		label: "改建日期",
		name: "reBuildTime",
		placeholder: "请选择",
		span: 12,
		visible: (data: any) => {
			return data && data!.isReBuild === 1;
		}
	},
	{
		type: "input",
		label: "船舶改建厂",
		name: "reBuildFactory",
		placeholder: "请输入",
		otherAttr: { max: 100 },
		span: 12,
		visible: (data: any) => {
			return data && data!.isReBuild === 1;
		}
	},
	// {
	// 	type: "text",
	// 	label: "",
	// 	name: "",
	// 	placeholder: "",
	// 	span: 12,
	// 	visible: data => {
	// 		return data!.isReBuild === 1;
	// 	}
	// },
	{
		type: "textarea",
		label: "改建情况",
		name: "reBuildDetail",
		placeholder: "请输入",
		otherAttr: { max: 200 },
		span: 12,
		visible: (data: any) => {
			return data && data!.isReBuild === 1;
		}
	},
	{ type: "divider", name: "" },
	{ type: "title", label: "参数信息", name: "" },
	{
		label: "船舶动力",
		type: "radio",
		name: "isSelfSail",
		staticOptions: [
			{
				label: "自航",
				value: 1
			},
			{
				label: "非自航",
				value: 2
			}
		]
	},
	{
		type: "input",
		label: "总吨位",
		name: "totalTon",
		placeholder: "请输入",
		otherAttr: { showWordLimit: false, append: "t", inputOnlyNumber: true, inputOnlyNonnegativeNumber: true, precision: 3 },
		span: 8
	},
	{
		type: "input",
		label: "净吨位",
		name: "netTon",
		placeholder: "请输入",
		otherAttr: { showWordLimit: false, append: "t", inputOnlyNumber: true, inputOnlyNonnegativeNumber: true, precision: 3 },
		span: 8
	},
	{
		type: "input",
		label: "载重吨",
		name: "loadTon",
		placeholder: "请输入",
		otherAttr: { showWordLimit: false, append: "t", inputOnlyNumber: true, inputOnlyNonnegativeNumber: true, precision: 3 },
		span: 8
	},
	{
		type: "input",
		label: "船舶总长",
		name: "totalLen",
		placeholder: "请输入",
		otherAttr: { showWordLimit: false, append: "m", inputOnlyNumber: true, inputOnlyNonnegativeNumber: true, precision: 2 },
		span: 8
	},
	{
		type: "input",
		label: "型宽/船宽",
		name: "width",
		placeholder: "请输入",
		otherAttr: { showWordLimit: false, append: "m", inputOnlyNumber: true, inputOnlyNonnegativeNumber: true, precision: 2 },
		span: 8
	},
	{
		type: "input",
		label: "型深",
		name: "depth",
		placeholder: "请输入",
		otherAttr: { showWordLimit: false, append: "m", inputOnlyNumber: true, inputOnlyNonnegativeNumber: true, precision: 2 },
		span: 8
	},
	{
		type: "input",
		label: "满载水线长",
		name: "fullWaterlineLength",
		placeholder: "请输入",
		otherAttr: { showWordLimit: false, append: "m", inputOnlyNumber: true, inputOnlyNonnegativeNumber: true, precision: 2 },
		span: 8
	},
	{
		type: "input",
		label: "空载吃水",
		name: "emptyDraught",
		placeholder: "请输入",
		otherAttr: { showWordLimit: false, append: "m", inputOnlyNumber: true, inputOnlyNonnegativeNumber: true, precision: 2 },
		span: 8
	},
	{
		type: "input",
		label: "满载吃水",
		name: "fullDraught",
		placeholder: "请输入",
		otherAttr: { showWordLimit: false, append: "m", inputOnlyNumber: true, inputOnlyNonnegativeNumber: true, precision: 2 },
		span: 8
	},
	{
		type: "input",
		label: "空载排水量",
		name: "emptyDrain",
		placeholder: "请输入",
		otherAttr: { showWordLimit: false, append: "t", inputOnlyNumber: true, inputOnlyNonnegativeNumber: true, precision: 3 },
		span: 8
	},
	{
		type: "input",
		label: "满载排水量",
		name: "fullDrain",
		placeholder: "请输入",
		otherAttr: { showWordLimit: false, append: "t", inputOnlyNumber: true, inputOnlyNonnegativeNumber: true, precision: 3 },
		span: 8
	},
	{
		type: "input",
		label: "参考载货量",
		name: "referCargo",
		placeholder: "请输入",
		otherAttr: { showWordLimit: false, append: "t", inputOnlyNumber: true, inputOnlyNonnegativeNumber: true, precision: 3 },
		span: 8
	},
	{
		type: "input",
		label: "24H航行油耗",
		name: "dailyOil",
		placeholder: "请输入",
		otherAttr: { showWordLimit: false, append: "t", inputOnlyNumber: true, inputOnlyNonnegativeNumber: true, precision: 3 },
		span: 8
	},
	{
		type: "input",
		label: "起重吨位",
		name: "liftingTon",
		visible: (data: any) => {
			return data && data!.shipType && data!.shipType.includes("起重船");
		},
		placeholder: "请输入",
		otherAttr: { showWordLimit: false, append: "t", inputOnlyNumber: true, inputOnlyNonnegativeNumber: true, precision: 3 },
		span: 8
	},
	{
		type: "input",
		label: "限制水面高度",
		name: "waterSurfaceHeight",
		visible: (data: any) => {
			return data && data!.shipType && data!.shipType.includes("起重船");
		},
		placeholder: "请输入",
		otherAttr: { showWordLimit: false, append: "m", inputOnlyNumber: true, inputOnlyNonnegativeNumber: true, precision: 2 },
		span: 8
	},
	{
		label: "航区",
		type: "singleSelect",
		name: "navigationArea",
		staticOptions: [
			{
				label: "沿海航区",
				value: "沿海航区"
			},
			{
				label: "近海航区",
				value: "近海航区"
			},
			{
				label: "远海航区",
				value: "远海航区"
			},
			{
				label: "遮蔽航区",
				value: "遮蔽航区"
			}
		],
		span: 24
	},
	{
		label: "营运海区",
		type: "multiSelect",
		name: "operatingSeaArea",
		staticOptions: [
			{
				label: "A1",
				value: "A1"
			},
			{
				label: "A2",
				value: "A2"
			},
			{
				label: "A3",
				value: "A3"
			},
			{
				label: "A4",
				value: "A4"
			}
		],
		otherAttr: {
			multiple: true,
			collapseTags: true
		},
		span: 24
	},
	{
		type: "input",
		label: "船体材料",
		name: "hullMaterial",
		placeholder: "请输入",
		otherAttr: {
			max: 20
		},
		span: 8
	},
	{
		type: "input",
		label: "甲板材料",
		name: "deckMaterial",
		placeholder: "请输入",
		otherAttr: {
			max: 20
		},
		span: 8
	},
	{
		type: "input",
		label: "甲板层数",
		name: "deckLayers",
		placeholder: "请输入",
		otherAttr: {
			inputOnlyNumber: true,
			showWordLimit: false
		},
		span: 8
	},
	{
		type: "input",
		label: "水密横舱壁数",
		name: "transverseBulkheads",
		placeholder: "请输入",
		otherAttr: {
			inputOnlyNumber: true,
			showWordLimit: false
		},
		span: 8
	},
	{
		type: "input",
		label: "双层底位置",
		name: "doubleBottom",
		placeholder: "请输入",
		otherAttr: {
			max: 20
		},
		span: 8
	},
	{
		type: "input",
		label: "结构型式",
		name: "structureType",
		placeholder: "请输入",
		otherAttr: {
			max: 20
		},
		span: 8
	},
	{
		type: "input",
		label: "补充加强结构",
		name: "strengtheningStructure",
		placeholder: "请输入",
		otherAttr: {
			max: 20
		},
		span: 8
	},
	{
		type: "input",
		label: "货舱数量",
		name: "cargoNum",
		placeholder: "请输入",
		otherAttr: {
			max: 20
		},
		span: 8
	},
	{
		type: "input",
		label: "货舱盖型式",
		name: "hatchCoverType",
		placeholder: "请输入",
		otherAttr: {
			max: 20
		},
		span: 8
	}
];

const formConfig: FormConfigOptions = {
	key: "edit-ship-info",
	isSaveDraft: true,
	items: formContent,
	successCallBack: handleSuccess,
	detailApi: ShipService.getInfo,
	detailKey: "uuid",
	editApi: ShipService.editInfo,
	closeFunction: close,
	closeFormAt: "both",
	changeLogApi: (log: string) => {
		return ShipService.addShipChangeRecord({
			type: ShipOptions.ShipChangeRecordTypeEnum.SHIPINFO,
			content: log,
			shipId: props.infoData.id
		});
	},
	dataCallBack: (data: any) => {
		data.deckLayers && (data.deckLayers = Number(data.deckLayers));
		data.transverseBulkheads && (data.transverseBulkheads = Number(data.transverseBulkheads));
		data.operatingSeaArea && (data.operatingSeaArea = data.operatingSeaArea.split("+"));
		return data;
	},
	beforeSubmit: (data: any) => {
		data.deckLayers && (data.deckLayers = data.deckLayers.toString());
		data.transverseBulkheads && (data.transverseBulkheads = data.transverseBulkheads.toString());
		data.operatingSeaArea && (data.operatingSeaArea = data.operatingSeaArea.join("+"));
		return data;
	}
};

//手动调用metaForm的初始化
let form = ref();

const { confirmClose } = useDialog(form);

function iniData() {
	form.value.iniForm("edit", props.infoData, { corpId: globalStore.userInfo.corpId }, {
		isReBuild: 2
	} as ShipOptions.ShipDetail);
}

function handleSuccess() {
	emits("onSuccess");
}

/**关闭弹窗，通知父组件 */
function close() {
	_showShipInfo.value = false;
}

function handleClosed() {
	close();
}

// /**校验表单并尝试进入下一步 */
// async function tryToNext() {
// 	let targetData: { [key: string]: any } = form.value.formData;
// 	for (let key in targetData) {
// 		shipFormData.value[key] = targetData[key];
// 	}
// 	return await form.value.doValidate();
// }

// onMounted(() => {
// 	form.value.iniForm("add", {}, { corpId: globalStore.userInfo.corpId }, {
// 		isReBuild: 2,
// 		pushDevices: [],
// 		powerDevices: [],
// 		oilCabinets: [],
// 		certs: [],
// 		insurances: []
// 	} as ShipOptions.ShipDetail);
// });
</script>

<style scoped></style>
