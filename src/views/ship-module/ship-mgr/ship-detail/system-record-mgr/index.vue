<!-- 台账列表 -->
<template>
	<div style="height: 100%">
		<TableLayout ref="LedgerTable" title="体系台账" :table-config="tableConfig" :filter-config="filterConfig">
			<template #depart="{ row }">
				{{ row.depart && row.depart.length > 0 ? row.depart.map((i:any) => i.name).join("、") : "-" }}
			</template>
			<template #corpUserName="{ row }">
				{{(row.position?.length ? `${row.position.map((i: any) => i.name).join("、")}：` : "") + (row.corpUserName || "-")}}
			</template>
			<template #opr="{ row }">
				<el-link
					v-if="hasAuth('台账详情')"
					style="margin-right: 12px"
					:underline="false"
					type="primary"
					@click="jumpTo('detail', row)"
					>查看详情</el-link
				>
				<div v-if="hasAuth('台账删除')">
					<OprDelete @on-delete="handleBtnDelete(row)"></OprDelete>
				</div>
			</template>
		</TableLayout>
	</div>
</template>

<script setup lang="ts" name="systemApprovalTable">
import { ref, reactive, onMounted, inject, Ref } from "vue";
import TableLayout from "@/meta-components/MetaTable/index.vue";
import { ElMessage } from "element-plus";
import { ColumnProps, FilterConfigOptions, TableConfig } from "@/meta-components/MetaTable/interface";
import OprDelete from "@/meta-components/MetaTableComponents/OprDelete.vue";

import { LedgerService } from "@/api/modules/system";
import { hasAuth } from "@/utils/util";
import { useTableMemory } from "@/meta-components/MetaHooks/usePageMemory";

import { useTableScrollMemory } from "@/hooks/useTableScrollMemory";
import router from "@/routers";

import { ShipOptions } from "@/api/interface/ship";

let LedgerTable = ref();
const shipDetail = inject("shipDetail") as Ref<ShipOptions.ResBriefInfo>;
let staticParam = shipDetail?.value?.id ? { shipId: shipDetail.value.id } : {};

/**筛选框配置数量 */
let filterConfig: Array<FilterConfigOptions> = reactive([
	// { name: "typeName", type: "input", span: 4, placeholder: "搜索流程名称" },
	{ name: "departName", type: "input", span: 4, placeholder: "搜索所属部门" },
	{ name: "corpUserName", type: "input", span: 4, placeholder: "搜索创建人" },
	{
		name: "completedTime",
		type: "dateRange",
		span: 6,
		placeholder: "创建时间"
	}
]);

let columnsConfig: Array<ColumnProps> = [
	{
		label: "序号",
		type: "index"
	},
	{
		label: "流程名称",
		prop: "typeName"
	},
	{
		label: "关键词",
		prop: "keyword"
	},
	{
		label: "时间标签",
		prop: "timeLabel"
	},
	{
		label: "流程所属部门",
		slotName: "depart"
	},
	{
		label: "创建人",
		slotName: "corpUserName"
	},
	{
		label: "创建时间",
		prop: "completedTime",
		width: 160,
		sortable: true,
		sortAttr: "completedTime"
	},
	{
		label: "操作",
		slotName: "opr",
		fixed: "right"
	}
];

let tableConfig: TableConfig = reactive({
	key: "system-ship-ledger-mgr",
	columns: columnsConfig,
	requestApi: LedgerService.getList,
	selectType: "none",
	pagination: true,
	staticParam,
	defaultExpandAll: false,
	showTitleArea: false
});
useTableMemory(LedgerTable, tableConfig);

//页面跳转
function jumpTo(mode: "detail", rowData?: any) {
	router.push({ name: "ApplyDetail", query: { uuid: rowData?.uuid }, params: { mode } });
}
//删除
async function handleBtnDelete(rowData: { [key: string]: any }) {
	let { code } = await LedgerService.delete({ uuid: rowData.uuid });
	if (code !== 0) return;
	ElMessage.success("删除成功");
	refresh();
}
/**刷新 */
function refresh() {
	// emits("onRefresh");
	LedgerTable.value.search();
}

onMounted(() => {
	useTableScrollMemory(LedgerTable, "SystemShipLedgerMgr");
});
</script>

<style scoped></style>
