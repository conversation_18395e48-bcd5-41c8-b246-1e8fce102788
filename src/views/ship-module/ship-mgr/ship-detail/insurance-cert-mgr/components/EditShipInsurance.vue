<!--船舶保险添加/编辑/详情-->
<template>
	<el-drawer
		ref="dialog"
		:title="mode === 'add' ? '添加船舶保险' : mode === 'edit' ? '编辑船舶保险' : '船舶保险详情'"
		v-model="_showEditShipInsurance"
		size="30%"
		destroy-on-close
		@open="iniData"
		:before-close="confirmClose"
		@closed="handleClosed"
		draggable
	>
		<MetaForm
			v-if="mode === 'add' || mode === 'edit'"
			:mode="mode"
			ref="form"
			:formConfig="formConfig"
			:styleConfig="{ mode: 'drawer', labelWidth: '100px' }"
			:rule-form="ruleForm"
		>
		</MetaForm>
		<MetaDetail ref="insDetailRef" v-if="mode === 'detail'" :detail-config="detailConfig">
			<template #attachs="scope: any">
				<div v-if="scope.detailData.attachs && scope.detailData.attachs.length">
					<el-row style="width: 100%">
						<el-col style="margin-top: 12px" :span="24" v-for="attach in scope.detailData.attachs" :key="attach.url">
							<div style="width: 100%">
								<FileItem :file-name="attach.name" :file-url="attach.url" />
							</div>
						</el-col>
					</el-row>
				</div>
				<div v-else>无附件</div>
			</template>
		</MetaDetail>
	</el-drawer>
</template>

<script setup lang="ts" name="EditShipInsurance">
//LATER OCR
import MetaForm from "@/meta-components/MetaForm/index.vue";
import MetaDetail from "@/meta-components/MetaDetail/index.vue";
import { PropType, ref, reactive, computed, inject, Ref } from "vue";
import { FormConfigOptions, FormItemOptions } from "@/meta-components/MetaForm/interface";
import { ShipOptions, InsuranceOptions } from "@/api/interface/ship";
import { InsuranceService, ShipService } from "@/api/modules/ship";
import { GlobalStore } from "@/store";
import { reformInpuNumber } from "@/meta-components/MetaUtils/FormUtils";
import { DetailConfigOptions, DetailItemOptions } from "@/meta-components/MetaDetail/interface";
import { UploadService } from "@/api/modules/upload";
import { isAfter, parseISO } from "date-fns";
import FileItem from "@/components/FileItem/index.vue";
import { useDialog } from "@/meta-components/MetaHooks/useDialog";

const globalStore = GlobalStore();
const shipDetail = inject("shipDetail") as Ref<ShipOptions.ResBriefInfo>;
const insDetailRef = ref();

let props = defineProps({
	mode: {
		type: String as PropType<"add" | "edit" | "detail">,
		requeired: true
	},
	showEditShipInsurance: { type: Boolean, required: true },
	editData: { type: Object, required: true },
	isInShipDetail: { type: Boolean, default: true }
});

let ruleForm = reactive({
	// no: [{ required: true, message: "请输入保单编号" }],
	name: [{ required: true, message: "请输入保险名称" }],
	invalidTime: [{ validator: checkInvalidTime, required: true, trigger: "change" }],
	shipId: [{ required: true, message: "请选择所属船舶" }]
	// validTime: [{ required: true, message: "请选择生效日期" }]
});

/**校验到期时间 */
function checkInvalidTime(rule: any, value: any, callback: any) {
	if (!form.value.formData.invalidTime) {
		callback(new Error("请选择有效截止日期"));
	} else {
		let timeBefore = form.value.formData.validTime;
		let timeAfter = form.value.formData.invalidTime;
		if (!timeAfter) {
			callback(new Error("请选择有效截止日期"));
		}
		if (isAfter(parseISO(timeBefore), parseISO(timeAfter))) {
			callback(new Error("有效截止日期不能早于生效日期"));
		}
		callback();
	}
}
const emits = defineEmits(["update:showEditShipInsurance", "onSuccess"]);

const _showEditShipInsurance = computed({
	get() {
		return props.showEditShipInsurance;
	},
	set(value) {
		emits("update:showEditShipInsurance", value);
	}
});

const formContent: Array<FormItemOptions> = [
	{ label: "基础信息", type: "title", name: "" },
	{
		label: "所属船舶",
		type: "singleSelect",
		api: ShipService.getAllSearchList,
		placeholder: "请选择所属船舶",
		name: "shipId",
		span: 24,
		otherAttr: {
			labelKey: "name",
			valueKey: "id"
		},
		visible: () => !props.isInShipDetail,
		disabled: () => props.mode === "edit"
	},
	{
		label: "保险名称",
		type: "input",
		placeholder: "请输入",
		name: "name",
		span: 24,
		otherAttr: {
			max: 50
		}
	},
	{
		label: "保单编号",
		type: "input",
		placeholder: "请输入",
		name: "no",
		span: 24,
		otherAttr: {
			max: 50
		}
	},
	{
		label: "保险周期",
		type: "input",
		name: "insuranceCycle",
		placeholder: "请输入",
		otherAttr: {
			inputOnlyNumber: true,
			showWordLimit: false,
			precision: 0,
			append: "年"
		}
	},
	{
		label: "生效日期",
		type: "date",
		placeholder: "请选择",
		name: "validTime",
		span: 24
	},
	{
		label: "有效期至",
		type: "date",
		placeholder: "请选择",
		name: "invalidTime",
		span: 24
	},
	{
		label: "保费",
		type: "input",
		placeholder: "请输入",
		name: "insurancePremium",
		span: 24,
		otherAttr: {
			inputOnlyNumber: true,
			showWordLimit: false,
			precision: 2,
			prepend: "¥"
		}
	},
	{
		label: "保额",
		type: "input",
		placeholder: "请输入",
		name: "insuranceAmount",
		span: 24,
		otherAttr: {
			inputOnlyNumber: true,
			showWordLimit: false,
			precision: 2,
			prepend: "¥"
		}
	},
	{
		label: "保险机构",
		type: "input",
		placeholder: "请输入",
		name: "insuranceAgency",
		otherAttr: {
			max: 20
		}
	},
	{
		label: "备注",
		type: "textarea",
		placeholder: "请输入",
		name: "remark",
		otherAttr: {
			max: 200
		}
	},
	{ label: "保险原件", type: "title", name: "" },
	{
		label: "上传附件",
		type: "fileUpload",
		name: "attachs",
		api: UploadService.ossApi,
		imageOptions: {
			pathName: "ship-insurance",
			mode: "multiple",
			limitNum: 100
		}
	}
];

let shipId = ref<number>(0);
const formConfig: FormConfigOptions = {
	key: "edit-shipInsurance",
	items: formContent,
	addApi: InsuranceService.add,
	editApi: InsuranceService.edit,
	beforeSubmit: data => {
		props.mode === "add" && (shipId.value = data.shipId);
		return reformInpuNumber(data, ["insuranceCycle"]);
	},
	dataCallBack: data => {
		shipId.value = data.shipId;
		return data;
	},
	successCallBack: handleSuccess,
	detailApi: InsuranceService.getDetail,
	detailKey: "uuid",
	closeFunction: close,
	closeFormAt: "both",
	hintMessage: ["创建船舶保险成功", "保存船舶保险成功"],
	changeLogApi: log => {
		return ShipService.addShipChangeRecord({
			type: ShipOptions.ShipChangeRecordTypeEnum.INS,
			content: log,
			shipId: shipDetail ? shipDetail.value.id : shipId.value
		});
	}
};

const detailContent: Array<DetailItemOptions> = [
	{ label: "基础信息", type: "title", name: "" },
	{
		label: "所属船舶",
		type: "detail",
		name: "shipName",
		span: 24
	},
	{
		label: "保险名称",
		type: "detail",
		name: "name",
		span: 24
	},
	{
		label: "保单编号",
		type: "detail",
		name: "no",
		span: 24
	},

	{
		label: "保险周期",
		type: "detail",
		name: "insuranceCycle",
		otherAttr: {
			append: "年"
		}
	},
	{
		label: "生效日期",
		type: "detail",
		name: "validTime",
		span: 24,
		otherAttr: {
			isDate: true
		}
	},
	{
		label: "有效期至",
		type: "detail",
		name: "invalidTime",
		span: 24,
		otherAttr: {
			isDate: true
		}
	},
	{
		label: "保费",
		type: "detail",
		name: "insurancePremium",
		span: 24,
		otherAttr: {
			precision: 2,
			prepend: "¥"
		}
	},
	{
		label: "保额",
		type: "detail",
		name: "insuranceAmount",
		span: 24,
		otherAttr: {
			precision: 2,
			prepend: "¥"
		}
	},
	{
		label: "保险机构",
		type: "detail",
		name: "insuranceAgency"
	},
	{
		label: "备注",
		type: "detail",
		name: "remark"
	},
	{ label: "保险原件", type: "title", name: "" },
	{ label: "", type: "slot", name: "attachs" }
];

function getDetailParam() {
	return { uuid: props.editData.uuid };
}
const detailConfig: DetailConfigOptions = {
	detailApi: InsuranceService.getDetail,
	detailParam: getDetailParam,
	labelPosition: "left",
	items: detailContent
};

//手动调用metaForm的初始化
let form = ref();

const { confirmClose } = useDialog(form);

function iniData() {
	if (props.mode === "detail") {
		insDetailRef.value.getDetailData();
		return;
	}
	//最初的shipId。船舶内页时锁定，综合证书页时由form提供
	let iniParam: InsuranceOptions.ReqAddParam = {
		corpId: globalStore.userInfo.corpId
	};
	shipDetail?.value?.id && (iniParam.shipId = shipDetail?.value?.id);

	form.value.iniForm(props.mode, props.editData, iniParam);
}

/**提交成功回调。多为关闭弹窗、提示成功 */
function handleSuccess() {
	emits("onSuccess");
}

/**本组件中的dialog关闭不代表父组件控制弹窗用的变量改为了false。需要手动emit一下 */
function handleClosed() {
	close();
}

/**关闭弹窗，通知父组件 */
function close() {
	_showEditShipInsurance.value = false;
}
</script>

<style scoped></style>
