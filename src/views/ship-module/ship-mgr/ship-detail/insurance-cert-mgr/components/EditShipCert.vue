<!--船舶证书添加/编辑/详情-->
<template>
	<el-drawer
		ref="dialog"
		:title="mode === 'add' ? '添加船舶证书' : mode === 'edit' ? '编辑船舶证书' : '船舶证书详情'"
		v-model="_showEditShipCert"
		size="30%"
		@open="iniData"
		:before-close="confirmClose"
		@closed="handleClosed"
		destroy-on-close
		draggable
	>
		<MetaForm
			v-if="mode === 'add' || mode === 'edit'"
			:mode="mode"
			ref="form"
			:formConfig="formConfig"
			:styleConfig="{ mode: 'drawer', labelWidth: '100px' }"
			:rule-form="ruleForm"
		>
		</MetaForm>
		<MetaDetail ref="certDetailRef" v-if="mode === 'detail'" :detail-config="detailConfig"> </MetaDetail>
	</el-drawer>
</template>

<script setup lang="ts" name="EditShipCert">
//LATER OCR
import MetaForm from "@/meta-components/MetaForm/index.vue";
import MetaDetail from "@/meta-components/MetaDetail/index.vue";
import { PropType, ref, reactive, computed, inject, Ref } from "vue";
import { FormConfigOptions, FormItemOptions } from "@/meta-components/MetaForm/interface";
import { CertOptions, ShipOptions } from "@/api/interface/ship";
import { CertService, ShipService } from "@/api/modules/ship";
import { GlobalStore } from "@/store";
import { reformInpuNumber } from "@/meta-components/MetaUtils/FormUtils";
import { DetailConfigOptions, DetailItemOptions } from "@/meta-components/MetaDetail/interface";
import { UploadService } from "@/api/modules/upload";
import { isAfter, parseISO } from "date-fns";
import { getCertAndInsRuleList } from "@/api/modules/power";
import { watch } from "vue";
import { useDialog } from "@/meta-components/MetaHooks/useDialog";

const globalStore = GlobalStore();
const shipDetail = inject("shipDetail") as Ref<ShipOptions.ResBriefInfo>;
const certDetailRef = ref();

let props = defineProps({
	mode: {
		type: String as PropType<"add" | "edit" | "detail">,
		requeired: true
	},
	showEditShipCert: { type: Boolean, required: true },
	editData: { type: Object, required: true },
	isInShipDetail: { type: Boolean, default: true },
	addShipId: { type: Number },
	addCertType: { type: String }
});

let ruleForm = reactive({
	// no: [{ required: true, message: "请输入证书编号" }],
	shipCertType: [{ required: true, message: "请输入证书类型" }],
	// signTime: [{ required: true, message: "请选择签发时间" }],
	validTime: [{ validator: checkValidTime, required: true, trigger: "change" }],
	shipId: [{ required: true, message: "请选择所属船舶" }]
});

/**校验到期时间 */
function checkValidTime(rule: any, value: any, callback: any) {
	if (form.value.formData.isTrainCert) {
		callback();
	} else {
		let timeBefore = form.value.formData.signTime;
		let timeAfter = form.value.formData.validTime;
		if (!timeAfter) {
			callback(new Error("请选择有效截止日期"));
		}
		if (isAfter(parseISO(timeBefore), parseISO(timeAfter))) {
			callback(new Error("有效截止日期不能早于签发日期"));
		}
		callback();
	}
}

const emits = defineEmits(["update:showEditShipCert", "onSuccess"]);

const _showEditShipCert = computed({
	get() {
		return props.showEditShipCert;
	},
	set(value) {
		emits("update:showEditShipCert", value);
	}
});

const formContent: Array<FormItemOptions> = [
	{ label: "基础信息", type: "title", name: "" },
	{
		label: "所属船舶",
		type: "singleSelect",
		api: ShipService.getAllSearchList,
		placeholder: "请选择所属船舶",
		name: "shipId",
		span: 24,
		otherAttr: {
			labelKey: "name",
			valueKey: "id"
		},
		visible: () => !props.isInShipDetail,
		disabled: () => props.mode === "edit"
	},
	{
		label: "证书类型",
		type: "singleSelect",
		api: () => {
			return getCertAndInsRuleList({ type: 1 });
		},
		placeholder: "请输入",
		name: "shipCertType",
		span: 24,
		otherAttr: {
			labelKey: "name",
			valueKey: "name"
		}
	},
	{
		label: "证书编号",
		type: "input",
		placeholder: "请输入",
		name: "no",
		span: 24,
		otherAttr: {
			max: 50
		}
	},
	{
		label: "签发日期",
		type: "date",
		placeholder: "请选择",
		name: "signTime",
		span: 24
	},
	{
		label: "有效期至",
		type: "date",
		placeholder: "请选择",
		name: "validTime",
		visible: () => !form?.value?.formData?.isNoValidTime,
		span: 24
	},
	{
		label: "签发机构",
		type: "input",
		placeholder: "请输入",
		name: "signAgency",
		span: 24
	},
	{
		label: "备注",
		type: "textarea",
		placeholder: "请输入",
		name: "remark",
		otherAttr: {
			max: 200
		}
	},
	{ label: "证书原件", type: "title", name: "" },
	{
		label: "上传附件",
		type: "fileUpload",
		name: "attachs",
		api: UploadService.ossApi,
		imageOptions: {
			pathName: "ship-cert",
			mode: "multiple",
			limitNum: 100
		}
	}
];

const formConfig: FormConfigOptions = {
	key: "edit-shipCert",
	items: formContent,
	addApi: CertService.add,
	editApi: CertService.edit,
	beforeSubmit: beforeSubmit,
	dataCallBack: dataCallBack,

	successCallBack: handleSuccess,
	detailApi: CertService.getDetail,
	detailKey: "uuid",
	closeFunction: close,
	closeFormAt: "both",
	hintMessage: ["创建船舶证书成功", "保存船舶证书成功"],
	changeLogApi: log => {
		return ShipService.addShipChangeRecord({
			type: ShipOptions.ShipChangeRecordTypeEnum.CERT,
			content: log,
			shipId: shipDetail ? shipDetail.value.id : shipId.value
		});
	}
};
/**若长期有效，则手动修改时间 */
function beforeSubmit(data: any) {
	props.mode === "add" && (shipId.value = data.shipId);
	if (data.isNoValidTime) {
		data.validTime = "9999-12-31 00:00:00";
	}
	return reformInpuNumber(data, ["certCycle", "certPremium", "certAmount"]);
}

let shipId = ref<number>(0);
/**编辑信息获取到数据后，若到期时间为9999，则记为长期有效 */
function dataCallBack(data: any) {
	if (data.validTime === "9999-12-31 00:00:00") {
		data.isNoValidTime = true;
		// delete data.validTime;
	}
	shipId.value = data.shipId;
	return data;
}
const detailContent: Array<DetailItemOptions> = [
	{ label: "基础信息", type: "title", name: "" },
	{
		label: "所属船舶",
		type: "detail",
		name: "shipName",
		span: 24
	},
	{
		label: "证书类型",
		type: "detail",
		name: "shipCertType",
		span: 24
	},
	{
		label: "证书编号",
		type: "detail",
		name: "no",
		span: 24
	},
	{
		label: "签发日期",
		type: "detail",
		name: "signTime",
		span: 24,
		otherAttr: {
			isDate: true
		}
	},
	{
		label: "有效期至",
		type: "detail",
		name: "validTime",
		span: 24,
		otherAttr: {
			isDate: true
		}
	},
	{
		label: "签发机构",
		type: "detail",
		name: "signAgency"
	},
	{
		label: "备注",
		type: "detail",
		name: "remark"
	},
	{ label: "证书原件", type: "title", name: "" },
	{ label: "附件列表", type: "fileList", name: "attachs" }
];

function getDetailParam() {
	return { uuid: props.editData.uuid };
}
const detailConfig: DetailConfigOptions = {
	detailApi: CertService.getDetail,
	detailParam: getDetailParam,
	labelPosition: "left",
	items: detailContent
};

//手动调用metaForm的初始化
let form = ref();

const { confirmClose } = useDialog(form);

function iniData() {
	if (props.mode === "detail") {
		certDetailRef.value.getDetailData();
		return;
	}

	if (props.mode === "add" && props.addCertType && props.addShipId) {
		form.value.iniForm(
			props.mode,
			{},
			{},
			{
				corpId: globalStore.userInfo.corpId,
				shipId: props.addShipId,
				shipCertType: props.addCertType
			}
		);
		// 这个是在按船搜索视图中 点击空白单元格打开新建证书且初始化船舶和证书类型
		return;
	}

	//最初的shipId。船舶内页时锁定，综合证书页时由form提供
	let iniParam: CertOptions.ReqAddParam = {
		corpId: globalStore.userInfo.corpId
	};
	shipDetail?.value?.id && (iniParam.shipId = shipDetail?.value?.id);
	form.value.iniForm(props.mode, props.editData, iniParam);
}

// 船舶证书按照证书种类直接确定是否为长期有效
watch(
	() => form.value?.formData?.shipCertType,
	(newval: any) => {
		if (form.value && form.value.selectionOptionsData.shipCertType && newval) {
			let shipCertTypeList = form.value.selectionOptionsData.shipCertType;
			let choosedType = shipCertTypeList.find((item: any) => {
				return item.label === newval;
			});
			if (choosedType) {
				if (choosedType.data.renewalDuration === -1) {
					form.value.formData.isNoValidTime = true;
				} else {
					form.value.formData.isNoValidTime = false;
				}
			} else {
				form.value.formData.isNoValidTime = false;
			}
		}
	}
);

/**提交成功回调。多为关闭弹窗、提示成功 */
function handleSuccess() {
	emits("onSuccess");
}

/**本组件中的dialog关闭不代表父组件控制弹窗用的变量改为了false。需要手动emit一下 */
function handleClosed() {
	close();
}

/**关闭弹窗，通知父组件 */
function close() {
	_showEditShipCert.value = false;
}
</script>

<style scoped></style>
