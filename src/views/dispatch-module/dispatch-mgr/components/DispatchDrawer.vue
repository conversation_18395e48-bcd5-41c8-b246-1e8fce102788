<!--调度添加/编辑抽屉-->
<template>
	<div class="dis-draw-container">
		<el-drawer
			ref="dialog"
			:title="mode === 'add' ? '发起调度' : '编辑调度'"
			v-model="_dialogDisRemarkShow"
			size="50%"
			destroy-on-close
			@open="iniData"
			:before-close="confirmClose"
			@closed="handleClosed"
			draggable
		>
			<MetaForm
				:mode="mode"
				ref="form"
				:formConfig="formConfig"
				:styleConfig="{ mode: 'drawer', labelWidth: '110px', labelPosition: 'left' }"
				:rule-form="ruleForm"
			>
				<template #dispatchHint>
					<div class="dispacth-type-hint">{{ dispatchTypeHint }}</div>
				</template>
				<template #selectMap="scope: any">
					<div v-if="!scope.formData.location || (scope.formData.location && !scope.formData.location.lat)" style="width: 100%">
						<el-button @click="handlerMap('add', scope.formData)"
							><el-icon style="margin-right: 10px"><LocationInformation /></el-icon>请选择</el-button
						>
					</div>
					<div style="width: 100%" v-else :style="{ marginBottom: '-15px' }">
						<el-button @click="handlerMap('edit', scope.formData)"
							><el-icon style="padding-right: 10px"><LocationInformation /></el-icon>重新选择</el-button
						>
						<div style="margin-top: -5px; margin-bottom: 4px; font-size: 12px; color: #40a9ff">
							已选{{ LatlngCnvTool(scope.formData.location.lat, scope.formData.location.lon, "DDD", "DMS") }}
						</div>
					</div>
				</template>
				<template #shipTitle>
					<div class="flx-align-center title">
						<div class="title-mark"></div>
						<text class="sub-title">参与船舶</text>
						<template v-if="isShowFlag && dispatchType === DispatchTypeEnum.SECDISPATCH">
							<span class="placeholder"> 暂无授权船舶，请重新申请后再发起调度</span>
						</template>
						<template v-if="dispatchIsShowFlag && dispatchType === DispatchTypeEnum.DISPATCH">
							<div>
								<span style="font-size: 13px; color: #909399"
									>暂无船舶，请
									<el-link :underline="false" type="primary" @click="toAddShip" style="transform: translateY(-1px)"
										>添加船舶</el-link
									>
									后再发起调度</span
								>
							</div>
						</template>
					</div>
				</template>
				<template #shipList="scope: any">
					<div v-show="dispatchType !== DispatchTypeEnum.SECDISPATCH || projectId !== UNSELECTEDNUM" style="width: 100%">
						<PartakeShipTable
							ref="shipListRef"
							:mode="mode"
							:formData="scope.formData"
							:dispatch-type="dispatchType"
							:inline-edit-data="inlineEditData"
							:project-id="projectId"
						></PartakeShipTable>
					</div>
				</template>
			</MetaForm>
			<SelectMapDialog
				ref="selectMapDialogRef"
				v-model:selectMapShow="selectMapShow"
				:location="disForm.location ?? {}"
				:mode="modeMap"
				title="调度地点"
				@map-center-point="getMapCenterPoint"
			></SelectMapDialog>
		</el-drawer>
	</div>
</template>

<script setup lang="ts" name="EditShipDisRemark">
import MetaForm from "@/meta-components/MetaForm/index.vue";
import PartakeShipTable from "./PartakeShipTable.vue";
import { PropType, ref, reactive, computed, provide, watch, nextTick, watchEffect } from "vue";
import SelectMapDialog from "../map-components/SelectMapDialog.vue";
import { LatlngCnvTool } from "@/utils/util";
import { FormConfigOptions, FormItemOptions } from "@/meta-components/MetaForm/interface";
import DispatchService from "@/api/modules/dispatch";
// import { reformInpuNumber } from "@/meta-components/MetaUtils/FormUtils";
import { MemberService } from "@/api/modules/member";
import { Dispatch } from "@/api/interface/dispatch.model";
import { IsOverLapping, IsTimeClash } from "../interface/index";
import _ from "lodash";
import { FigureColorMap } from "../interface/index";
import { ProjectService } from "@/api/modules/project";
import router from "@/routers";
import { ShipService } from "@/api/modules/ship";
import { useDialog } from "@/meta-components/MetaHooks/useDialog";

// import { ElMessage } from "element-plus";
// 未选中的默认值
const UNSELECTEDNUM = -1;
enum DispatchTypeEnum {
	"DISPATCH" = 1,
	"SECDISPATCH" = 2
}
let props = defineProps({
	mode: { type: String as PropType<"add" | "edit">, requeired: true },
	dialogDisRemarkShow: { type: Boolean, required: true },
	editData: { type: Object }
});

// 调度所需要控制展示隐藏的变量
let dispatchIsShowFlag = ref(Dispatch.IsShowPlaceholder.Hidden);

// 二级调度所需要控制展示隐藏的变量
let isShowFlag = ref(Dispatch.IsShowPlaceholder.Hidden);

const emits = defineEmits(["update:dialogDisRemarkShow", "refresh"]);

const _dialogDisRemarkShow = computed({
	get() {
		return props.dialogDisRemarkShow;
	},
	set(value) {
		emits("update:dialogDisRemarkShow", value);
	}
});
let disForm = reactive({} as { [key: string]: any });
let shipList = ref<Array<Dispatch.DispatchShip>>([]);
/**参与船舶列表 */
provide("shipList", shipList);
const validatorMap = (rule: any, value: any, callback: any) => {
	if (JSON.stringify(disForm.location) === "{}" || !disForm.location || (disForm.location && !disForm.location.lat)) {
		callback(new Error("请在地图上选择位置"));
	} else {
		callback();
	}
};
/** 重叠船舶数据 */
let lappingShipArray = ref<Array<{ [key: string]: any }>>([]);
/** 冲突船舶数据 */
let clashShipArray = ref<Array<{ [key: string]: any }>>([]);

const validatorShipList = (rule: any, value: any, callback: any) => {
	if (shipList.value && shipList.value.length === 0) {
		callback(new Error("请填写并保存船舶信息"));
	} else if (lappingShipArray.value.length > 0 || clashShipArray.value.length > 0) {
		/** 拼接任务冲突的船舶 */
		let shipContent = "";
		lappingShipArray.value.forEach((ship, index) => {
			shipContent += ship.shipName;
			if (index !== lappingShipArray.value.length - 1) {
				shipContent += "、";
			}
		});

		/** 拼接任务周期冲突的船舶 */
		let clashShipContent = "";
		clashShipArray.value.forEach((ship, index) => {
			clashShipContent += ship.shipName;
			if (index !== clashShipArray.value.length - 1) {
				clashShipContent += "、";
			}
		});
		if (clashShipContent !== "" && shipContent !== "") {
			callback(new Error(`${shipContent}与已有任务时间重叠，当前任务周期与${clashShipContent}存在时间冲突，请重新选择`));
		} else if (clashShipContent !== "") {
			callback(new Error(`当前任务周期与${clashShipContent}存在时间冲突，请重新选择`));
		} else if (shipContent !== "") {
			callback(new Error(`${shipContent}与已有任务时间重叠，请重新选择`));
		}
	} else if (shipListRef.value.partakeShipTableRef && shipListRef.value.partakeShipTableRef.inlineEditProps.open) {
		callback(new Error("请填写并保存船舶信息"));
	} else {
		callback();
	}
};

// 监听获取调度类型
let dispatchType = ref<number>(UNSELECTEDNUM);
let projectId = ref<number>(UNSELECTEDNUM);
watch(
	() => props.editData,
	val => {
		if (!val) return;
		dispatchType.value = val.dispatchType || UNSELECTEDNUM;
	},
	{ deep: true, immediate: true }
);

let form = ref();

const { confirmClose } = useDialog(form);

// 电镀类型对应的提示文本
const dispatchTypeHint = computed(() => {
	return dispatchType.value === DispatchTypeEnum.SECDISPATCH ? "可调度租用中的船舶" : "可调度自用的船舶";
});

// 监听调度类型变化
const validatorDispatchType = (rule: any, value: any, callback: any) => {
	if (value) {
		dispatchType.value = value;
		if (value === DispatchTypeEnum.DISPATCH) {
			projectId.value = UNSELECTEDNUM;
		}
		callback();
	} else {
		callback(new Error("请选择调度类型"));
	}
};

// 监听项目选择框的候选项，查找选中项目的相关数据
watchEffect(() => {
	if (form.value && form.value.selectionOptionsData.projectId) {
		const selectionOptionsData = form.value.selectionOptionsData.projectId;
		const aim = selectionOptionsData.find((e: any) => {
			return e.value === projectId.value;
		});
		if (!aim) return;
		form.value.formData.projectTime = [aim.data.startTime, aim.data.endTime];
		// 二级调度下taskTime不显示，fornData中的taskTime属性被删除，这里备份
		form.value.formData.taskTimeBackup = [aim.data.startTime, aim.data.endTime];
		form.value.formData.contact = `${aim.data.principalName} ${aim.data.principalMobile}`;
	}
});

const validatorProject = (rule: any, value: any, callback: any) => {
	if (value) {
		projectId.value = value;
		callback();
	} else {
		projectId.value = UNSELECTEDNUM;
		callback(new Error("请选择项目"));
	}
};

watch(
	() => dispatchType.value,
	() => {
		// 由于船舶列表在调度类型切换之后的校验提示直接清除无效(包括nextTick)，暂时加定时器
		setTimeout(() => {
			form.value.clearValidate();
		}, 100);
	},
	{}
);

/** 收集参与船舶重叠对象 */
watch(
	() => shipList.value,
	newV => {
		lappingShipArray.value = [];
		newV &&
			newV.forEach((item: any) => {
				if (item.isLapping === IsOverLapping.Lapping) {
					lappingShipArray.value.push(item);
				}
			});
		clashShipArray.value = [];
		newV &&
			newV.forEach((item: any) => {
				if (item.isClash === IsTimeClash.Clash) {
					clashShipArray.value.push(item);
				}
			});
		nextTick(() => {
			/**手动校验 */
			form.value && form.value.doSingleValidate("shipList");
		});
		// form.value.doValidate();
	},
	{ deep: true }
);
let ruleForm = reactive({
	dispatchType: [{ validator: validatorDispatchType, required: true, message: "请选择电镀类型" }],
	projectId: [{ validator: validatorProject, required: true, message: "请选择项目" }],
	taskName: [{ required: true, message: "请输入任务名称" }],
	taskTime: [{ required: true, message: "请选择任务周期" }],
	selectMap: [{ validator: validatorMap, required: true, trigger: "change" }],
	principalName: [{ required: true, message: "请输入负责人" }],
	principalMobile: [{ required: true, message: "请输入负责人电话" }],
	shipList: [{ validator: validatorShipList, required: true }]
});

const formContent: Array<FormItemOptions> = [
	{ label: "任务信息", type: "title", name: "" },
	{
		label: "调度类型",
		type: "radio",
		placeholder: "请输入",
		name: "dispatchType",
		span: 24,
		staticOptions: [
			{ label: "调度", value: 1 },
			{ label: "二级调度", value: 2 }
		],
		disabled: () => props.mode === "edit",
		hintSlot: "dispatchHint"
	},
	{
		label: "项目名称",
		type: "singleSelect",
		placeholder: "请选择",
		name: "projectId",
		span: 24,
		api: () => ProjectService.getProjectList({ offset: -1, length: -1 }),
		otherAttr: {
			labelKey: "name",
			valueKey: "id",
			appendOptionToFormDataWithKey: [
				{ optionKey: "startTime", formKey: "startTime" },
				{ optionKey: "endTime", formKey: "endTime" },
				{ optionKey: "principalMobile", formKey: "projectPrincipalMobile" },
				{ optionKey: "principalName", formKey: "projectPrincipalName" },
				{ optionKey: "corpName", formKey: "corpName" }
			]
		},
		clearable: false,
		visible() {
			return dispatchType.value === DispatchTypeEnum.SECDISPATCH;
		},
		disabled: () => props.mode === "edit"
	},
	{
		label: "任务名称",
		type: "input",
		placeholder: "请输入",
		name: "taskName",
		span: 24,
		otherAttr: {
			max: 50
		}
	},
	{
		label: "任务周期",
		type: "dateRange",
		placeholder: "请选择",
		name: "taskTime",
		span: 24,
		visible() {
			return dispatchType.value !== DispatchTypeEnum.SECDISPATCH;
		}
	},
	{
		label: "项目周期",
		type: "dateRange",
		placeholder: "请选择",
		name: "projectTime",
		span: 24,
		visible() {
			return dispatchType.value === DispatchTypeEnum.SECDISPATCH;
		},
		disabled: () => true
	},
	{
		label: "项目方",
		type: "input",
		placeholder: "请输入",
		name: "corpName",
		span: 24,
		otherAttr: {
			max: 50
		},
		visible() {
			return dispatchType.value === DispatchTypeEnum.SECDISPATCH;
		},
		disabled: () => true
	},
	{
		label: "项目方联系人",
		type: "input",
		placeholder: "请输入",
		name: "contact",
		span: 24,
		otherAttr: {
			max: 50
		},
		visible() {
			return dispatchType.value === DispatchTypeEnum.SECDISPATCH;
		},
		disabled: () => true
	},
	{
		label: "地图选点",
		type: "slot",
		name: "selectMap",
		span: 24,
		hintSlot: "selectMapSlot"
	},
	{
		label: "负责人",
		type: "autoComplete",
		name: "principalName",
		span: 24,
		api: MemberService.getSimpleList,
		otherAttr: {
			labelKey: "name",
			valueKey: "name",
			subLabelKey: "contactMobile",
			appendOptionToFormDataWithKey: [
				{
					optionKey: "contactMobile",
					formKey: "principalMobile"
				}
			],
			max: 50
		},
		placeholder: "请输入"
	},
	{
		label: "负责人电话",
		type: "input",
		placeholder: "请输入",
		name: "principalMobile",
		span: 24,
		otherAttr: {
			max: 15
		}
	},
	{
		label: "",
		type: "slot",
		name: "shipTitle",
		visible() {
			return dispatchType.value !== DispatchTypeEnum.SECDISPATCH || projectId.value !== UNSELECTEDNUM;
		}
	},
	{
		label: "",
		type: "slot",
		name: "shipList",
		span: 24
	}
];

const dataCallBack = (data: any) => {
	disForm = data;
	let { ships } = data;
	if (ships) {
		let _ss = _.cloneDeep(ships);
		_ss.forEach((e: any) => {
			e.id = -Number(new Date().getTime() + "" + Math.floor(Math.random() * 1000000));
			/**是否重叠，编辑情况下肯定是不重叠的 */
			e.isLapping = IsOverLapping.NotLapping;
			e.taskTime = data.taskTime;
			e.dispatchUuid = data.uuid;
		});
		shipList.value.push(..._ss);
	}
	dispatchType.value = data.dispatchType;
	projectId.value = data.projectId;
	// 备份taskTime
	data.taskTimeBackup = data.projectTime;
	return data;
};
const beforeSubmit = (data: any) => {
	shipList.value.map((item: any) => {
		if (item.shipId) {
			if (typeof item.shipId !== "number") {
				item.shipId = JSON.parse(item.shipId as unknown as string).id;
			}
		}
		item.format && delete item.format;
		item.rent = Number(item.rent);
	});
	data.ships = shipList.value;

	// 颜色赋值
	let randomWord = parseInt((Math.random() * 4).toString());
	let color = FigureColorMap.get(Number(randomWord));
	data.color = color;
	// 二级调度下 任务周期用项目周期替换
	data.dispatchType === DispatchTypeEnum.SECDISPATCH && (data.taskTime = data.projectTime);
	return data;
};
const formConfig: FormConfigOptions = {
	key: "edit-shipInsurance",
	items: formContent,
	addApi: DispatchService.addDisRemark,
	editApi: DispatchService.updateDisRemark,
	successCallBack: handleSuccess,
	detailApi: DispatchService.getDisRemarkDetail,
	beforeSubmit: beforeSubmit,
	detailKey: "uuid",
	closeFunction: close,
	closeFormAt: "both",
	dataCallBack: dataCallBack,
	hintMessage: ["发起调度成功", "保存调度任务成功"]
};

//手动调用metaForm的初始化
async function iniData() {
	// 初始化调度船舶列表
	form.value.iniForm(
		props.mode,
		props.editData,
		{ uuid: props.editData?.uuid },
		{ dispatchType: 1, location: {}, taskTime: props.editData?.taskTime }
	);
	disForm = form.value.formData;
	await getShipOptions();
}
/**选择地图的显隐 */
let selectMapShow = ref(false);
let modeMap = ref("add" as "add" | "edit");
/**显示地图弹框 */
const handlerMap = (mode: "add" | "edit", form: { [key: string]: any }) => {
	disForm = reactive(form);
	modeMap.value = mode;
	selectMapShow.value = true;
};
/**获取地图中点 */
const getMapCenterPoint = (obj: { [key: string]: any }) => {
	console.log("获取地图中点", obj);
	disForm.location.lon = Number(obj.lon);
	disForm.location.lat = Number(obj.lat);
	/**手动校验 */
	form.value.doSingleValidate("selectMap");
};
/**提交成功回调。多为关闭弹窗、提示成功 */
function handleSuccess() {
	emits("refresh");
}
/**船舶列表实例 */
let shipListRef = ref();
/**新增船舶, 按钮内移至表格，暂时注释 */
// let addShip = () => {
// 	if (projectId.value === UNSELECTEDNUM && dispatchType.value === DispatchTypeEnum.SECDISPATCH) {
// 		ElMessage.error("请选择项目");
// 		return;
// 	}
// 	if (props.mode === "add") {
// 		shipListRef.value.partakeShipTableRef.enterInlineAdd({
// 			shipTaskTime: form.value.formData.taskTime || form.value.formData.taskTimeBackup,
// 			taskTime: form.value.formData.taskTime || form.value.formData.taskTimeBackup
// 		});
// 	} else {
// 		shipListRef.value.partakeShipTableRef.enterInlineAdd({
// 			dispatchUuid: form.value.formData.uuid,
// 			shipTaskTime: form.value.formData.taskTime || form.value.formData.taskTimeBackup,
// 			taskTime: form.value.formData.taskTime || form.value.formData.taskTimeBackup
// 		});
// 	}
// };

let inlineEditData = computed(() => {
	if (!form.value) return {};
	if (props.mode === "add") {
		return {
			shipTaskTime: form.value.formData.taskTime || form.value.formData.taskTimeBackup,
			taskTime: form.value.formData.taskTime || form.value.formData.taskTimeBackup
		};
	} else {
		return {
			dispatchUuid: form.value.formData.uuid,
			shipTaskTime: form.value.formData.taskTime || form.value.formData.taskTimeBackup,
			taskTime: form.value.formData.taskTime || form.value.formData.taskTimeBackup
		};
	}
});

/**本组件中的dialog关闭不代表父组件控制弹窗用的变量改为了false。需要手动emit一下 */
function handleClosed() {
	close();
}
/**关闭弹窗，通知父组件 */
function close() {
	shipList.value = [];
	dispatchType.value = UNSELECTEDNUM;
	projectId.value = UNSELECTEDNUM;
	_dialogDisRemarkShow.value = false;
}

// 去添加船舶页面
function toAddShip() {
	router.push({
		name: "EditShip"
	});
}

/** 船舶所属方 */
enum BELONGTYPE {
	/** 船东方 */
	"SHIPOWNER" = 1,
	/** 项目方 */
	"PROJECT" = 2
}

/**监听 projectId */
watch(
	() => projectId.value,
	async () => {
		await getShipOptions();
	},
	{ deep: true }
);

/**监听 dispatchType */
watch(
	() => dispatchType.value,
	async () => {
		await getShipOptions();
	},
	{ deep: true }
);
/**获取参与船舶的船舶列表 分调度和二级调度 二级调度是通过projectId来拿的 用来给子组件使用 */
async function getShipOptions() {
	// 调度 只需要发送一次网络请求 就是初始化的时候
	if (dispatchType.value === DispatchTypeEnum.DISPATCH) {
		const { code, data } = await ShipService.getAllSearchList({});
		if (code === 0) {
			if (data?.list && data.list.length !== 0) {
				dispatchIsShowFlag.value = Dispatch.IsShowPlaceholder.Hidden;
			} else {
				dispatchIsShowFlag.value = Dispatch.IsShowPlaceholder.Show;
			}
		}
	}
	// 二级调度 且选择了项目
	if (dispatchType.value === DispatchTypeEnum.SECDISPATCH && projectId.value !== UNSELECTEDNUM) {
		const { code, data } = await DispatchService.getDispatchShipList({
			projectId: projectId.value,
			belong: BELONGTYPE.SHIPOWNER
		});
		if (code === 0) {
			if (data?.list && data.list.length !== 0) {
				isShowFlag.value = Dispatch.IsShowPlaceholder.Hidden;
			} else {
				isShowFlag.value = Dispatch.IsShowPlaceholder.Show;
			}
		}
	}
}
</script>

<style lang="scss" scoped>
.dis-draw-container {
	.el-form-item__error--inline {
		margin-left: 0;
		line-height: 15px;
	}
}
.dispacth-type-hint {
	color: var(--el-color-warning);
	line-height: 20px;
}

.sub-title {
	margin-left: 12px;
	margin-right: 12px;
	font-weight: bold;
}
.title-mark {
	width: 4px;
	height: 16px;
	background-color: $primary-color;
}
.title {
	height: 21px;
	font-size: 16px;
	.placeholder {
		height: 32px;
		font-size: 14px;
		display: inline-block;
		margin-left: 12px;
		color: var(--el-color-warning);
		font-weight: normal;
	}
}
</style>
