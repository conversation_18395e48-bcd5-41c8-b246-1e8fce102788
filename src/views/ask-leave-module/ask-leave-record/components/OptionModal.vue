<!-- 配置请假指定人员弹窗 -->
<template>
	<el-dialog title="配置请假审批人员" v-model="visible" width="30%" draggable :destroy-on-close="true" @open="getData">
		<MetaForm
			mode="edit"
			ref="form"
			:formConfig="formConfig"
			:styleConfig="{ mode: 'drawer', labelWidth: '100px', labelPosition: 'left' }"
			:rule-form="ruleForm"
		>
		</MetaForm>
	</el-dialog>
</template>
<script setup lang="ts">
import { ref, reactive } from "vue";
import MetaForm from "@/meta-components/MetaForm/index.vue";
import { FormConfigOptions, FormItemOptions } from "@/meta-components/MetaForm/interface";
import { MemberService } from "@/api/modules/member";
import AskLeaveService from "@/api/modules/askLeave";
import { LeaveStore } from "@/store/modules/leave";

const leaveStore = LeaveStore();

let visible = ref(false);
let form = ref();
// let inited = ref(false);
const open = () => {
	visible.value = true;
};
const formContent: Array<FormItemOptions> = reactive([
	{
		label: "负责人",
		type: "singleSelect",
		name: "userId",
		span: 24,
		api: MemberService.getSimpleList,
		otherAttr: {
			remote: true,
			remoteLabelKey: "userName",
			labelKey: "name",
			valueKey: "id",
			subLabelKey: "contactMobile",
			appendOptionToFormDataWithKey: [
				{ optionKey: "contactMobile", formKey: "phone" },
				{ optionKey: "name", formKey: "userName" }
			]
		},
		placeholder: "请输入"
	},
	{
		label: "负责人电话",
		type: "text",
		name: () => {
			return form.value?.formData.phone || leaveStore.option.phone;
		},
		placeholder: "请输入",
		span: 24,
		otherAttr: {
			max: 15
		}
	}
]);

const handleSuccess = (_: any, formData: any) => {
	// update后更新store
	leaveStore.setOption(formData);
};
const close = () => {
	visible.value = false;
};
const formConfig: FormConfigOptions = {
	key: "leave-option",
	items: formContent,
	editApi: AskLeaveService.optionUpdate,
	successCallBack: handleSuccess,
	detailApi: AskLeaveService.optionDetail,
	dataCallBack: data => {
		// 未配置用户，回显空对象
		if (data.userId !== 0) {
			leaveStore.setOption(JSON.parse(JSON.stringify(data)));
			return data;
		}
		return {};
	},
	detailKey: "uuid",
	closeFunction: close,
	closeFormAt: "both",
	hintMessage: ["", "修改成功"]
};

let ruleForm = reactive({
	userName: [{ required: true, message: "请输入负责人" }]
});

const getData = () => {
	form.value.iniForm("edit", { uuid: "" }, {});
};
defineExpose({ open });
</script>
<style lang="scss" scoped></style>
