<!-- 船舶汇报记录 -->
<template>
	<div style="height: calc(100% - 24px); overflow-y: overlay" class="page-container">
		<div class="page-container-statis-mini-relative" style="margin-bottom: 1px">
			<div class="flex-container">
				<div>
					<span>船舶汇报列表</span>
					<ElDivider direction="vertical" />
					<div v-if="hasDoubleViewAuth()" class="switch-wrapper">
						<SwitchGroup v-model="currentView" :buttons="buttons" />
					</div>
				</div>
				<el-button v-if="hasAuth('汇报配置修改') && hasAuth('汇报配置详情')" type="primary" @click="showOption"
					>配置汇报接收人</el-button
				>
			</div>
		</div>
		<div class="main">
			<TransitionGroup appear name="fade-transform" mode="out-in" :duration="250">
				<keep-alive>
					<ReportTableView v-if="currentView === CurrentViewEnum.ALL && hasAuth('汇报列表')" />
					<BoardView v-else-if="currentView === CurrentViewEnum.Board && hasAuth('汇报列表')" />
					<CardView v-else-if="currentView === CurrentViewEnum.Card && hasAuth('按船汇报列表')" />
				</keep-alive>
			</TransitionGroup>
		</div>

		<DetailDrawer v-model:visible="visible" :edit-data="formData" />
		<OptionModal ref="optionModalRef" />
	</div>
</template>
<script setup lang="ts" name="ShipReportRecord">
import { ref, onMounted } from "vue";
import { useTableScrollMemory } from "@/hooks/useTableScrollMemory";
import DetailDrawer from "./components/DetailDrawer.vue";
import { hasAuth } from "@/utils/util";
import SwitchGroup, { SwitchButtonProps } from "@/components/SwitchGroup/index.vue";
import ReportTableView from "./components/ReportTableView.vue";
import BoardView from "./components/BoardView.vue";
import CardView from "./components/CardView.vue";
import OptionModal from "./components/OptionModal.vue";

/**标签页配置项 */
let tableRef = ref();
let visible = ref(false);
let formData = ref({});

let optionModalRef = ref();

// const allKey = ref(Date.now());
// const cardKey = ref(Date.now());
// const boardKey = ref(Date.now());

enum CurrentViewEnum {
	"ALL" = "all",
	"Card" = "card",
	"Board" = "board"
}
const currentView = ref<string>(CurrentViewEnum.ALL);
const buttons = ref<SwitchButtonProps[]>([
	{ label: "列表", key: "all", value: "all" },
	{ label: "画册", key: "card", value: "card" },
	{ label: "看板", key: "board", value: "board" }
]);

const showOption = () => {
	optionModalRef.value.open();
};

// 是否有切换视图的权限
function hasDoubleViewAuth() {
	return hasAuth("汇报列表") && hasAuth("按船汇报列表");
}

onMounted(() => {
	useTableScrollMemory(tableRef, "ShipReportMgr");
});
</script>

<style scoped lang="scss">
.flex-container {
	display: flex;
	align-items: center;
	justify-content: space-between;
}
.switch-wrapper {
	display: inline-flex;
	align-items: center;
	justify-content: center;
	width: 140px;
	height: 100%;
}
.main {
	/* padding: 10px 12px; */

	/* background-color: var(--el-fill-color-blank); */
	width: calc(100%);
	height: calc(100% - 40px - 4px);
	padding: 0;
	margin-top: 4px !important;
	overflow: hidden;
	background-color: transparent !important;
	border-radius: 4px;
}
</style>
