<!-- 汇报接收人配置弹窗 -->
<template>
	<el-dialog title="配置汇报接收人" v-model="visible" width="30%" draggable :destroy-on-close="true" @open="getData">
		<MetaForm
			mode="edit"
			ref="form"
			:formConfig="formConfig"
			:styleConfig="{ mode: 'drawer', labelWidth: '100px', labelPosition: 'left' }"
		>
		</MetaForm>
	</el-dialog>
</template>
<script setup lang="ts">
import { ref, reactive } from "vue";
import MetaForm from "@/meta-components/MetaForm/index.vue";
import { FormConfigOptions, FormItemOptions } from "@/meta-components/MetaForm/interface";
import { MemberService } from "@/api/modules/member";
import ShipReportService from "@/api/modules/shipReport";

let visible = ref(false);
let form = ref();

const open = () => {
	visible.value = true;
};
const close = () => {
	visible.value = false;
};

const formContent: Array<FormItemOptions> = reactive([
	{
		label: "接收人",
		type: "singleSelect",
		name: "userId",
		span: 24,
		api: MemberService.getSimpleList,
		otherAttr: {
			remote: true,
			remoteLabelKey: "userName",
			labelKey: "name",
			valueKey: "id",
			max: 50,
			subLabelKey: "contactMobile",
			appendOptionToFormDataWithKey: [
				{ optionKey: "contactMobile", formKey: "phone" },
				{ optionKey: "name", formKey: "userName" }
			]
		},
		placeholder: "请输入"
	},
	{
		label: "接收人电话",
		type: "text",
		name: () => {
			return form.value?.formData.phone;
		},
		placeholder: "请输入",
		span: 24,
		otherAttr: {
			max: 15
		}
	}
]);

const handleSuccess = () => {
	visible.value = false;
};

const formConfig: FormConfigOptions = {
	key: "ship-report-option",
	items: formContent,
	editApi: ShipReportService.optionUpdate,
	successCallBack: handleSuccess,
	detailApi: ShipReportService.optionDetail,
	dataCallBack: data => {
		// 未配置用户，回显空对象
		if (data.userId !== 0) {
			return data;
		}
		return {};
	},
	beforeSubmit: (formData: any) => {
		return formData;
	},
	detailKey: "uuid",
	closeFunction: close,
	closeFormAt: "both",
	hintMessage: ["", "修改成功"]
};

const getData = async () => {
	setTimeout(() => {
		form.value.iniForm("edit", { uuid: "" }, {});
	}, 100);
};
defineExpose({ open });
</script>
<style lang="scss" scoped></style>
