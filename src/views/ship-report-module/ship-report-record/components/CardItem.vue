<!-- 船舶汇报单个卡片 -->
<template>
	<div class="report-card" :key="data.id">
		<div class="title">{{ data.shipName }}</div>
		<div class="item">
			<div class="label">报告人姓名</div>
			<div class="value">{{ data.reporterName }}</div>
		</div>
		<div class="item">
			<div class="label">报告类型</div>
			<div class="value tag" :style="{ background: TagColorMap && TagColorMap.get(data.type) }">
				{{ ReportTypeEnum[data.type] }}
			</div>
		</div>
		<div class="item img-item" v-for="imgItem in ImageTypes" :key="imgItem.key">
			<template v-if="data[imgItem.key] && data[imgItem.key].length > 0">
				<div class="label">{{ imgItem.name }}</div>
				<div class="value img">
					<el-image
						v-for="img in data[imgItem.key]"
						:key="img"
						:src="img"
						fit="cover"
						class="img-in-table"
						preview-teleported
						:hide-on-click-modal="true"
						:preview-src-list="data[imgItem.key]"
						lazy
					/>
				</div>
			</template>
		</div>
		<div class="item">
			<div class="label">文字说明</div>
			<div class="value">
				<span v-if="data.description && data.description.length < 40">{{ data.description }}</span>
				<el-tooltip v-else effect="dark" placement="top-end">
					<template #content>
						<div style="width: 160px">{{ data.description }}</div>
					</template>
					{{ data.description }}
				</el-tooltip>
			</div>
		</div>
		<div class="item">
			<div class="label">上报时间</div>
			<div class="value">{{ data.createdAt }}</div>
		</div>
	</div>
</template>
<script lang="ts" setup>
import { ImageTypes, ReportTypeEnum, TagColorMap } from "../../interface";
defineProps({
	data: { type: Object, required: true }
});
</script>
<style lang="scss" scoped>
.report-card {
	display: inline-block;
	width: 200px;
	padding: 12px;
	margin-top: 12px;
	vertical-align: top;
	background: #ffffff;
	border-radius: 10px;
	.title {
		margin: 8px 0 12px;
		font-size: 16px;
	}
	.item {
		margin-top: 8px;
		font-size: 14px;
		.label {
			font-size: 12px;
			color: #aaaaaa;
			margin: 8px 0;
		}
		.value {
			display: -webkit-box;
			-webkit-box-orient: vertical;
			-webkit-line-clamp: 3;
			overflow: hidden;
			text-overflow: ellipsis;
			&.img {
				:deep(.el-image) {
					height: 48px;
					margin-right: 4px;
				}
			}
			&.tag {
				width: fit-content;
				padding: 2px 6px;
				font-size: 12px;
				color: #ffffff;
				border-radius: 14px;
			}
		}
		&.img-item {
			.label {
				margin-bottom: 12px;
			}
		}
	}
}
// @media only screen and (max-width: 1024px) {
// 	.report-card {
// 		width: calc((100% - 48px) / 3);
// 		:nth-child(3n) {
// 			margin-right: 12px;
// 		}
// 	}
// }
// @media only screen and (min-width: 1024px) {
// 	.report-card {
// 		width: calc((100% - 60px) / 4);
// 		:nth-child(4n) {
// 			margin-right: 12px;
// 		}
// 	}
// }
// @media only screen and (min-width: 1440px) {
// 	.report-card {
// 		width: calc((100% - 72px) / 5);
// 		:nth-child(5n) {
// 			margin-right: 12px;
// 		}
// 	}
// }
// @media only screen and (min-width: 1680px) {
// 	.report-card {
// 		width: calc((100% - 84px) / 6);
// 		:nth-child(6n) {
// 			margin-right: 12px;
// 		}
// 	}
// }
</style>
