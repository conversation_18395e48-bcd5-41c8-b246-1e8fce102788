<!-- 船舶汇报详情 -->
<template>
	<el-drawer
		ref="dialog"
		title="船舶汇报"
		v-model="_visible"
		size="35%"
		@open="iniData"
		@closed="handleClosed"
		destroy-on-close
		draggable
	>
		<MetaDetail ref="detailRef" :detail-config="detailConfig">
			<template #type="{ detailData }: any"> {{ ReportTypeEnum[detailData.type] }}</template>
			<template v-for="item in imgSlots" #[`${item}Img`]="{ detailData }: any" :key="item">
				<div v-if="detailData[`${item}Img`]" class="img-container">
					<el-image
						v-for="imgUrl in detailData[`${item}Img`]"
						:key="imgUrl"
						:src="imgUrl"
						fit="cover"
						class="img-in-table"
						:lazy="true"
						preview-teleported
						:hide-on-click-modal="true"
						:preview-src-list="[imgUrl]"
					/>
				</div>
				<span v-else style="color: #909399">-</span>
			</template>
		</MetaDetail>
	</el-drawer>
</template>

<script setup lang="ts" name="EditSailorCert">
//LATER OCR
import MetaDetail from "@/meta-components/MetaDetail/index.vue";
import { PropType, ref, computed, watch } from "vue";
import ShipReportService from "@/api/modules/shipReport";
// import { GlobalStore } from "@/store";
import { DetailConfigOptions, DetailItemOptions } from "@/meta-components/MetaDetail/interface";
import { YN } from "@/enums/global-enums";
import { ReportTypeEnum } from "../../interface";

const detailRef = ref();
const addCertForm = ref();
const imgSlots = ["bridge", "work", "cabin", "deck", "life", "lifesaving"];

let props = defineProps({
	visible: { type: Boolean, required: true },
	mode: { type: String as PropType<"add" | "edit" | "detail">, requeired: true },
	/**若传入uuid，则锁定对应用户 */
	userId: { type: String, default: "" },
	editData: { type: Object, required: true },
	userMobile: { type: String, default: "" },
	userName: { type: String, default: "" }
});

const emits = defineEmits(["update:visible", "onSuccess"]);

const _visible = computed({
	get() {
		return props.visible;
	},
	set(value) {
		emits("update:visible", value);
	}
});
/**监听certType选择的内容，判断是否为培训证书 */
watch(
	() => addCertForm.value?.formData,
	newV => {
		if (newV && newV.certType) {
			addCertForm.value && (addCertForm.value.formData.isTrain = newV.certType.indexOf("培训") !== -1 ? YN.Yes : YN.No);
		} else {
			addCertForm.value && (addCertForm.value.formData.isTrain = YN.No);
		}
	},
	{ deep: true }
);
/**监听contents变化，及时修改显示文字 */
watch(
	() => addCertForm.value?.formData.contents,
	() => {
		addCertForm.value && addCertForm.value.doSingleValidate("contents");
	},
	{ deep: true }
);

const detailContent: Array<DetailItemOptions> = [
	{ label: "基础信息", type: "title", name: "" },
	{
		label: "船名",
		type: "detail",
		name: "shipName",
		span: 24
	},
	{
		label: "报告人姓名",
		type: "detail",
		name: "reporterName",
		span: 24
	},
	{
		label: "报告类型",
		type: "slot",
		name: "type",
		span: 24
	},
	{
		label: "汇报日期",
		type: "detail",
		name: "createdAt",
		span: 24
	},
	{ label: "汇报详情", type: "title", name: "" },
	{
		label: "顶层甲板/驾驶台",
		type: "slot",
		name: "bridge",
		span: 24,
		otherAttr: {
			isDate: true
		}
	},
	{
		label: "",
		type: "slot",
		name: "bridgeImg"
	},
	{
		label: "施工/作业/其他",
		type: "slot",
		name: "work",
		span: 24
	},
	{
		label: "",
		type: "slot",
		name: "workImg"
	},
	{
		label: "机舱/机器设备",
		type: "slot",
		name: "cabin",
		span: 24
	},
	{
		label: "",
		type: "slot",
		name: "cabinImg"
	},
	{
		label: "甲板/船体",
		type: "slot",
		name: "deck",
		span: 24
	},
	{
		label: "",
		type: "slot",
		name: "deckImg"
	},
	{
		label: "生活区（房间/厨房/餐厅/走廊等）",
		type: "slot",
		name: "life",
		span: 24
	},
	{
		label: "",
		type: "slot",
		name: "lifeImg"
	},
	{
		label: "消防救生设备",
		type: "slot",
		name: "lifesaving",
		span: 24
	},
	{
		label: "",
		type: "slot",
		name: "lifesavingImg"
	},
	{
		label: "文字说明",
		type: "detail",
		name: "description",
		span: 24
	}
];

function getDetailParam() {
	return { uuid: props.editData.uuid };
}

let isTrainInDetail = ref<YN>(YN.No);
const detailConfig: DetailConfigOptions = {
	detailApi: ShipReportService.detail,
	detailParam: getDetailParam,
	labelPosition: "left",
	labelWidth: "228px",
	items: detailContent,
	dataCallBack: data => {
		isTrainInDetail.value = data.isTrain;
		return data;
	}
};

//手动调用metaForm的初始化
function iniData() {
	if (props.mode === "detail") {
		detailRef.value.getDetailData();
		return;
	}
}

/**本组件中的dialog关闭不代表父组件控制弹窗用的变量改为了false。需要手动emit一下 */
function handleClosed() {
	close();
}

/**关闭弹窗，通知父组件 */
function close() {
	_visible.value = false;
}
</script>

<style scoped>
.img-container {
	display: flex;
	flex-wrap: wrap;
	justify-content: flex-start;
	width: 100%;
	.img-in-table {
		width: 100px;
		height: 100px;
		margin-right: 12px;
		margin-bottom: 12px;
	}
}
</style>
