<!-- 表格 -->
<template>
	<div style="height: 100%">
		<TableLayout ref="bannerTable" title="轮播图管理" :table-config="tableConfig" :filter-config="filterConfig">
			<template #button>
				<el-button type="primary" icon="plus" @click="handleBtnEdit('add')"> 添加轮播图 </el-button>
			</template>
			<template #isEnable="{ row }">
				<ShowTagCpn :is-enable="row.isEnable" />
			</template>
			<template #image="{ row }">
				<div>
					<el-image
						:src="row.image"
						:lazy="true"
						:preview-src-list="[row.image]"
						preview-teleported
						:hide-on-click-modal="true"
					/>
				</div>
			</template>
			<template #path="{ row }">
				<TagCpn :path="row.path" />
			</template>
			<template #opr="{ row }">
				<div>
					<el-link :underline="false" type="primary" style="margin-right: 12px" @click="handleBtnEdit('edit', row.uuid)"
						>编辑</el-link
					>
					<PopconfirmCpn :is-enable="row.isEnable" :uuid="row.uuid" @on-success="refresh" />
				</div>
			</template>
		</TableLayout>
		<!-- 添加/编辑成交信息弹出层 -->
		<DialogCpn v-model:showEdit="dialogEditIsShow" :mode="bannerMode" :uuid="bannerUuid" @on-success="refresh"></DialogCpn>
	</div>
</template>

<script setup lang="ts" name="BannerMgr">
import { ref, reactive, onMounted } from "vue";

// 组件
import TableLayout from "@/meta-components/MetaTable/index.vue";
import DialogCpn from "./Dialog.vue";
import ShowTagCpn from "./ShowTag.vue";
import TagCpn from "./Tag.vue";
import PopconfirmCpn from "./Popconfirm.vue";

// ts
import { TableConfig, ColumnProps, FilterConfigOptions } from "@/meta-components/MetaTable/interface";

// 接口
import { BannerService } from "@/api/modules/banner";

import { useTableScrollMemory } from "@/hooks/useTableScrollMemory";

const bannerTable = ref();

const columnsConfig: Array<ColumnProps> = [
	{
		label: "是否展示",
		slotName: "isEnable",
		width: "65px"
	},
	{
		label: "名称",
		prop: "name"
	},
	{
		label: "图片",
		slotName: "image"
	},
	{
		label: "排序值",
		prop: "sortNum",
		width: "65px"
	},
	{
		label: "跳转链接",
		slotName: "path",
		width: "65px"
	},
	{
		label: "备注",
		prop: "remark"
	},
	{
		label: "创建时间",
		prop: "createdAt",
		width: "140px"
	},
	{
		label: "操作",
		slotName: "opr"
	}
];

const tableConfig: TableConfig = reactive({
	key: "banner-mgr",
	columns: columnsConfig,
	requestApi: BannerService.getList,
	selectType: "none",
	selectId: "uuid",
	pagination: true,
	staticParam: {},
	defaultExpandAll: false
});

const filterConfig: Array<FilterConfigOptions> = reactive([
	{
		name: "name",
		type: "input",
		span: 3,
		placeholder: "名称"
	},
	{
		name: "createdAt",
		type: "dateRange",
		span: 4,
		placeholder: "创建时间"
	}
]);

/**banner编辑相关字段 */
let dialogEditIsShow = ref(false);
let bannerMode = ref("add" as "add" | "edit");
let bannerUuid = ref("");

// 添加或者编辑按钮点击事件 需要弹窗
function handleBtnEdit(mode: "add" | "edit", uuid: string = "") {
	bannerUuid.value = uuid;
	bannerMode.value = mode;
	dialogEditIsShow.value = true;
}

//  刷新表格数据
function refresh() {
	bannerTable.value.search();
}

onMounted(() => {
	useTableScrollMemory(bannerTable, "BannerMgr");
});
</script>

<style scoped></style>
