<!-- 船员数据页面 -->
<template>
	<div class="salary-analysis-container">
		<div class="header">
			<el-row :gutter="24">
				<el-col :span="4">
					<el-cascader v-model="selectPosition" :options="positionOptions" />
				</el-col>
				<el-col :span="4">
					<el-date-picker
						v-model="selectMonth"
						type="monthrange"
						range-separator="～"
						start-placeholder="开始月"
						end-placeholder="结束月"
						:shortcuts="shortcuts"
						value-format="YYYY-MM"
						:clearable="false"
						:disabled-date="disableDate"
					/>
				</el-col>
			</el-row>
		</div>

		<div class="charts-container">
			<div class="chart-item">
				<div
					class="charts-inner-container"
					:class="{
						'full-screen': fullScreenMode
					}"
				>
					<div class="chart-title">
						<span>{{ selectPositionText }}薪资趋势分析</span>
						<div>
							<el-icon class="show-all-icon" @click="showAll">
								<ZoomIn v-if="isShowAll" />
								<ZoomOut v-else />
							</el-icon>
							<el-icon class="download-icon" @click="tryDownload"><Download></Download></el-icon>
							<el-icon class="full-screen-icon" @click="switchFullScreen"><FullScreen></FullScreen></el-icon>
						</div>
					</div>
					<TrendChart ref="trendRef" title="" :data="trendData" />
				</div>
			</div>
			<div class="chart-item">
				<div class="chart-title">{{ selectPositionText }}薪资构成分布</div>
				<DistributionChart title="" :data="pieData" />
			</div>
		</div>
		<div class="container-mask" @click="switchFullScreen" v-if="fullScreenMode"></div>

		<div v-if="params.month && (params.shipNames || params.company)" class="table-container">
			<DetailTable />
		</div>
	</div>
</template>

<script setup lang="ts" name="SailorStatistics">
import TrendChart from "./components/TrendChart.vue";
import DistributionChart from "./components/DistributionChart.vue";
import DetailTable from "./components/DetailTable.vue";

import { ref, onMounted, reactive, watch, computed, provide } from "vue";
import { setMonth, format } from "date-fns";
import { StorageService } from "@/api/modules/power";
import { SalaryService } from "@/api/modules/salary";
import { SalaryItem } from "@/api/interface/salary.model";

const selectMonth = ref<any[]>([]);
const selectPosition = ref<string[]>([]);
const pieData = ref<any[]>([]);
const trendData = ref<any[]>([]);
const trendRef = ref();
let shipArr: string[] = [];
const shortcuts = [
	{
		text: "今年",
		value: [setMonth(new Date(), 0), new Date()]
	}
];
// 禁用未来的月份
const disableDate = (date: Date) => {
	return format(date, "yyyy-MM") > format(new Date(), "yyyy-MM");
};
const positionOptions: any[] = reactive([
	{
		value: "company",
		label: "按公司",
		children: []
	},
	{
		value: "ship",
		label: "按船舶",
		children: []
	}
]);
const params = computed(() => {
	const temp: SalaryItem.SalaryStatReq = {
		month: selectMonth.value
	};
	if (selectPosition.value[0] === "company") {
		temp.company = selectPosition.value[1];
	}
	if (selectPosition.value[0] === "ship") {
		temp.shipNames = shipArr;
	}
	return temp;
});
const selectPositionText = computed(() => params.value.company || (params.value.shipNames?.length ? "按船舶" : ""));
provide("params", params);

// 监听月份和船舶变化，重新获取数据
const fetchData = () => {
	SalaryService.getStatistic(params.value).then(res => {
		if (res.data) {
			const { lineList, pieData: salaryPie } = res.data;
			const {
				salaryWithAllowance = 0,
				overTimeSalary = 0,
				subsidy = 0,
				senioritySalary = 0,
				eatSubsidy = 0,
				companyProvidentFund = 0,
				companySocialSecurity = 0
			} = salaryPie;
			pieData.value = [
				["", "金额"],
				["应发工资", salaryWithAllowance],
				["补贴", subsidy],
				["加班", overTimeSalary],
				["工龄工资", senioritySalary],
				["伙食费", eatSubsidy],
				["社保（公司部分）", companySocialSecurity],
				["公积金（公司部分）", companyProvidentFund]
			];
			const positions = Array.from(new Set(lineList.map((item: any) => item.name)));
			const months = Array.from(new Set(lineList.map((item: any) => item.month))).sort();
			const lineArr = [["部门", ...months], ...positions.map((month: string) => [month, ...new Array(months.length).fill(0)])];
			for (let i = 0; i < lineList.length; i++) {
				const { month, name, endSalarySum } = lineList[i];
				const positionIndex = lineArr.findIndex(item => item[0] === name);
				const monthIndex = lineArr[0].findIndex(item => item === month);
				lineArr[positionIndex][monthIndex] = endSalarySum;
			}
			trendData.value = lineArr;
		}
	});
};
onMounted(() => {
	StorageService.getDetail({ key: "SalaryPosition" }).then(res => {
		if (res.code === 0 && res.data) {
			const companyPosition = JSON.parse(res.data.value);
			positionOptions[0].children = companyPosition.map((item: any) => ({
				value: item.value,
				label: item.name
			}));
			const firstShipIndex = companyPosition[0].children.findIndex((i: any) => /^[\u4e00-\u9fa5].*\d$/.test(i.name));
			shipArr = companyPosition[0].children.slice(firstShipIndex).map((item: any) => item.name);
			// 初始化默认值
			selectPosition.value = [positionOptions[0].value, positionOptions[0].children[0].value];
			selectMonth.value = [format(setMonth(new Date(), 0), "yyyy-MM"), format(new Date(), "yyyy-MM")];
		}
	});
});

watch(params, () => {
	isShowAll.value = false;
	fetchData();
});

let fullScreenMode = ref(false);
/**下载按钮 */
function tryDownload() {
	trendRef.value.downloadImage();
}
/**切换全屏 */
function switchFullScreen() {
	fullScreenMode.value = !fullScreenMode.value;
	setTimeout(() => {
		trendRef.value.showDataZoom(!fullScreenMode.value);
		trendRef.value.echartsResize();
	}, 200);
}
const isShowAll = ref(false);

/** 滚动条进度缩放 */
function showAll() {
	if (isShowAll.value) {
		isShowAll.value = false;
		trendRef.value.setDataZoom();
	} else {
		isShowAll.value = true;
		trendRef.value.setDataZoom(0, 100);
	}
}
</script>

<style scoped lang="scss">
.salary-analysis-container {
	display: flex;
	flex-direction: column;
	gap: 16px;
	padding: 16px;
	.charts-container {
		display: flex;
		gap: 16px;
		height: 350px;
		.chart-item {
			flex: 1;
			padding: 16px;
			background-color: #ffffff;
			border-radius: 4px;
		}
	}
	.charts-inner-container {
		height: 100%;
		transition: all 0.2s;
		&.full-screen {
			position: fixed;
			top: 50%;
			left: 50%;
			z-index: 10;
			width: 80vw;
			height: 80vh;
			padding: 16px;
			background-color: #ffffff;
			border-radius: 10px;
			transform: translate(-50%, -50%);
		}
	}
	.table-container {
		flex: 1;
		padding: 16px;
		overflow: hidden;
		background-color: #ffffff;
		border-radius: 4px;
	}
}
.chart-title {
	position: relative;
	display: flex;
	justify-content: space-between;
}
.full-screen-icon {
	position: absolute;
	right: 14px;
	width: 20px;
	height: 20px;
	font-size: 20px;
	color: $hint-text-color;
	cursor: pointer;
}
.download-icon {
	position: absolute;
	right: 42px;
	width: 20px;
	height: 20px;
	font-size: 20px;
	color: $hint-text-color;
	cursor: pointer;
}
.container-mask {
	position: fixed;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
	z-index: 4;
	background-color: rgb(0 0 0 / 80%);
}
.show-all-icon {
	position: absolute;
	right: 68px;
	width: 20px;
	height: 20px;
	font-size: 20px;
	font-weight: 200;
	color: $hint-text-color;
}
</style>
