<!-- 薪资趋势柱状图图 -->
<template>
	<div ref="oilChart" id="trend-chart" style="height: 95%; max-height: 80vh" class="content-box"></div>
</template>

<script setup lang="ts" name="columnChart">
import { ref, onMounted, PropType, toRef, watch } from "vue";
import { useEcharts } from "@/hooks/useEcharts";
const dataZoom: any = [
	{
		type: "slider",
		yAxisIndex: 0,
		orient: "vertical",
		right: "5%", // 调整位置避免遮挡
		start: 0,
		end: 22,
		// 样式优化
		backgroundColor: "#f5f5f5",
		fillerColor: "rgba(100,149,237,0.2)",
		handleSize: "100%", // 操作柄大小
		handleStyle: { color: "#6495ED" },
		zoomLock: true // 锁定缩放
	}
];
/**
 * 柱状图配置
 */
const option_Bar_Stack: echarts.EChartsOption = {
	tooltip: {
		trigger: "axis",
		axisPointer: {
			type: "line",
			label: {
				show: true
			}
		},
		valueFormatter: value => Number(value).toFixed(2),
		position: function (pos) {
			// 显示到鼠标右下侧
			const obj = { top: pos[1], left: pos[0] };
			return obj;
		}
	},
	legend: {
		type: "scroll",
		padding: 10,
		top: "top",
		right: "right",
		width: "63%",
		pageButtonPosition: "end",
		pageIconSize: 12,
		pageTextStyle: {
			color: "#999"
		}
	},
	grid: {
		left: "3%",
		right: "4%",
		bottom: "7%",
		containLabel: true
	},
	xAxis: {
		type: "value"
	},
	yAxis: {
		type: "category"
	},
	series: [],
	dataset: {
		source: []
	},
	dataZoom
};
const oilChart = ref<HTMLElement>();

/**入参
 * title：饼图标题，将显示在正圆内
 * data数据：以dataset的格式
 */
const props = defineProps({
	data: { type: Object as PropType<any[][]>, required: true }
});

/**引入config中的饼图配置，并拼接title和dataset */
let option = ref<any>({
	...option_Bar_Stack
});

let dataRef = toRef(props, "data");
let series = ref([] as any[]);
/** 设置 柱状图缩放条比例 */
const visibleItems = 5; // 默认显示的条数
const setDataZoom = (start?: number, end?: number) => {
	if (start === undefined && end === undefined) {
		// 根据数据条数动态调整 dataZoom
		const dataLength = props.data.length - 1; // 减去表头行
		if (dataLength > visibleItems) {
			// 计算 end 百分比，使得只显示指定数量的条目
			const endPercent = (visibleItems / dataLength) * 100;

			// 更新 dataZoom 配置
			option.value.dataZoom[0].end = endPercent;
		} else {
			// 数据条目较少时，显示全部
			option.value.dataZoom[0].end = 100;
		}
	} else {
		option.value.dataZoom[0].start = start;
		option.value.dataZoom[0].end = end;
	}
};
watch(
	() => props.data,
	(newV: any) => {
		series.value = [];
		if (!newV || newV.length === 0) return;

		let l = newV[0].length - 1;
		if (l <= 0) return;

		for (let i = 0; i < l; i++) {
			series.value.push({
				type: "bar",
				labelLayout: {
					hideOverlap: true
				},

				stack: "total",
				emphasis: {
					focus: "series"
				}
				// barWidth: 18 // 固定柱子宽度
			});
		}
		setDataZoom();
	},
	{
		immediate: true
	}
);
/** 显示/隐藏 柱状图缩放条 */
const showDataZoom = (isShow: boolean) => {
	option.value.dataZoom = isShow ? dataZoom : [];
	option.value.legend = isShow
		? {
				type: "scroll",
				padding: 10,
				top: "top",
				right: "right",
				width: "63%",
				pageButtonPosition: "end",
				pageIconSize: 12,
				pageTextStyle: {
					color: "#999"
				}
		  }
		: {
				type: "plain",
				padding: 10,
				top: "top",
				right: "right",
				width: "63%"
		  };
};
/**使用封装的hook，自动实现echart相关功能 */
let { echartsResize, downloadImage } = useEcharts({
	chartName: "薪资趋势分析表",
	chartRef: oilChart,
	options: option,
	datasetSource: dataRef,
	series: series
});
defineExpose({ echartsResize, downloadImage, showDataZoom, setDataZoom });

onMounted(() => {});
</script>

<style scoped lang="scss"></style>
