<!-- 薪资构成分布饼图 -->
<template>
	<div class="distribution-chart" style="height: 100%">
		<div class="content-box" style="width: 100%" ref="chartRef"></div>
	</div>
</template>

<script setup lang="ts" name="columnChart">
import { PropType, ref, toRef } from "vue";
import { useEcharts } from "@/hooks/useEcharts";

const option: echarts.EChartsOption = {
	color: ["#FAC858", "#507AFC", "#93BEFF", "#FF6B6B", "#63D354", "#A66CFF", "#50E3C2"],
	tooltip: {
		trigger: "item"
	},
	title: {
		left: "center",
		top: "center",
		textStyle: {
			fontWeight: "normal",
			fontSize: "14px"
		}
	},
	grid: {
		left: "0%",
		right: "0%",
		bottom: "7%",
		containLabel: true
	},
	legend: {
		orient: "vertical",
		right: "0%",
		top: "center"
	},
	series: [
		{
			type: "pie",
			label: {
				show: false,
				overflow: "break",
				fontSize: "14px",
				lineHeight: 20,
				formatter(param: any) {
					return param.name + "(" + param.percent + "%)";
				}
			}
		}
	],
	dataset: {
		source: []
	}
};
const chartRef = ref<HTMLElement>();
/**入参
 * title：饼图标题，将显示在正圆内
 * data数据：以dataset的格式
 */
const props = defineProps({
	title: { type: String, required: true },
	data: { type: Object as PropType<any[][]>, required: true },
	clickFuntion: { type: Object as PropType<(params?: any) => any> }
});

const dataRef = toRef(props, "data");

useEcharts({ chartRef: chartRef, options: option, datasetSource: dataRef, clickFunction: props.clickFuntion });
</script>

<style scoped lang="scss"></style>
