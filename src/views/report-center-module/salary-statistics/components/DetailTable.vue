<!-- 详情财务数据表 -->
<template>
	<div class="detail-table">
		<TableLayout ref="tableRef" title="详细财务数据" :table-config="tableConfig" :export-config="exportConfig">
			<template #department="{ row }">
				<span>{{ row.department }}</span>
			</template>
		</TableLayout>
	</div>
</template>

<script setup lang="ts">
import { inject, reactive, Ref, watch, ref } from "vue";
import TableLayout from "@/meta-components/MetaTable/index.vue";
import { ColumnProps, TableConfig, ExportConfig } from "@/meta-components/MetaTable/interface";
import { SalaryService } from "@/api/modules/salary";
import { SalaryItem } from "@/api/interface/salary.model";

const params = inject("params") as Ref<{ month: string[]; company: string; shipName: string }>;
const tableRef = ref();
let columnsConfig: Array<ColumnProps> = [
	{
		label: "月份",
		prop: "month"
	},
	{
		label: "部门/船舶名称",
		slotName: "department"
	},
	{
		label: "应发工资",
		prop: "salaryWithAllowance"
	},
	{
		label: "个人五险一金",
		prop: "personalInsurance"
	},
	{
		label: "企业五险一金",
		prop: "companyInsurance"
	},
	{
		label: "津贴",
		prop: "allowance"
	},
	{
		label: "出海补贴",
		prop: "seaSubsidy"
	},
	{
		label: "工龄工资",
		prop: "senioritySalary"
	},
	{
		label: "加班",
		prop: "overtimeSalary"
	},
	{
		label: "高温",
		prop: "highTempSubsidy"
	},
	{
		label: "伙食",
		prop: "eatSubsidy"
	},
	{
		label: "总计",
		prop: "salaryTotal"
	}
];

let tableConfig: TableConfig = reactive({
	key: "import-error-table",
	// mode: "mini",
	columns: columnsConfig,
	requestApi: p => {
		return SalaryService.getFinanceList({ ...p, ...params.value });
	},
	selectType: "none",
	selectId: "uuid",
	pagination: true,
	pageSize: 10,
	showTitleArea: true,
	canChangeHead: false
});
let exportConfig = reactive<ExportConfig<SalaryItem.DetailSalary[]>>({
	fileName: "详细财务数据表",
	formatRule: {
		// status: data => {
		// 	return getShipInsStatusTagInfo(data.invalidTime!, data.reminderTime).text;
		// },
		// invalidTime: data => {
		// 	if (!data.invalidTime) return "";
		// 	return format(parseISO(data.invalidTime), "yyyy/MM/dd");
		// },
		// validTime: data => {
		// 	if (!data.validTime) return "";
		// 	return format(parseISO(data.validTime), "yyyy/MM/dd");
		// }
	}
});
// 获取表格数据...
watch(params, () => {
	tableRef.value.search();
});
</script>

<style scoped lang="scss">
.detail-table {
	height: 100%;
	.table-title-text {
		margin-bottom: 16px;
		font-size: 16px;
		font-weight: 500;
	}
}
</style>
