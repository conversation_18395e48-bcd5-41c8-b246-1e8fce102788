<!-- 船员数据页面 -->
<template>
	<div style="height: calc(100% - 24px); overflow-y: overlay" class="page-container">
		<div class="export-btn">
			<div class="salary-btn">
				<el-button @click="handleClickExport" :disabled="!workDataCpn || !workDataCpn.hasData">导出表格</el-button>
			</div>
			<div v-if="hasAuth('薪资基础配置')" class="salary-btn">
				<el-button @click="openSalaryOptionModal">基础配置</el-button>
			</div>
			<div v-if="hasAuth('工资表创建')" class="salary-btn">
				<el-button @click="jumpToCreate" type="primary" :disabled="option.corpId === 0">创建工资单</el-button>
			</div>
		</div>
		<div class="table-class" style="height: calc(100% - 63px)">
			<div style="height: 100%">
				<WorkDateCpn ref="workDataCpn" />
			</div>
		</div>
		<SalaryOptionModal ref="modalRef" @on-success="getOption" />
	</div>
</template>

<script setup lang="ts" name="SailorSalary">
import { ref, onMounted, provide } from "vue";
import WorkDateCpn from "./components/WorkDateTable.vue";
import { SalaryOptionService } from "@/api/modules/salary";
import SalaryOptionModal from "./components/SalaryOptionModal.vue";
import { useRouter } from "vue-router";

import { GlobalStore } from "@/store";
import { ElMessage } from "element-plus";

import { hasAuth } from "@/utils/util";
import { format } from "date-fns";
import { exportSheetExcel } from "./utils";
import { SalaryStore } from "@/store/modules/salary";
import { storeToRefs } from "pinia";

const globalStore = GlobalStore();
const salaryStore = SalaryStore();
const { option } = storeToRefs(salaryStore);
const router = useRouter();

// 获取组件 tabs切换时重置数据
const workDataCpn = ref();

const corpName = ref("");
const modalRef = ref();

provide("corpName", corpName);
// 导出报表
function handleClickExport() {
	// 根据公司名拆分导出多个sheet
	//@ts-ignore
	const sheet = JSON.parse(JSON.stringify(luckysheet.getluckysheetfile()));
	const sheetData = sheet[0].data;
	const companyDataMap: Map<string, any[]> = new Map();
	let startIndex = 0,
		endIndex = 0;
	for (let i = 0; i < sheetData.length; i++) {
		const column = sheetData[i];
		const headerIndex = column.findIndex((item: any) => item?.v === "工资分摊岗位");
		if (headerIndex !== -1) {
			endIndex = i - 2; //前一张表的结尾序号
			if (startIndex !== 0 || endIndex > 0) {
				// 处理前一张表的数据
				companyDataMap.set(sheetData[startIndex]?.[0].v, sheetData.slice(startIndex + 1, endIndex + 1));
			}
			startIndex = i - 1; //下一张表的开始序号
		}
	}
	endIndex = sheetData.findIndex((item: any) => item.every((cell: any) => !cell));
	// 处理最后一张表的数据
	companyDataMap.set(
		sheetData[startIndex]?.[0].v,
		sheetData.slice(startIndex + 1, endIndex === -1 ? sheetData.length : endIndex)
	);
	sheet[0].config.rowlen = {};
	companyDataMap.forEach((list, companyName) => {
		sheet[0].data = list;
		const merge: { [key: string]: any } = {};
		list.forEach((column, rowIndex) => {
			column.forEach((cell: any, columnIndex: number) => {
				if (cell && cell.mc && (cell.mc.rs || cell.mc.cs)) {
					merge[`${rowIndex}_${columnIndex}`] = {
						r: rowIndex,
						c: columnIndex,
						rs: cell.mc.rs || 1,
						cs: cell.mc.cs || 1
					};
				}
			});
			// 导出时删除公式
			column.forEach((cell: any) => {
				if (cell && cell.f && cell.f !== "") {
					delete cell.f;
				}
			});
			// //按公司分表后 转换原公式
			// if (rowIndex === list.length - 1) {
			// 	column.forEach((cell: any) => {
			// 		if (cell && cell.f && cell.f !== "") {
			// 			cell.f = replaceSumFormula(cell.f, 3, list.length - 1);
			// 		}
			// 	});
			// }
		});
		sheet[0].config.merge = merge;
		exportSheetExcel(sheet, `${format(workDataCpn.value.getMonth(), "yyyy年MM月")}工资表-${companyName}`);
	});
	// exportSheetExcel(sheet, `${format(workDataCpn.value.getMonth(), "yyyy年MM月")}工资表`);
	ElMessage.success("导出成功");
}
//打开配置弹窗
const openSalaryOptionModal = () => {
	modalRef.value && modalRef.value.open();
};
//跳转创建
const jumpToCreate = () => {
	router.push("/report-center/sailor-salary/add-salary");
};

const getOption = async () => {
	const { code, data } = await SalaryOptionService.getDetail();
	if (code === 0 && data) {
		option.value = data;
	}
};
onMounted(() => {
	getOption();
	corpName.value = globalStore.userInfo.corpName;
});
</script>

<style scoped lang="scss">
.page-container {
	position: relative;
	.export-btn {
		position: absolute;
		right: 12px;
		z-index: 999;
		display: flex;
		.salary-btn {
			margin-right: 12px;
		}
	}
}
</style>
