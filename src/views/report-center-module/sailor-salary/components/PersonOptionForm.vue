<!-- 薪资基础配置 - 个人部分 -->
<template>
	<MetaForm
		mode="edit"
		ref="form"
		:formConfig="formConfig"
		:styleConfig="{ mode: 'drawer', labelPosition: 'left', labelWidth: '110px' }"
	>
		<template #button><div></div></template>
	</MetaForm>
</template>
<script setup lang="ts">
import { ref, computed, watch, nextTick } from "vue";
import MetaForm from "@/meta-components/MetaForm/index.vue";
import { FormConfigOptions } from "@/meta-components/MetaForm/interface";
import { formContent, DEFAULT_OPTION } from "./config/personFormConfig";
import { useTransExcelFile } from "./useTransExcelFile";
import { cloneDeep } from "lodash";

const props = defineProps<{
	formData: Record<string, any>;
}>();

const form = ref();

const formConfig: FormConfigOptions = {
	key: "person-salary-option",
	items: formContent
};

const validate = async () => {
	return await form.value?.validate();
};

watch(
	() => props.formData,
	newVal => {
		nextTick(() => {
			form.value.formData = {
				...newVal
			};
		});
	}
);

const formData = computed(() => {
	return form.value?.formData ?? {};
});
const { transData: salaryOrder } = useTransExcelFile(form, "salaryOrderAttaches", { name: "姓名", order: "序号" });

const transformData = () => {
	const rawData = cloneDeep(formData.value);
	const { salaryOrderAttaches, ...other } = rawData;
	// 第一次保存 未填值的属性直接取默认值
	const dataWithDefault = Object.keys(other).reduce((obj, key) => {
		obj[key] = rawData[key] === "" ? DEFAULT_OPTION[key] : rawData[key];
		return obj;
	}, {} as any);
	// 转换工资单顺序表
	if (salaryOrderAttaches && salaryOrderAttaches.length) {
		dataWithDefault.salaryOrderAttaches = salaryOrderAttaches;
		if (salaryOrder.value) {
			dataWithDefault.salaryOrder = salaryOrder.value.sort((a: any, b: any) => a.order - b.order).map((i: any) => i.name);
		}
	}
	return dataWithDefault;
};
// 获取转换后的表单数据的方法
const getFormData = () => {
	return transformData();
};
defineExpose({ validate, getFormData });
</script>
<style lang="scss" scoped></style>
