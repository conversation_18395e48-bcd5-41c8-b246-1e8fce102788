import { FormItemOptions } from "@/meta-components/MetaForm/interface";

const DEFAULT_OPTION = {
	/** 养老保险-船务公司 */
	pensionInsurance_shipBusiness: "769.92",
	/** 医疗保险-船务公司 */
	medicalInsurance_shipBusiness: "409.02",
	/** 失业保险-船务公司 */
	unemployInsurance_shipBusiness: "24.06",
	/** 工伤保险-船务公司 */
	workersInsurance_shipBusiness: "26.47",
	/** 公积金-船务公司 */
	providentFund_shipBusiness: "125.00",
	/** 养老保险-船舶公司 */
	pensionInsurance_ship: "769.92",
	/** 医疗保险-船舶公司 */
	medicalInsurance_ship: "409.02",
	/** 失业保险-船舶公司 */
	unemployInsurance_ship: "24.06",
	/** 工伤保险-船舶公司 */
	workersInsurance_ship: "21.65",
	/** 公积金-船舶公司 */
	providentFund_ship: "125.00",
	/** 养老保险-物流公司 */
	pensionInsurance_logistics: "769.92",
	/** 医疗保险-物流公司 */
	medicalInsurance_logistics: "409.02",
	/** 失业保险-物流公司 */
	unemployInsurance_logistics: "24.06",
	/** 工伤保险-物流公司 */
	workersInsurance_logistics: "26.47",
	/** 公积金-物流公司 */
	providentFund_logistics: "125.00",
	/** 养老保险-野渡公司 */
	pensionInsurance_yeedo: "769.92",
	/** 医疗保险-野渡公司 */
	medicalInsurance_yeedo: "409.02",
	/** 失业保险-野渡公司 */
	unemployInsurance_yeedo: "24.06",
	/** 工伤保险-野渡公司 */
	workersInsurance_yeedo: "9.62",
	/** 公积金-野渡公司 */
	providentFund_yeedo: "125.00"
} as any;
export const formContent: Array<FormItemOptions> = [
	{ label: "船务公司企业部分五险一金配置", type: "title", name: "" },
	{
		label: "养老保险",
		type: "input",
		placeholder: DEFAULT_OPTION.pensionInsurance_shipBusiness,
		name: "pensionInsurance_shipBusiness",
		span: 6,
		otherAttr: {
			precision: 2,
			inputOnlyNumber: true,
			inputOnlyNonnegativeNumber: true,
			append: "元/月",
			showWordLimit: false
		}
	},
	{
		label: "医疗保险",
		type: "input",
		placeholder: DEFAULT_OPTION.medicalInsurance_shipBusiness,
		name: "medicalInsurance_shipBusiness",
		span: 6,
		otherAttr: {
			precision: 2,
			inputOnlyNumber: true,
			inputOnlyNonnegativeNumber: true,
			append: "元/月",
			showWordLimit: false
		}
	},
	{
		label: "失业保险",
		type: "input",
		placeholder: DEFAULT_OPTION.unemployInsurance_shipBusiness,
		name: "unemployInsurance_shipBusiness",
		span: 6,
		otherAttr: {
			precision: 2,
			inputOnlyNumber: true,
			inputOnlyNonnegativeNumber: true,
			append: "元/月",
			showWordLimit: false
		}
	},
	{
		label: "工伤保险",
		type: "input",
		placeholder: DEFAULT_OPTION.workersInsurance_shipBusiness,
		name: "workersInsurance_shipBusiness",
		span: 6,
		otherAttr: {
			precision: 2,
			inputOnlyNumber: true,
			inputOnlyNonnegativeNumber: true,
			append: "元/月",
			showWordLimit: false
		}
	},
	{
		label: "公积金",
		type: "input",
		placeholder: DEFAULT_OPTION.providentFund_shipBusiness,
		name: "providentFund_shipBusiness",
		span: 6,
		otherAttr: {
			precision: 2,
			inputOnlyNumber: true,
			inputOnlyNonnegativeNumber: true,
			append: "元/月",
			showWordLimit: false
		}
	},
	{ label: "船舶公司企业部分五险一金配置", type: "title", name: "" },
	{
		label: "养老保险",
		type: "input",
		placeholder: DEFAULT_OPTION.pensionInsurance_ship,
		name: "pensionInsurance_ship",
		span: 6,
		otherAttr: {
			precision: 2,
			inputOnlyNumber: true,
			inputOnlyNonnegativeNumber: true,
			append: "元/月",
			showWordLimit: false
		}
	},
	{
		label: "医疗保险",
		type: "input",
		placeholder: DEFAULT_OPTION.medicalInsurance_ship,
		name: "medicalInsurance_ship",
		span: 6,
		otherAttr: {
			precision: 2,
			inputOnlyNumber: true,
			inputOnlyNonnegativeNumber: true,
			append: "元/月",
			showWordLimit: false
		}
	},
	{
		label: "失业保险",
		type: "input",
		placeholder: DEFAULT_OPTION.unemployInsurance_ship,
		name: "unemployInsurance_ship",
		span: 6,
		otherAttr: {
			precision: 2,
			inputOnlyNumber: true,
			inputOnlyNonnegativeNumber: true,
			append: "元/月",
			showWordLimit: false
		}
	},
	{
		label: "工伤保险",
		type: "input",
		placeholder: DEFAULT_OPTION.workersInsurance_ship,
		name: "workersInsurance_ship",
		span: 6,
		otherAttr: {
			precision: 2,
			inputOnlyNumber: true,
			inputOnlyNonnegativeNumber: true,
			append: "元/月",
			showWordLimit: false
		}
	},
	{
		label: "公积金",
		type: "input",
		placeholder: DEFAULT_OPTION.providentFund_ship,
		name: "providentFund_ship",
		span: 6,
		otherAttr: {
			precision: 2,
			inputOnlyNumber: true,
			inputOnlyNonnegativeNumber: true,
			append: "元/月",
			showWordLimit: false
		}
	},
	{ label: "航运物流公司企业部分五险一金配置", type: "title", name: "" },
	{
		label: "养老保险",
		type: "input",
		placeholder: DEFAULT_OPTION.pensionInsurance_logistics,
		name: "pensionInsurance_logistics",
		span: 6,
		otherAttr: {
			precision: 2,
			inputOnlyNumber: true,
			inputOnlyNonnegativeNumber: true,
			append: "元/月",
			showWordLimit: false
		}
	},
	{
		label: "医疗保险",
		type: "input",
		placeholder: DEFAULT_OPTION.medicalInsurance_logistics,
		name: "medicalInsurance_logistics",
		span: 6,
		otherAttr: {
			precision: 2,
			inputOnlyNumber: true,
			inputOnlyNonnegativeNumber: true,
			append: "元/月",
			showWordLimit: false
		}
	},
	{
		label: "失业保险",
		type: "input",
		placeholder: DEFAULT_OPTION.unemployInsurance_logistics,
		name: "unemployInsurance_logistics",
		span: 6,
		otherAttr: {
			precision: 2,
			inputOnlyNumber: true,
			inputOnlyNonnegativeNumber: true,
			append: "元/月",
			showWordLimit: false
		}
	},
	{
		label: "工伤保险",
		type: "input",
		placeholder: DEFAULT_OPTION.workersInsurance_logistics,
		name: "workersInsurance_logistics",
		span: 6,
		otherAttr: {
			precision: 2,
			inputOnlyNumber: true,
			inputOnlyNonnegativeNumber: true,
			append: "元/月",
			showWordLimit: false
		}
	},
	{
		label: "公积金",
		type: "input",
		placeholder: DEFAULT_OPTION.providentFund_logistics,
		name: "providentFund_logistics",
		span: 6,
		otherAttr: {
			precision: 2,
			inputOnlyNumber: true,
			inputOnlyNonnegativeNumber: true,
			append: "元/月",
			showWordLimit: false
		}
	},
	{ label: "野渡公司企业部分五险一金配置", type: "title", name: "" },
	{
		label: "养老保险",
		type: "input",
		placeholder: DEFAULT_OPTION.pensionInsurance_yeedo,
		name: "pensionInsurance_yeedo",
		span: 6,
		otherAttr: {
			precision: 2,
			inputOnlyNumber: true,
			inputOnlyNonnegativeNumber: true,
			append: "元/月",
			showWordLimit: false
		}
	},
	{
		label: "医疗保险",
		type: "input",
		placeholder: DEFAULT_OPTION.medicalInsurance_yeedo,
		name: "medicalInsurance_yeedo",
		span: 6,
		otherAttr: {
			precision: 2,
			inputOnlyNumber: true,
			inputOnlyNonnegativeNumber: true,
			append: "元/月",
			showWordLimit: false
		}
	},
	{
		label: "失业保险",
		type: "input",
		placeholder: DEFAULT_OPTION.unemployInsurance_yeedo,
		name: "unemployInsurance_yeedo",
		span: 6,
		otherAttr: {
			precision: 2,
			inputOnlyNumber: true,
			inputOnlyNonnegativeNumber: true,
			append: "元/月",
			showWordLimit: false
		}
	},
	{
		label: "工伤保险",
		type: "input",
		placeholder: DEFAULT_OPTION.workersInsurance_yeedo,
		name: "workersInsurance_yeedo",
		span: 6,
		otherAttr: {
			precision: 2,
			inputOnlyNumber: true,
			inputOnlyNonnegativeNumber: true,
			append: "元/月",
			showWordLimit: false
		}
	},
	{
		label: "公积金",
		type: "input",
		placeholder: DEFAULT_OPTION.providentFund_yeedo,
		name: "providentFund_yeedo",
		span: 6,
		otherAttr: {
			precision: 2,
			inputOnlyNumber: true,
			inputOnlyNonnegativeNumber: true,
			append: "元/月",
			showWordLimit: false
		}
	}
];

// 子公司key
export enum CorpEnum {
	"船务公司" = "shipBusiness",
	"船舶公司" = "ship",
	"物流公司" = "logistics",
	"野渡公司" = "yeedo"
}

// 子公司key反转

export enum ReverseCorpEnum {
	"shipBusiness" = "船务公司",
	"ship" = "船舶公司",
	"logistics" = "物流公司",
	"yeedo" = "野渡公司"
}

//社保字段
export const socialFields = ["pensionInsurance", "medicalInsurance", "unemployInsurance", "workersInsurance", "providentFund"];
