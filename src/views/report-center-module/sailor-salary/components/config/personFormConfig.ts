import { FormItemOptions } from "@/meta-components/MetaForm/interface";
import { UploadService } from "@/api/modules/upload";
/** 薪资配置默认值 */
export const DEFAULT_OPTION: { [key: string]: string } = {
	/** 临时工日薪 */
	daySalary: "500",
	/** 临时工每日工时 */
	dailyHourTempMan: "8.5",
	/** 工人每日工时 */
	dailyHourWorkMan: "8.5",
	/** 工人每月带薪休假 */
	restDayWorkMan: "2",
	/** 船员休假薪资 */
	shipManMinSalary: "2490",
	/** 船员每日工时 */
	dailyHourShipMan: "8.5",
	/** 船员每年带薪休假 */
	restDayShipMan: "30",
	/** 船员伙食补贴 */
	eatSubsidyShipMan: "25",
	/** 出海补贴 */
	seaSubsidy: "50",
	/** 作业负责 */
	workSubsidy: "80",
	/** 管理每日工时 */
	dailyHourOfficer: "8",
	/** 网科部每日工时 */
	dailyHourInternet: "7.5",
	/** 网科部每日饭补 */
	eatSubsidyInternet: "18",
	/** 基本工资 */
	basicSalary: "2500",
	/** 社保 */
	socialSecurity: "384.96",
	/** 医疗保险 */
	medicalInsurance: "96.24",
	/** 失业保险 */
	unemployInsurance: "24.06",
	/** 公积金 */
	providentFund: "125.00"
};

export const formContent: Array<FormItemOptions> = [
	{ label: "临时工薪资参数", type: "title", name: "" },
	{
		label: "薪资",
		type: "input",
		placeholder: DEFAULT_OPTION.daySalary,
		name: "daySalary",
		span: 6,
		otherAttr: {
			precision: 1,
			inputOnlyNumber: true,
			inputOnlyNonnegativeNumber: true,
			append: "元/天",
			showWordLimit: false
		}
	},
	{
		label: "每日工时",
		type: "input",
		placeholder: DEFAULT_OPTION.dailyHourTempMan,
		name: "dailyHourTempMan",
		span: 6,
		otherAttr: {
			precision: 1,
			inputOnlyNumber: true,
			inputOnlyNonnegativeNumber: true,
			append: "h",
			showWordLimit: false
		}
	},
	{ label: "工人薪资参数", type: "title", name: "" },
	{
		label: "每日工时",
		type: "input",
		placeholder: DEFAULT_OPTION.dailyHourWorkMan,
		name: "dailyHourWorkMan",
		span: 6,
		otherAttr: {
			precision: 1,
			inputOnlyNumber: true,
			inputOnlyNonnegativeNumber: true,
			append: "h",
			showWordLimit: false
		}
	},
	{
		label: "每月带薪休假(法定节假日外)",
		type: "input",
		placeholder: DEFAULT_OPTION.restDayWorkMan,
		name: "restDayWorkMan",
		span: 6,
		otherAttr: {
			precision: 1,
			inputOnlyNumber: true,
			inputOnlyNonnegativeNumber: true,
			append: "天",
			showWordLimit: false
		}
	},
	{ label: "船员薪资参数", type: "title", name: "" },
	{
		label: "船员休假薪资",
		type: "input",
		placeholder: DEFAULT_OPTION.shipManMinSalary,
		name: "shipManMinSalary",
		span: 6,
		otherAttr: {
			precision: 0,
			inputOnlyNumber: true,
			inputOnlyNonnegativeNumber: true,
			append: "元/月",
			showWordLimit: false
		}
	},
	{
		label: "每日工时",
		type: "input",
		placeholder: DEFAULT_OPTION.dailyHourShipMan,
		name: "dailyHourShipMan",
		span: 6,
		otherAttr: {
			precision: 1,
			inputOnlyNumber: true,
			inputOnlyNonnegativeNumber: true,
			append: "h",
			showWordLimit: false
		}
	},
	{
		label: "每年带薪休假(法定节假日外)",
		type: "input",
		placeholder: DEFAULT_OPTION.restDayShipMan,
		name: "restDayShipMan",
		span: 6,
		otherAttr: {
			precision: 0,
			inputOnlyNumber: true,
			inputOnlyNonnegativeNumber: true,
			append: "天",
			showWordLimit: false
		}
	},
	{
		label: "伙食补贴",
		type: "input",
		placeholder: DEFAULT_OPTION.eatSubsidyShipMan,
		name: "eatSubsidyShipMan",
		span: 6,
		otherAttr: {
			precision: 0,
			inputOnlyNumber: true,
			inputOnlyNonnegativeNumber: true,
			append: "元/天",
			showWordLimit: false
		}
	},
	{ label: "管理薪资参数", type: "title", name: "" },
	{
		label: "每日工时",
		type: "input",
		placeholder: DEFAULT_OPTION.dailyHourOfficer,
		name: "dailyHourOfficer",
		span: 6,
		otherAttr: {
			precision: 1,
			inputOnlyNumber: true,
			inputOnlyNonnegativeNumber: true,
			append: "h",
			showWordLimit: false
		}
	},
	{ label: "网科部薪资参数", type: "title", name: "" },
	{
		label: "每日工时",
		type: "input",
		placeholder: DEFAULT_OPTION.dailyHourInternet,
		name: "dailyHourInternet",
		span: 6,
		otherAttr: {
			precision: 1,
			inputOnlyNumber: true,
			inputOnlyNonnegativeNumber: true,
			append: "h",
			showWordLimit: false
		}
	},
	{
		label: "每日饭补",
		type: "input",
		placeholder: DEFAULT_OPTION.eatSubsidyInternet,
		name: "eatSubsidyInternet",
		span: 6,
		otherAttr: {
			precision: 0,
			inputOnlyNumber: true,
			inputOnlyNonnegativeNumber: true,
			append: "元/天",
			showWordLimit: false
		}
	},
	{ label: "五险一金及补贴", type: "title", name: "" },
	{
		label: "基本工资",
		type: "input",
		placeholder: DEFAULT_OPTION.basicSalary,
		name: "basicSalary",
		span: 6,
		otherAttr: {
			precision: 2,
			inputOnlyNumber: true,
			inputOnlyNonnegativeNumber: true,
			append: "元/月",
			showWordLimit: false
		}
	},
	{
		label: "社保",
		type: "input",
		placeholder: DEFAULT_OPTION.socialSecurity,
		name: "socialSecurity",
		span: 6,
		otherAttr: {
			precision: 2,
			inputOnlyNumber: true,
			inputOnlyNonnegativeNumber: true,
			append: "元/月",
			showWordLimit: false
		}
	},
	{
		label: "医疗保险",
		type: "input",
		placeholder: DEFAULT_OPTION.medicalInsurance,
		name: "medicalInsurance",
		span: 6,
		otherAttr: {
			precision: 2,
			inputOnlyNumber: true,
			inputOnlyNonnegativeNumber: true,
			append: "元/月",
			showWordLimit: false
		}
	},
	{
		label: "失业保险",
		type: "input",
		placeholder: DEFAULT_OPTION.unemployInsurance,
		name: "unemployInsurance",
		span: 6,
		otherAttr: {
			precision: 2,
			inputOnlyNumber: true,
			inputOnlyNonnegativeNumber: true,
			append: "元/月",
			showWordLimit: false
		}
	},
	{
		label: "公积金",
		type: "input",
		placeholder: DEFAULT_OPTION.providentFund,
		name: "providentFund",
		span: 6,
		otherAttr: {
			precision: 2,
			inputOnlyNumber: true,
			inputOnlyNonnegativeNumber: true,
			append: "元/月",
			showWordLimit: false
		}
	},
	{
		label: "出海补贴",
		type: "input",
		placeholder: DEFAULT_OPTION.seaSubsidy,
		name: "seaSubsidy",
		span: 6,
		otherAttr: {
			precision: 0,
			inputOnlyNumber: true,
			inputOnlyNonnegativeNumber: true,
			append: "元/天",
			showWordLimit: false
		}
	},
	{
		label: "作业负责",
		type: "input",
		placeholder: DEFAULT_OPTION.workSubsidy,
		name: "workSubsidy",
		span: 6,
		otherAttr: {
			precision: 0,
			inputOnlyNumber: true,
			inputOnlyNonnegativeNumber: true,
			append: "元/天",
			showWordLimit: false
		}
	},
	{ label: "工资单顺序", type: "title", name: "" },
	{
		label: "工资单顺序表",
		type: "fileUpload",
		name: "salaryOrderAttaches",
		placeholder: "请上传考勤表",
		span: 18,
		api: UploadService.ossApi,
		hint: "仅支持xls，xlsx。",
		otherAttr: { showUploadPlaceholder: false, returnFile: true },
		imageOptions: {
			mode: "single",
			pathName: "salary-attendance",
			limitNum: 1,
			accept: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel"
		}
	}
];
