<!-- 薪资基础配置-企业部分 -->
<template>
	<MetaForm
		mode="edit"
		ref="form"
		:formConfig="formConfig"
		:styleConfig="{ mode: 'drawer', labelPosition: 'left', labelWidth: '110px' }"
	>
		<template #button></template>
	</MetaForm>
</template>
<script setup lang="ts">
import { ref, computed, watch, nextTick } from "vue";
import MetaForm from "@/meta-components/MetaForm/index.vue";
import { FormConfigOptions } from "@/meta-components/MetaForm/interface";
import { formContent, socialFields, CorpEnum } from "./config/corpFormConfig";

const props = defineProps<{
	formData: Record<string, any>;
}>();

const form = ref();

const formConfig: FormConfigOptions = {
	key: "corp-salary-option",
	items: formContent
};

const validate = async () => {
	return await form.value?.validate();
};

const formData = computed(() => {
	return form.value?.formData ?? {};
});

watch(
	() => props.formData,
	newVal => {
		nextTick(() => {
			const { corpSocialHousingOption } = newVal;
			const corpFormData = {} as any;
			corpSocialHousingOption?.forEach((item: any) => {
				const corpType = CorpEnum[item.name as keyof typeof CorpEnum];
				socialFields.forEach((filed: string) => {
					corpFormData[`${filed}_${corpType}`] = item[filed];
				});
			});

			form.value.formData = {
				...corpFormData
			};
		});
	},
	{ immediate: true }
);

defineExpose({ validate, formData });
</script>
<style lang="scss" scoped></style>
