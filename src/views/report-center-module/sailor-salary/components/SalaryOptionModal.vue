<!-- 基础配置弹窗 -->
<template>
	<el-dialog
		class="salary-option-modal"
		v-model="visible"
		title="基础配置"
		:destroy-on-close="true"
		width="1200px"
		@open="getData"
		top="30px"
	>
		<el-tabs class="option-tabs" @tab-click="handleSelect" v-model="tabKey">
			<el-tab-pane label="个人部分" name="person">
				<PersonOptionForm ref="personFormRef" :formData="formData" />
			</el-tab-pane>
			<el-tab-pane label="企业部分" name="corp">
				<div class="top-label" style="transform: translateY(-16px)">
					以下数据最新修改日期为：{{
						formData.updateTime && formData.updateTime !== "" ? format(new Date(formData.updateTime), "yyyy-MM-dd HH:mm:ss") : "-"
					}}
				</div>
				<CorpOptionForm ref="corpFormRef" :formData="formData" />
			</el-tab-pane>
		</el-tabs>
		<div class="dialog-button-group">
			<el-button @click="close">关闭</el-button>
			<el-button type="primary" @click="handleSave">保存</el-button>
		</div>
	</el-dialog>
</template>
<script setup lang="ts">
import { ref } from "vue";
import { SalaryOptionService } from "@/api/modules/salary";
import PersonOptionForm from "./PersonOptionForm.vue";
import CorpOptionForm from "./CorpOptionForm.vue";
import { ReverseCorpEnum } from "./config/corpFormConfig";
import { DEFAULT_OPTION } from "./config/personFormConfig";
import { format } from "date-fns";
const visible = ref<boolean>(false);
const formData = ref<any>({});
const tabKey = ref("person");
const personFormRef = ref();
const corpFormRef = ref();

const handleSelect = (key: any) => {
	tabKey.value = key.props.name;
};

const getData = async () => {
	try {
		const { data } = await SalaryOptionService.getDetail();
		formData.value = data;
	} catch (error) {
		console.error("获取数据失败:", error);
	}
};

function normalizeObject(obj: any) {
	if (obj === null || typeof obj !== "object") {
		return JSON.stringify(obj);
	}
	const sortedKeys = Object.keys(obj).sort();
	const keyValuePairs: string[] = sortedKeys.map(key => {
		const value = normalizeObject(obj[key]);
		return `"${key}":${value}`;
	});
	return `{${keyValuePairs.join(",")}}`;
}
/** 比较数组内容是否相同、整理数组顺序 */
function areArraysEqual(arr1: any[], arr2: any[]) {
	if (arr1.length !== arr2.length) return false;
	const arr1Str = arr1.map(obj => normalizeObject(obj)).sort();
	const arr2Str = arr2.map(obj => normalizeObject(obj)).sort();
	return arr1Str.every((s, i) => s === arr2Str[i]);
}
const handleSave = async () => {
	try {
		// 验证表单
		if (!(await Promise.all([personFormRef.value?.validate, corpFormRef.value?.validate]))) {
			return;
		}
		const personFormData = personFormRef.value.getFormData();
		const corpFormData = corpFormRef.value.formData;

		const corpSocialMap = new Map();
		Object.keys(corpFormData).forEach((key: string) => {
			if (key.includes("_")) {
				const [field, corpType] = key.split("_");
				const corpSocialMapItem = corpSocialMap.get(ReverseCorpEnum[corpType as keyof typeof ReverseCorpEnum]) || {};
				corpSocialMapItem[field] = corpFormData[key];
				corpSocialMap.set(ReverseCorpEnum[corpType as keyof typeof ReverseCorpEnum], corpSocialMapItem);
			}
		});
		const corpSocialHousingOption = Array.from(corpSocialMap.entries()).map(([key, value]) => ({
			...value,
			name: key
		}));
		// 合并表单数据
		const newFormData = {
			...DEFAULT_OPTION,
			...personFormData,
			corpSocialHousingOption,
			isUpdateCorpOption: !areArraysEqual(formData.value.corpSocialHousingOption || [], corpSocialHousingOption)
		};
		delete newFormData.corpId;
		await SalaryOptionService.update(newFormData);
		close();
	} catch (error) {
		console.error("保存失败:", error);
	}
};

const open = () => {
	visible.value = true;
};

const close = () => {
	visible.value = false;
};

defineExpose({ open, close });
</script>
<style scoped lang="scss">
:global(.salary-option-modal .el-dialog__body) {
	height: 1040px;
	padding: 0 30px;
}
:global(.salary-option-modal .el-dialog__body .option-tabs > .el-tabs__content) {
	height: 900px;
	padding: 16px 0;
}
:global(.salary-option-modal .el-dialog__body .dialog-button-group) {
	display: flex;
	justify-content: end;
}
</style>
