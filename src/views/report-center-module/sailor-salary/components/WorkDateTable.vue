<!-- 船员考勤数据表格 -->
<template>
	<div style="height: 100%">
		<div class="filterBox">
			<div class="item">
				<MonthSelectCpn v-model:date="params.date" />
			</div>
			<div style="width: 280px" class="item">
				<el-input
					v-model="params.search"
					:suffix-icon="Search"
					maxlength="50"
					placeholder="搜索表格内容"
					:clearable="true"
					@input="handleInput"
				/>
			</div>
			<div class="warning-text" v-if="monthData?.historyImpactNow === HistoryImpactType.HasImpact">
				<el-icon><WarningFilled /></el-icon>
				历史工资被修改，可能对本月工资计算产生影响。
			</div>
		</div>
		<div class="content">
			<div style="width: 100%; height: 100%">
				<div id="luckysheet" class="excel" />
				<!-- <span>暂无数据</span> -->
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch } from "vue";
import { Search } from "@element-plus/icons-vue";
import { subMonths, format } from "date-fns";
import { debounce } from "lodash";
import { SalaryService } from "@/api/modules/salary";
import { getContentByUrl } from "../utils";

import MonthSelectCpn from "../../sailor-data/components/MonthSelect.vue";
import { HistoryImpactType } from "@/api/interface/salary.model";

interface Params {
	date: Date;
	search: string | undefined;
}

const excelData = ref();
const hasData = ref(false);
const monthData = ref();

const params: Params = reactive({
	date: subMonths(new Date(), 1),
	search: undefined
});

/**销毁EXCEL对象 */
function sheetDestroy() {
	// @ts-ignore
	luckysheet && luckysheet.destroy();
}
const getData = () => {
	excelData.value = undefined;
	hasData.value = false;
	SalaryService.getDetail({ month: format(params.date, "yyyy-MM-01 00:00:00") }).then(res => {
		if (res.data) {
			monthData.value = res.data;
			if (res.data.finalPayroll) {
				getContentByUrl(res.data.finalPayroll).then(data => {
					excelData.value = data;
					hasData.value = true;
					sheetInit();
				});
			} else {
				sheetInit();
			}
		}
	});
};
const sheetInit = () => {
	// 销毁
	sheetDestroy();
	let options = {
		container: "luckysheet", //luckysheet为容器id
		gridKey: "sheet",
		lang: "zh",
		title: "",
		/**初始化sheet */
		data: [
			{
				name: "sheet1", //工作表名称
				index: 0, //工作表索引
				status: 1, //激活状态
				row: 50, //行数
				column: 30, //列数
				config: {
					authority: {
						//当前工作表的权限配置
						sheet: 1, //如果为1或true，则该工作表受到保护；如果为0或false，则该工作表不受保护。
						hintText: "当前表仅可查看" //弹窗提示的文字
					}
				}
			}
		],
		/**单元格右键点击 功能项 */
		cellRightClickConfig: {
			copy: false, // 复制
			copyAs: false, // 复制为
			paste: false, // 粘贴
			insertRow: false, // 插入行
			insertColumn: false, // 插入列
			deleteRow: false, // 删除选中行
			deleteColumn: false, // 删除选中列
			deleteCell: false, // 删除单元格
			hideRow: false, // 隐藏选中行和显示选中行
			hideColumn: false, // 隐藏选中列和显示选中列
			rowHeight: false, // 行高
			columnWidth: false, // 列宽
			clear: false, // 清除内容
			matrix: false, // 矩阵操作选区
			sort: false, // 排序选区
			filter: false, // 筛选选区
			chart: false, // 图表生成
			image: false, // 插入图片
			link: false, // 插入链接
			data: false, // 数据验证
			cellFormat: false // 设置单元格格式
		},
		/**是否显示公式栏 */
		sheetFormulaBar: true,
		showinfobar: false,
		showsheetbar: false,
		showtoolbar: false,
		enableAddRow: false,
		/**sheet 右键点击 功能项 */
		sheetRightClickConfig: {
			delete: true, // 删除
			copy: true, // 复制
			rename: true, //重命名
			color: true, //更改颜色
			hide: true, //隐藏，取消隐藏
			move: true //向左移，向右移
		},
		/**行宽度 默认46 0为隐藏 */
		rowHeaderWidth: 46,
		/**列高度 默认20 0为隐藏 */
		columnHeaderHeight: 20,
		/**初始化默认字体大小 默认11 */
		defaultFontSize: 11
	};
	if (excelData.value) {
		options.data = JSON.parse(excelData.value);
	}
	// @ts-ignore
	luckysheet.create(options);
	// @ts-ignore
	luckysheet.refreshFormula();
};

// 监听月度的变化 获取某年某月有多少天
watch(
	() => params.date,
	() => {
		refresh();
	}
);

// 输入框 值改变 刷新表格 500ms防抖
const handleInput = debounce(() => {
	if (params.search === "") {
		return;
	}
	//@ts-ignore
	const res = luckysheet.find(params.search);
	if (res.length > 0) {
		//@ts-ignore
		luckysheet.setRangeShow({ row: [res[0].row, res[0].row], column: [res[0].column, res[0].column] });
	}
	// refresh();
}, 500);

// 刷新表格数据
function refresh() {
	getData();
}
defineExpose({
	getMonth: () => params.date,
	hasData
});
onMounted(() => {
	getData();
});
</script>

<style scoped lang="scss">
.excel {
	margin: 0;
	padding: 0;
	height: 100%;
	width: 100%;
}
.filterBox {
	display: flex;
	margin-bottom: 20px;
	.item {
		margin-right: 20px;
		&:last-child {
			margin-right: 0;
		}
	}
}
.content {
	width: 100%;
	height: 100%;
	display: flex;
	flex-direction: column;
	align-items: center;
	.placeholder-box {
		cursor: default;
		border: 1px solid #ebeef5;
		color: $color-text-primary;
		padding: 20px 40px;
		margin-top: 20px;
		border-radius: 5px;
	}
}
.warning-text {
	display: flex;
	align-items: center;
	margin-left: 12px;
	color: #e6a23c;
	& > .el-icon {
		margin-right: 4px;
	}
}
:deep(.cell) {
	justify-content: center;
}
:deep(.remark-cell .cell) {
	justify-content: start;
}
:deep(.label-remark-cell .cell) {
	justify-content: center;
}
</style>
