/* eslint-disable no-console */
/* @preserve console */

import { getDaysInMonth } from "date-fns";
import { SalaryPositionType } from "@/api/interface/member";
import { ONLY_SHOW_SECOND_CARD_NAMES } from "../constants";
import {
	calculateDeduct,
	calculateEatSubsidy,
	calculateSeaSubsidy,
	determinePositionType,
	calculateDaySalary,
	calculateHourSalary,
	calculateInsurancesAndFund,
	calculateSecondCardDeduction,
	calculateOvertimePay,
	calculateHighTempSubsidy
} from "./salaryCalculator";
import { debugLog } from "@/utils/util";
/**
 * 薪资计算调试工具
 * @param userNameInfoMap 用户信息映射
 * @param salaryOption 薪资配置选项
 * @param formData 前置表单数据
 * @param shipUsersMap 船名船员映射
 */

export default function setupDebugTool(
	userNameInfoMap: Map<string, any>,
	salaryOption: any,
	formData: any,
	shipUsersMap: Map<string, any>
) {
	// 将调试函数挂载到全局 window 对象
	(window as any).debugSalaryCalculation = (userName: string) => {
		if (!userName) {
			console.warn("请提供用户姓名进行调试");
			return;
		}

		const userInfo = userNameInfoMap.get(userName);
		if (!userInfo) {
			console.warn(`未找到用户: ${userName}`);
			debugLog("可用的用户列表:", Array.from(userNameInfoMap.keys()));
			return;
		}

		// 创建格式化输出
		console.group(`%c${userName} 薪资计算调试信息`, "color: #0066FF; font-size: 16px; font-weight: bold;");

		// 基本信息
		debugLog("%c基本信息", "color: #009688; font-weight: bold;");
		console.table({
			姓名: userInfo.name,
			公司: userInfo.salaryPosition?.[0] || "未知",
			部门: userInfo.salaryPosition?.[1] || "未知",
			岗位: userInfo.salaryPosition?.[2] || "未知",
			入职时间: userInfo.enterTime || "未知",
			离职时间: userInfo.leaveTime || "未知",
			基本工资: userInfo.salary || 0,
			工龄工资: userInfo.senioritySalary || 0,
			通讯补贴: userInfo.communicationSubsidy || 0,
			副卡工资: userInfo.secondCardSalary || 0
		});

		// 准备计算所需数据
		const monthDays = getDaysInMonth(new Date(formData.month));
		const restDayWeekend = formData.restDayWeekend || 0;
		const restDay = formData.restDay || 0;

		// 将配置项转换为数字
		const salaryOptionNumber: any = Object.keys(salaryOption)
			.filter(i => !["salaryOrder", "salaryOrderAttaches", "specialOption"].includes(i))
			.reduce((pre, cur) => {
				return {
					...pre,
					[cur]: Number(salaryOption[cur as keyof typeof salaryOption])
				};
			}, {});

		const tempDaySalary = salaryOptionNumber.daySalary || 0;

		// 使用新函数判断岗位类型（带调试输出）
		const position = determinePositionType(userInfo);
		debugLog(`岗位类型: ${position} (${SalaryPositionType[position]})`);

		// 使用新函数计算日薪（带调试输出）
		debugLog("%c日薪计算", "color: #009688; font-weight: bold;");
		const daySalary = calculateDaySalary(
			position,
			userInfo,
			monthDays,
			restDayWeekend,
			tempDaySalary,
			formData.month,
			true // 启用调试输出
		);

		// 使用新函数计算小时工资（带调试输出）
		debugLog("%c小时工资计算", "color: #009688; font-weight: bold;");
		const hourSalary = calculateHourSalary(position, daySalary, salaryOptionNumber, true);

		// 准备用户数据对象
		const userItem = {
			name: userInfo.name,
			workDay: userInfo.workDay || 0,
			annualLeaveDay: userInfo.annualLeaveDay || 0,
			illDay: userInfo.illDay || 0,
			remark: userInfo.remark || "",
			basicSalaryDay: userInfo.basicSalaryDay || 0,
			firstHalfDay: userInfo.firstHalfDay,
			lastHalfDay: userInfo.lastHalfDay,
			compensatoryDay: userInfo.compensatoryDay || 0
		};

		// 使用 salaryCalculator 中的方法计算缺勤扣减（带调试输出）
		const userLeaveMap = new Map([[userInfo.name, userInfo.remainderDays]]);
		const deductResult = calculateDeduct(
			userItem,
			position,
			daySalary,
			monthDays,
			restDay,
			restDayWeekend,
			salaryOptionNumber,
			formData.month,
			userInfo,
			userLeaveMap,
			true // 启用调试输出
		);

		let [deduct] = deductResult;

		// 计算加班费
		const overtime = userInfo.overtime || 0;
		const overtimeSalary = calculateOvertimePay(
			overtime,
			daySalary,
			hourSalary,
			position,
			userItem,
			monthDays,
			restDay,
			true // 启用调试输出
		);

		// 计算高温补贴
		const highTempSubsidy = calculateHighTempSubsidy(
			formData.month,
			position,
			true // 启用调试输出
		);

		// 计算出海补贴（带调试输出）
		const seaSubsidyObj = {
			seaDays: userInfo.seaDays || 0,
			workDays: userInfo.workDays || 0
		};

		// 计算津贴
		const allowance =
			position === SalaryPositionType.INTERNETMAN ? salaryOptionNumber.eatSubsidyInternet * Math.floor(userInfo.workDay) : 0;

		const seaSubsidyTotal = calculateSeaSubsidy(
			position,
			salaryOptionNumber,
			seaSubsidyObj,
			true // 启用调试输出
		);

		// 五险一金计算（带调试输出）
		debugLog("%c五险一金计算", "color: #009688; font-weight: bold;");
		const insuranceResult = calculateInsurancesAndFund(userInfo, salaryOptionNumber, {}, true);

		// 计算主卡应发
		const salaryWithAllowance =
			Number(userInfo.salary || 0) -
			Number(userInfo.secondCardSalary || 0) +
			Number(allowance || 0) +
			// Number(eatSubsidy || 0) +
			Number(seaSubsidyTotal || 0) +
			Number(userInfo.senioritySalary || 0) +
			Math.round(overtimeSalary || 0) +
			Number(highTempSubsidy || 0);

		debugLog(
			`主卡应发 = 薪酬(${Number(userInfo.salary || 0) - Number(userInfo.secondCardSalary || 0)}) + 津贴(${Number(
				allowance || 0
			)}) + 出海补贴(${Number(seaSubsidyTotal || 0)}) + 工龄工资(${Number(userInfo.senioritySalary || 0)}) + 加班(${Math.round(
				overtimeSalary || 0
			)}) + 高温补贴(${highTempSubsidy}) = ${salaryWithAllowance}`
		);
		debugLog(`五险一金扣除 = ${insuranceResult.totalDeduction}`);
		debugLog(`缺勤扣减 = ${deduct}`);

		// 计算伙食（伙食费）补贴（带调试输出）
		calculateEatSubsidy(
			userItem,
			userInfo,
			salaryOptionNumber.eatSubsidyShipMan,
			shipUsersMap,
			true // 启用调试输出
		);

		if (!userInfo.secondCardName || !userInfo.secondCardSalary) {
			console.groupEnd();
			return;
		}

		// 副卡扣减判断（带调试输出）
		debugLog("%c副卡扣减判断", "color: #009688; font-weight: bold;");
		calculateSecondCardDeduction(
			Number(userInfo.salary || 0) - Number(userInfo.secondCardSalary || 0),
			0, // allowance
			Number(seaSubsidyTotal || 0),
			Number(userInfo.senioritySalary || 0),
			Math.round(overtimeSalary || 0),
			insuranceResult.totalDeduction,
			deduct,
			Number(userInfo.secondCardSalary || 0),
			true // 启用调试
		);

		let needDeductInSecondCard = false;
		if (salaryWithAllowance - insuranceResult.totalDeduction - deduct < 0) {
			debugLog(`主卡不够扣: ${salaryWithAllowance} - ${insuranceResult.totalDeduction} - ${deduct} < 0`);

			if (userInfo.secondCardSalary) {
				needDeductInSecondCard = true;
				const secondCardDeduct = Math.min(deduct, Number(userInfo.secondCardSalary || 0));
				const newDeduct = Math.max(deduct - Number(userInfo.secondCardSalary || 0), 0);

				debugLog(
					`副卡扣减 = Math.min(缺勤扣减(${deduct}), 副卡工资(${Number(userInfo.secondCardSalary || 0)})) = ${secondCardDeduct}`
				);
				debugLog(
					`主卡缺勤扣减调整为 = Math.max(缺勤扣减(${deduct}) - 副卡工资(${Number(
						userInfo.secondCardSalary || 0
					)}), 0) = ${newDeduct}`
				);

				deduct = newDeduct;
			} else {
				debugLog(`无副卡工资，主卡将出现负数情况`);
			}
		} else {
			debugLog(`主卡足够扣: ${salaryWithAllowance} - ${insuranceResult.totalDeduction} - ${deduct} >= 0`);
		}

		// 计算主卡实发工资
		const mainCardEndSalary = salaryWithAllowance - insuranceResult.totalDeduction - deduct;
		debugLog(
			`主卡实发工资 = 主卡应发(${salaryWithAllowance}) - 五险一金(${insuranceResult.totalDeduction}) - 缺勤扣减(${deduct}) = ${mainCardEndSalary}`
		);

		// 副卡信息
		debugLog(`副卡扣减标志: ${needDeductInSecondCard}`);
		if (userInfo.secondCardName) {
			debugLog(`副卡姓名: ${userInfo.secondCardName}`);
		}
		debugLog(`副卡工资: ${Number(userInfo.secondCardSalary || 0)}`);

		if (needDeductInSecondCard) {
			const secondCardDeduct = Math.min(deduct, userInfo.secondCardSalary || 0);

			const viceCardEndSalary = Number(userInfo.secondCardSalary || 0) - secondCardDeduct;
			debugLog(`副卡缺勤扣减: ${secondCardDeduct}`);
			debugLog(
				`副卡实发工资 = 副卡工资(${Number(
					userInfo.secondCardSalary || 0
				)}) - 副卡缺勤扣减(${secondCardDeduct}) = ${viceCardEndSalary}`
			);
		} else {
			debugLog(`副卡应发 = 副卡工资(${Number(userInfo.secondCardSalary || 0)})`);
		}

		// 特殊人员处理
		if (ONLY_SHOW_SECOND_CARD_NAMES && ONLY_SHOW_SECOND_CARD_NAMES.includes(userInfo.name)) {
			debugLog(`特殊人员(${userInfo.name})：仅显示副卡工资单，主卡工资单将被隐藏`);
		}

		console.groupEnd();
	};
	debugLog('%c薪资计算调试工具已加载，使用 debugSalaryCalculation("姓名") 查看用户计算详情', "color: #4CAF50; font-size: 12px;");
}
