import { getDate, isSameMonth, setYear, getYear, getMonth, format } from "date-fns";
import { SalaryPositionType, PayItem, Member } from "@/api/interface/member";
import { AttendanceData, HIGH_TEMP_SUBSIDY } from "../constants";
import { debugLog } from "@/utils/util";

/**
 * 薪资计算核心逻辑
 * 将所有计算逻辑抽离到单独的文件，同时支持实际计算和调试
 */

/**
 * 计算缺勤扣减
 */
export function calculateDeduct(
	item: AttendanceData,
	position: SalaryPositionType,
	actDaySalary: number,
	monthDays: number,
	restDay: number,
	restDayWeekend: number,
	salaryOptionNumber: any,
	month: string,
	userInfo: Member.CorpUserWithSalary | undefined,
	userLeaveMap: Map<string, number>,
	debug = false
) {
	// 本月工作天数
	let workDay = item.workDay + (item.annualLeaveDay ?? 0);
	const { restDayShipMan, shipManMinSalary } = salaryOptionNumber;
	const { enterTime: infoEnterTime = "", leaveTime: infoLeaveTime = "" } = userInfo || {};

	// 用户入/离职日期
	let enterTime: string = infoEnterTime;
	let leaveTime: string = infoLeaveTime;

	// 从备注中提取入职/离职信息
	if (item.remark) {
		const enterMatch = item.remark.match(/(\w+\.\w+入职)/);
		if (enterMatch && enterTime === "") {
			const remarkEnterTime = format(
				setYear(new Date(enterMatch[1].replaceAll("入职", "")), getYear(new Date(month))),
				"yyyy-MM-dd"
			);
			enterTime = remarkEnterTime;
			if (debug) debugLog(`从备注中提取入职日期: ${remarkEnterTime}`);
		}

		const leaveMatch = item.remark.match(/(\w+\.\w+离职)/);
		if (leaveMatch && leaveTime === "") {
			const remarkLeaveTime = format(
				setYear(new Date(leaveMatch[1].replaceAll("离职", "")), getYear(new Date(month))),
				"yyyy-MM-dd"
			);
			leaveTime = remarkLeaveTime;
			if (debug) debugLog(`从备注中提取离职日期: ${remarkLeaveTime}`);
		}
	}

	// 处理入职/离职情况（半天工作和未在职天数）
	let unemployedDays = 0;

	// 处理入职情况
	if (enterTime !== "" && isSameMonth(new Date(enterTime), new Date(month))) {
		// 计算入职前未在职天数
		unemployedDays += new Date(enterTime).getDate() - 1;
		if (debug) debugLog(`本月入职，入职前未在职天数: ${unemployedDays}`);

		// 处理入职当天为半天的情况
		if (getDate(new Date(enterTime)) === item.firstHalfDay) {
			workDay += 0.5;
			if (debug) debugLog(`入职当天为半天，补回0.5天，调整后工作天数: ${workDay}`);
		}
	}

	// 处理离职情况
	if (leaveTime !== "" && isSameMonth(new Date(leaveTime), new Date(month))) {
		// 计算离职后未在职天数
		unemployedDays += monthDays - new Date(leaveTime).getDate();
		if (debug) debugLog(`本月离职，离职后未在职天数: ${unemployedDays}`);

		// 处理离职当天为半天的情况
		if (getDate(new Date(leaveTime)) === item.lastHalfDay) {
			workDay += 0.5;
			if (debug) debugLog(`离职当天为半天，补回0.5天，调整后工作天数: ${workDay}`);
		}
	}

	// 根据岗位类型计算缺勤扣减
	if (position === SalaryPositionType.SHIPMAN) {
		return calculateShipmanDeduct(
			item,
			workDay,
			monthDays,
			actDaySalary,
			unemployedDays,
			shipManMinSalary,
			restDayShipMan,
			userLeaveMap,
			debug
		);
	} else if (position === SalaryPositionType.WORKMAN) {
		return calculateWorkmanDeduct(item, workDay, monthDays, actDaySalary, unemployedDays, restDay, shipManMinSalary, debug);
	} else {
		return calculateOfficeDeduct(item, workDay, monthDays, actDaySalary, position, restDayWeekend, debug);
	}
}

// 船员缺勤扣减计算
function calculateShipmanDeduct(
	item: AttendanceData,
	workDay: number,
	monthDays: number,
	actDaySalary: number,
	unemployedDays: number,
	shipManMinSalary: number,
	restDayShipMan: number,
	userLeaveMap: Map<string, number>,
	debug = false
) {
	if (debug) debugLog("%c船员缺勤扣减计算", "color: #009688; font-weight: bold;");

	if (item.remark?.includes("临时")) {
		if (debug) debugLog("临时船员不参与绩效扣减");
		return [0];
	}

	// 船员最低日薪时扣减
	const dayMinDeduct = Number((actDaySalary - shipManMinSalary / monthDays).toFixed(2));
	if (debug) debugLog(`船员保底日薪扣减: ${dayMinDeduct}`);

	// 用户剩余年休假数
	const remainderDays = userLeaveMap.get(item.name) ?? restDayShipMan;
	if (debug) debugLog(`用户剩余年休假天数: ${remainderDays}`);

	// 使用后剩余年休假
	let remainderDaysAfter = remainderDays;
	let deduct = 0;

	if (workDay < monthDays) {
		// 实际缺勤天数
		const leaveDays = Math.max(monthDays - workDay - unemployedDays, 0);
		if (debug) debugLog(`实际缺勤天数: ${leaveDays}`);

		// 发保底且不扣年休假的天数
		const basicSalaryDay = Math.min(item.basicSalaryDay || 0, leaveDays);
		if (debug) debugLog(`发保底且不扣年休假的天数: ${basicSalaryDay}`);

		if (leaveDays - basicSalaryDay > 0) {
			// 需要扣减的天数
			const needDeductDays = leaveDays - basicSalaryDay;
			if (debug) debugLog(`需要扣减的天数: ${needDeductDays}`);

			// 全部使用年休假折抵
			if (needDeductDays < remainderDays) {
				if (debug) debugLog(`全部使用年休假折抵，使用年休假: ${needDeductDays}天`);
				deduct = Number((leaveDays * dayMinDeduct).toFixed(2));
				remainderDaysAfter -= needDeductDays;
			} else {
				// 年休假不够用，部分按全额扣减
				if (debug) debugLog(`年休假不够用，使用全部年休假: ${remainderDays}天`);
				deduct = Number(
					((remainderDays + basicSalaryDay) * dayMinDeduct + (leaveDays - remainderDays - basicSalaryDay) * actDaySalary).toFixed(
						2
					)
				);
				remainderDaysAfter = 0;
			}
		} else {
			deduct = Number((basicSalaryDay * dayMinDeduct).toFixed(2));
			if (debug) debugLog(`全部按保底扣减: ${deduct}`);
		}

		// 未在职天数按日薪扣减
		if (unemployedDays > 0) {
			const unemployedDeduct = unemployedDays * actDaySalary;
			if (debug) debugLog(`未在职扣减: ${unemployedDeduct}`);
			deduct += unemployedDeduct;
		}
	}

	if (debug) debugLog(`最终缺勤扣减: ${deduct}, 剩余假期: ${remainderDaysAfter}`);
	return [deduct, remainderDaysAfter];
}

// 工人缺勤扣减计算
function calculateWorkmanDeduct(
	item: AttendanceData,
	workDay: number,
	monthDays: number,
	actDaySalary: number,
	unemployedDays: number,
	restDay: number,
	shipManMinSalary: number,
	debug = false
) {
	if (debug) debugLog("%c工人缺勤扣减计算", "color: #009688; font-weight: bold;");

	// 工人实际缺勤 = 本月工作天数 - 实际出勤天数 - 工人可休假天数 - 未在职天数
	const absentDays = Math.max(monthDays - workDay - restDay - unemployedDays, 0);
	if (debug) debugLog(`实际缺勤天数: ${absentDays}`);

	if (absentDays <= 0) {
		// 缺勤天小于0，即全勤，但需要扣减未在职的工资
		const deduct = unemployedDays * actDaySalary;
		if (debug) debugLog(`全勤，仅扣减未在职工资: ${deduct}`);
		return [deduct];
	} else {
		// 保底工资
		const basicDaySalary = Number((shipManMinSalary / monthDays).toFixed(2));
		if (debug) debugLog(`保底日薪: ${basicDaySalary}`);

		// 病假和调休天数
		const illDay = item.illDay || 0;
		const compensatoryDay = item.compensatoryDay || 0;
		if (debug) debugLog(`病假天数: ${illDay}, 调休天数: ${compensatoryDay}`);

		// 缺勤扣减 = 缺勤天数 * 全扣日薪 - 病假or调休天数 * 保底日薪
		const illCompDays = Math.min(absentDays, illDay + compensatoryDay);
		if (debug) debugLog(`病假和调休可抵扣天数: ${illCompDays}`);

		const deduct = (absentDays + unemployedDays) * actDaySalary - illCompDays * basicDaySalary;
		if (debug) debugLog(`缺勤扣减: ${deduct.toFixed(2)}`);

		return [deduct];
	}
}

// 网科部/办公室缺勤扣减计算
function calculateOfficeDeduct(
	item: AttendanceData,
	workDay: number,
	monthDays: number,
	actDaySalary: number,
	position: SalaryPositionType,
	restDayWeekend: number,
	debug = false
) {
	const positionName =
		position === SalaryPositionType.INTERNETMAN ? "网科部" : position === SalaryPositionType.OFFICER ? "办公室" : "临时工";
	if (debug) debugLog(`%c${positionName}岗位缺勤扣减计算`, "color: #009688; font-weight: bold;");

	if (position === SalaryPositionType.TEMPORARY) {
		if (debug) debugLog("临时工按实际出勤计算工资，不存在缺勤扣减");
		return [0];
	}

	// 病假天数
	const illDay = Math.min(item.illDay || 0, monthDays - workDay - restDayWeekend);
	if (debug) debugLog(`可计入薪资的病假天数: ${Math.max(illDay, 0)}`);

	// 网科部、办公室 薪资天数 = 实际出勤 + 法定假日（包含周末）+ 病假天数*0.8
	const salaryDays = workDay + restDayWeekend + Math.max(illDay, 0) * 0.8;
	if (debug)
		debugLog(
			`薪资天数 = 实际出勤(${workDay}) + 法定假日（包含周末）(${restDayWeekend})+ 病假天数(${Math.max(
				illDay,
				0
			)})*0.8 = ${salaryDays}`
		);

	// 缺勤扣减 = 日薪 * (月天数 - 薪资天数)
	const absentDays = Math.max(monthDays - salaryDays, 0);
	let deduct = 0;

	if (absentDays > 0) {
		deduct = actDaySalary * absentDays;
		if (debug) debugLog(`缺勤扣减:= 日薪(${actDaySalary}) * (月天数 - 薪资天数)(${absentDays}) = ${deduct.toFixed(2)}`);
	} else {
		if (debug) debugLog(`无缺勤，不需要扣减`);
	}

	if (debug) debugLog(`最终缺勤扣减: ${deduct.toFixed(2)}`);
	return [deduct];
}

/**
 * 计算伙食补贴
 */
export function calculateEatSubsidy(
	item: AttendanceData,
	userInfo: Member.CorpUserWithSalary | undefined,
	eatSubsidyShipMan: number,
	shipUsersMap: Map<string, any[]>,
	debug = false
) {
	const shipName = userInfo?.salaryPosition?.[1];
	if ((item.remark?.includes("打饭补") || item.remark?.includes("打伙食")) && shipName) {
		const shipUsers = shipUsersMap.get(shipName) || [];
		const subsidy = shipUsers.reduce((pre, cur) => pre + (cur.wholeDay || 0), 0) * eatSubsidyShipMan;

		if (debug) {
			debugLog(`%c伙食费计算`, "color: #009688; font-weight: bold;");
			debugLog(`船名: ${shipName}, 船员人数: ${shipUsers.length}`);
			debugLog(`船员总计工作天数: ${shipUsers.reduce((pre, cur) => pre + (cur.wholeDay || 0), 0)}`);
			debugLog(`伙食费 = 船员总计工作天数 * 船员每日伙食费(${eatSubsidyShipMan}) = ${subsidy}`);
		}

		return subsidy;
	}

	if (debug) {
		debugLog(`%c伙食费计算`, "color: #009688; font-weight: bold;");
		debugLog("不符合伙食费条件");
	}
	return 0;
}

/**
 * 计算出海补贴、作业负责合计补贴
 */
export function calculateSeaSubsidy(position: SalaryPositionType, salaryOptionNumber: any, seaSubsidyObj: any, debug = false) {
	const { seaSubsidy, workSubsidy } = salaryOptionNumber;

	if (seaSubsidyObj) {
		// 船员和工人计算出海补贴
		if ([SalaryPositionType.SHIPMAN, SalaryPositionType.WORKMAN, SalaryPositionType.OFFICER].includes(position)) {
			const total = seaSubsidyObj.seaDays * seaSubsidy + seaSubsidyObj.workDays * workSubsidy;

			if (debug) {
				debugLog(`%c出海补贴计算`, "color: #009688; font-weight: bold;");
				debugLog(`出海天数: ${seaSubsidyObj.seaDays}, 出海补贴: ${seaSubsidy}`);
				debugLog(`作业负责天数: ${seaSubsidyObj.workDays}, 作业负责补贴: ${workSubsidy}`);
				debugLog(`出海补贴合计: ${total}`);
			}

			return total;
		}
	}

	if (debug) debugLog("不符合出海补贴条件");
	return 0;
}

/**
 * 计算带薪休假部分的加班费
 */
export function calculateOvertimeSalary(daySalary: number, item: any, monthDays: number, restDay: number, debug = false) {
	const overtimeDays = item.workDay + restDay - monthDays;

	if (overtimeDays > 0) {
		const salary = daySalary * overtimeDays;
		if (debug) {
			debugLog(`%c带薪休假加班费计算`, "color: #009688; font-weight: bold;");
			debugLog(`超出天数: ${overtimeDays}`);
			debugLog(`带薪休假加班费 = 日薪(${daySalary}) * 超出天数(${overtimeDays}) = ${salary}`);
		}
		return salary;
	}

	return 0;
}

/**
 * 计算加班费
 */
export function calculateOvertimePay(
	overtime: number,
	daySalary: number,
	hourSalary: number,
	position: SalaryPositionType,
	item: any,
	monthDays: number,
	restDay: number,
	debug = false
): number {
	// 基础加班费
	let overtimeSalary = overtime ? overtime * hourSalary : 0;

	if (debug) {
		debugLog(`基础加班费 = 加班时长(${overtime}) * 小时工资(${hourSalary.toFixed(2)}) = ${overtimeSalary.toFixed(2)}`);
	}

	// 工人需加上带薪休假未休的加班费
	if (position === SalaryPositionType.WORKMAN) {
		const additionalOvertimeSalary = calculateOvertimeSalary(daySalary, item, monthDays, restDay, debug);

		if (additionalOvertimeSalary > 0) {
			overtimeSalary += additionalOvertimeSalary;
			if (debug) {
				debugLog(`总加班费 = ${overtimeSalary.toFixed(2)}`);
			}
		}
	}

	return overtimeSalary;
}

/**
 * 根据用户信息判断岗位类型
 */
export function determinePositionType(userInfo: Member.CorpUserWithSalary | undefined, debug = false): SalaryPositionType {
	if (!userInfo || !userInfo.salaryPosition?.[2]) {
		if (debug) debugLog("无岗位信息，默认为临时工");
		return SalaryPositionType.TEMPORARY;
	}

	// 临时工判断
	if (["生产部临时工", "临时工"].includes(userInfo.salaryPosition?.[1])) {
		if (debug) debugLog(`部门为${userInfo.salaryPosition?.[1]}，判定为临时工`);
		return SalaryPositionType.TEMPORARY;
	}

	// 根据岗位名称判断
	switch (userInfo.salaryPosition[2]) {
		case "船员":
			if (debug) debugLog("岗位为船员");
			return SalaryPositionType.SHIPMAN;
		case "工人":
			if (debug) debugLog("岗位为工人");
			return SalaryPositionType.WORKMAN;
		case "管理":
			// 网科部特殊处理
			if (userInfo.salaryPosition[1] === "网科部") {
				if (debug) debugLog("岗位为网科部管理");
				return SalaryPositionType.INTERNETMAN;
			}
			if (debug) debugLog("岗位为办公室管理");
			return SalaryPositionType.OFFICER;
		default:
			if (debug) debugLog(`未知岗位类型: ${userInfo.salaryPosition[2]}，默认为临时工`);
			return SalaryPositionType.TEMPORARY;
	}
}

/**
 * 计算日薪
 */
export function calculateDaySalary(
	position: SalaryPositionType,
	userInfo: Member.CorpUserWithSalary | undefined,
	monthDays: number,
	restDayWeekend: number,
	tempDaySalary: number,
	month: string,
	debug = false
): number {
	// 临时工日薪
	if (position === SalaryPositionType.TEMPORARY) {
		if (userInfo?.salaryPosition?.[1] === "生产部临时工") {
			const salary = Number(userInfo.salaryPosition[2].replace("元/天", ""));
			if (debug) debugLog(`生产部临时工日薪 = ${salary}`);
			return salary;
		}
		if (debug) debugLog(`临时工日薪 = ${tempDaySalary}`);
		return tempDaySalary;
	} else if (userInfo) {
		// 网科部或当管理日薪按应出勤天数算
		if (position === SalaryPositionType.INTERNETMAN || position === SalaryPositionType.OFFICER) {
			const salary = Number(
				((Number(userInfo.salary) + Number(userInfo.senioritySalary)) / (monthDays - restDayWeekend)).toFixed(2)
			);
			if (debug)
				debugLog(
					`日薪 = (薪酬(${userInfo.salary}) + 工龄工资(${userInfo.senioritySalary}))/ (月天数(${monthDays}) - 法定假日(${restDayWeekend})) = ${salary}`
				);
			return salary;
		}

		// 其余岗位按月天数算
		const salary = Number(((Number(userInfo.salary) + Number(userInfo.senioritySalary)) / monthDays).toFixed(2));
		if (debug)
			debugLog(`日薪 = (薪酬(${userInfo.salary}) + 工龄工资(${userInfo.senioritySalary}))/ 月天数(${monthDays}) = ${salary}`);
		return salary;
	} else {
		if (debug) debugLog("无薪酬信息，默认为0");
		return 0;
	}
}

/**
 * 计算小时工资
 */
export function calculateHourSalary(
	position: SalaryPositionType,
	daySalary: number,
	salaryOptionNumber: any,
	debug = false
): number {
	const { dailyHourInternet, dailyHourOfficer, dailyHourWorkMan, dailyHourTempMan } = salaryOptionNumber;

	let dailyHours;
	switch (position) {
		case SalaryPositionType.INTERNETMAN:
			dailyHours = dailyHourInternet;
			break;
		case SalaryPositionType.OFFICER:
			dailyHours = dailyHourOfficer;
			break;
		case SalaryPositionType.WORKMAN:
			dailyHours = dailyHourWorkMan;
			break;
		default:
			dailyHours = dailyHourTempMan;
	}

	const hourSalary = daySalary / dailyHours;

	if (debug) {
		debugLog(`小时工资 = 日薪(${daySalary}) / 每日工时(${dailyHours}) = ${hourSalary.toFixed(2)}`);
	}

	return hourSalary;
}

/**
 * 计算五险一金
 */
export function calculateInsurancesAndFund(
	userInfo: Member.CorpUserWithSalary | undefined,
	salaryOptionNumber: any,
	specialOption: Record<string, any> = {},
	debug = false
): {
	socialSecurity: number;
	medicalInsurance: number;
	unemployInsurance: number;
	providentFund: number;
	totalDeduction: number;
} {
	const { socialSecurity, medicalInsurance, unemployInsurance, providentFund } = salaryOptionNumber;

	let result = {
		socialSecurity: 0,
		medicalInsurance: 0,
		unemployInsurance: 0,
		providentFund: 0,
		totalDeduction: 0
	};

	if (!userInfo || !userInfo.payItems) {
		if (debug) debugLog("无缴纳项目信息");
		return result;
	}

	// 计算三险
	if (userInfo.payItems.includes(PayItem.Insurances)) {
		result.socialSecurity = specialOption.socialSecurity ?? socialSecurity;
		result.medicalInsurance = specialOption.medicalInsurance ?? medicalInsurance;
		result.unemployInsurance = specialOption.unemployInsurance ?? unemployInsurance;

		if (debug) {
			debugLog(`社保 = ${result.socialSecurity}`);
			debugLog(`医疗保险 = ${result.medicalInsurance}`);
			debugLog(`失业保险 = ${result.unemployInsurance}`);
		}
	} else if (debug) {
		debugLog("不缴纳社保");
	}

	// 计算公积金
	if (userInfo.payItems.includes(PayItem.Fund)) {
		result.providentFund = specialOption.providentFund ?? providentFund;
		if (debug) debugLog(`公积金 = ${result.providentFund}`);
	} else if (debug) {
		debugLog("不缴纳公积金");
	}

	// 计算总扣减
	result.totalDeduction = result.socialSecurity + result.medicalInsurance + result.unemployInsurance + result.providentFund;

	return result;
}

/**
 * 计算副卡扣减
 */
export function calculateSecondCardDeduction(
	salary: number,
	allowance: number = 0,
	seaSubsidy: number = 0,
	senioritySalary: number = 0,
	overtimeSalary: number = 0,
	socialDeduction: number = 0,
	deduct: number = 0,
	secondCardSalary: number = 0,
	debug: boolean = false
): {
	needDeductInSecondCard: boolean;
	mainCardDeduct: number;
	secondCardDeduct: number;
} {
	// 主卡应发
	const salaryWithAllowance =
		Number(salary) + Number(allowance) + Number(seaSubsidy) + Number(senioritySalary) + Number(overtimeSalary);

	if (debug) {
		debugLog(
			`主卡应发 = ${salary} + ${allowance} + ${seaSubsidy} + ${senioritySalary} + ${overtimeSalary} = ${salaryWithAllowance}`
		);
		debugLog(`五险一金扣除 = ${socialDeduction}`);
		debugLog(`缺勤扣减 = ${deduct}`);
	}

	// 判断主卡是否够扣
	if (salaryWithAllowance - socialDeduction - deduct < 0 && secondCardSalary > 0) {
		if (debug) {
			debugLog(`主卡不够扣: ${salaryWithAllowance} - ${socialDeduction} - ${deduct} < 0`);
		}

		// 副卡扣减
		const secondCardDeduct = Math.min(deduct, secondCardSalary);
		// 主卡扣减调整
		const mainCardDeduct = Math.max(deduct - secondCardSalary, 0);

		if (debug) {
			debugLog(`副卡扣减 = Math.min(${deduct}, ${secondCardSalary}) = ${secondCardDeduct}`);
			debugLog(`主卡缺勤扣减调整为 = Math.max(${deduct} - ${secondCardSalary}, 0) = ${mainCardDeduct}`);
		}

		return {
			needDeductInSecondCard: true,
			mainCardDeduct,
			secondCardDeduct
		};
	} else {
		if (debug) {
			debugLog(`主卡足够扣: ${salaryWithAllowance} - ${socialDeduction} - ${deduct} >= 0`);
		}

		return {
			needDeductInSecondCard: false,
			mainCardDeduct: deduct,
			secondCardDeduct: 0
		};
	}
}

/**
 * 计算高温补贴
 * @param month 月份
 * @param position 岗位类型
 * @param debug 是否调试
 * @param highTempSubsidy 高温补贴金额
 * @returns 高温补贴
 */

export function calculateHighTempSubsidy(month: string, position: SalaryPositionType, debug = false): number {
	// 7-9月为高温补贴月份
	const monthNumber = getMonth(new Date(month)) + 1;
	if (monthNumber < 7 || monthNumber > 9) {
		if (debug) debugLog(`非高温补贴月份(${monthNumber})，高温补贴为0`);
		return 0;
	} else {
		if (debug)
			debugLog(
				`岗位类型 = ${position}，高温补贴 = ${
					[SalaryPositionType.OFFICER, SalaryPositionType.INTERNETMAN].includes(position) ? HIGH_TEMP_SUBSIDY : 0
				}`
			);
		return [SalaryPositionType.OFFICER, SalaryPositionType.INTERNETMAN].includes(position) ? HIGH_TEMP_SUBSIDY : 0;
	}
}
