// 薪资表数据计算
import { getDaysInMonth, format } from "date-fns";
import { onMounted, ref, Ref, computed, watch, onActivated } from "vue";
import { SalaryOption } from "@/api/interface/salary.model";
import { SalaryOptionService, LeaveRecordService } from "@/api/modules/salary";
import {
	sheetHeader,
	sheetFooter,
	oneColumn,
	header,
	SalaryPositionTextOrder,
	DEIVIDEND_NAMES,
	ONLY_SHOW_SECOND_CARD_NAMES,
	AttendanceData,
	CellItem,
	FormData,
	OvertimeData,
	SeaSubsidyData
} from "../constants";
import { MemberService } from "@/api/modules/member";
import { Member } from "@/api/interface/member";
import { SalaryPositionType, PayItem } from "@/api/interface/member";
import { StorageService } from "@/api/modules/power";
import setupDebugTool from "../utils/salaryDebug";
import {
	calculateDeduct,
	calculateEatSubsidy,
	calculateSeaSubsidy,
	determinePositionType,
	calculateDaySalary,
	calculateHourSalary,
	calculateInsurancesAndFund,
	calculateSecondCardDeduction,
	calculateOvertimePay,
	calculateHighTempSubsidy
} from "../utils/salaryCalculator";
import { ElMessageBox } from "element-plus";

interface SalaryPosition {
	name: string;
	value: string;
	children?: SalaryPosition[];
}

// 定义公司用户组类型
interface CompanyUsers {
	users: AttendanceData[];
	companyName: string;
}

// 定义部门排序映射类型
interface DepartmentOrderMap {
	[companyName: string]: Map<string, number>;
}

/**
 * @description 根据formData计算薪资表数据
 * @param {Ref} formData 新建薪资表流程数据(必传)
 * */

//表头总列数
const header_num = header.reduce((pre, cur) => (pre < cur.col ? cur.col : pre), 0) + 1;
export const useSalaryData = (formData: Ref<FormData>) => {
	let requestReady = ref([false, false, false, false]); //请求数据是否都获取完成
	const dataReady = computed(() => requestReady.value.every(i => i));
	const tableDataReady = ref(false);
	//薪资相关配置
	const salaryOption = ref<SalaryOption.SalaryOption>({});
	const companyList = ref<SalaryPosition[]>([]);
	/**用户名和用户信息的映射表*/
	let userNameInfoMap: Map<string, Member.CorpUserWithSalary>;
	/** 姓名->排序值 */
	let userOrderMap: Map<string, number>;
	//用户该年剩余带薪休假天数表
	let userLeaveMap: Map<string, number>;
	/** 船名->船员 */
	let shipUsersMap: Map<string, AttendanceData[]> = new Map();
	//最终结果
	const celldata = ref<CellItem[]>([]);
	//船员使用带薪休假列表
	const shipManLeaveList = ref<any[]>([]);

	//特殊五险一金信息
	let specialMap = new Map<string, any>();

	/** 用户薪资过程详细信息 */
	const userProcessMap = new Map<string, any>();
	/** 获取薪资参数配置和组织架构Map */
	const getOptionData = async () => {
		if (!formData.value.month) return;
		SalaryOptionService.getDetail().then(res => {
			if (res.data) {
				salaryOption.value = res.data;
				const { salaryOrder = [], specialOption } = res.data;
				specialMap = new Map(specialOption?.privateOption?.map((item: any) => [item.name, item]) || []);
				userOrderMap = new Map(salaryOrder?.map((item: any, index: number) => [item, index + 1]));
				requestReady.value[0] = true;
			}
		});
		MemberService.getListForSalary({ offset: -1, length: -1 }).then(res => {
			if (res.data && res.data.list) {
				userNameInfoMap = new Map(res.data.list.map((item: Member.CorpUserWithSalary) => [item.name, item]));
				//写死数据信息
				DEIVIDEND_NAMES.forEach(name => {
					userNameInfoMap.set(name, {
						name,
						salaryPosition: ["船务公司", "满洋301", "工人"],
						salary: "12500"
					} as Member.CorpUserWithSalary);
				});
				requestReady.value[1] = true;
			}
		});

		StorageService.getDetail({ key: "SalaryPosition" }).then(res => {
			if (res.data) {
				companyList.value = JSON.parse(res.data.value);
				requestReady.value[2] = true;
			}
		});
		LeaveRecordService.getList({
			year: format(new Date(formData.value.month), "yyyy"),
			beforeMonth: format(new Date(formData.value.month), "yyyy-MM")
		}).then(res => {
			if (res.data) {
				userLeaveMap = new Map(res.data.list?.map((item: any) => [item.userName, item.remainderDays]));
				requestReady.value[3] = true;
			}
		});
	};

	// 计算并转成所需要的表格数据
	const getData = () => {
		formData.value.unShowData = [];
		tableDataReady.value = false;
		if (!salaryOption.value || !userNameInfoMap || !companyList.value) {
			return;
		} else {
			const {
				workerAttendance = [], //考勤表数据
				tempAttendance = [], //临时工考勤表数据
				tempOverTime = [], //临时工加班数据
				internetAttendance = [], //网科部考勤表数据
				overtime = [], //加班表数据
				seaSubsidy = [], //出海补贴表数据
				month, //所属月份
				restDayWeekend, //法定假日天数(包含周末)
				restDay //工人可休假天数
			} = formData.value;

			//将配置项转换为数字
			interface SalaryOptionNumber {
				daySalary: number;
				basicSalary: number;
				eatSubsidyInternet: number;
				eatSubsidyShipMan: number;
				seaSubsidyDay: number;
				seaSubsidyMonth: number;
				socialSecurityBase: number;
				socialSecurityRate: number;
				medicalInsuranceBase: number;
				medicalInsuranceRate: number;
				unemployInsuranceBase: number;
				unemployInsuranceRate: number;
				providentFundBase: number;
				providentFundRate: number;
				[key: string]: number;
			}

			const salaryOptionNumber: SalaryOptionNumber = Object.keys(salaryOption.value)
				.filter(i => !["salaryOrder", "salaryOrderAttaches", "specialOption"].includes(i))
				.reduce((pre, cur) => {
					return {
						...pre,
						[cur]: Number(salaryOption.value[cur as keyof typeof salaryOption.value])
					};
				}, {} as SalaryOptionNumber);

			const {
				daySalary: tempDaySalary, //临时工日薪
				/** 基本工资 */
				basicSalary,
				eatSubsidyInternet //网科部每日餐补
			} = salaryOptionNumber;

			const monthDays = getDaysInMonth(new Date(month));
			/** 姓名->加班时长 */
			const overtimeMap: Map<string, number> = new Map(
				overtime.concat(tempOverTime).map((i: OvertimeData) => [i.name, i.overtime])
			);
			/** 姓名->出海补贴 */
			const seaSubsidyMap: Map<string, SeaSubsidyData> = new Map(seaSubsidy.map((i: SeaSubsidyData) => [i.name, i]));

			// 结合组织架构列表和考勤表和顺序表 先按公司再按顺序表排列
			const groupedUsers: { [key: string]: AttendanceData[] } = {};
			const departmentOrder: DepartmentOrderMap = {};

			companyList.value.forEach(company => {
				groupedUsers[company.name] = [];
				if (company.children) {
					departmentOrder[company.name] = new Map(
						company.children.map((child: SalaryPosition, index: number) => [child.name, index + 1])
					);
				}
			});
			groupedUsers["临时公司"] = [];

			// 组织架构里不存在的用户记录
			const notExistUserNames: string[] = [];

			// 所有员工的考勤
			const users: AttendanceData[] = workerAttendance
				.concat(internetAttendance)
				.concat(DEIVIDEND_NAMES.map(name => ({ name, remark: "满洋301固定分红", dividend: true } as AttendanceData)))
				.concat(tempAttendance);
			users.forEach((user: AttendanceData) => {
				// 船员按船名分组，用于计算伙食费
				if (user.shipName && user.shipName !== "") {
					if (shipUsersMap.has(user.shipName)) {
						shipUsersMap.set(user.shipName, (shipUsersMap.get(user.shipName) || []).concat(user));
					} else {
						shipUsersMap.set(user.shipName, [user]);
					}
				}
				//按公司分组
				let userInfo = userNameInfoMap.get(user.name);
				if (!userInfo) return notExistUserNames.push(user.name); //组织架构里不存在的用户不展示
				const companyName = userInfo.salaryPosition?.[0] || "临时公司";
				if (groupedUsers[companyName]) {
					groupedUsers[companyName].push(user);
				}

				// 增加按部门排序，部门优先级高于排序表
				if (userInfo.salaryPosition?.[1]) {
					const nameOrder = userOrderMap.get(user.name) ?? 999;
					const departOrder = departmentOrder[companyName]?.get(userInfo.salaryPosition?.[1]) ?? 99;
					const positionOrder = SalaryPositionTextOrder[userInfo.salaryPosition?.[2]] ?? 5;
					userOrderMap.set(user.name, nameOrder! + positionOrder * 100 + departOrder * 10000);
				}
			});
			// 不存在的用户进行提示
			if (notExistUserNames.length > 0) {
				ElMessageBox.confirm(
					`系统中无以下人员信息，将不会计算工资：<br/>
					${notExistUserNames.join("、")}`,
					"注意",
					{
						confirmButtonText: "确认",
						showCancelButton: false,
						type: "warning",
						dangerouslyUseHTMLString: true
					}
				);
			}
			const companyUsers: CompanyUsers[] = companyList.value.concat({ name: "临时公司", value: "临时公司" }).map(company => {
				return {
					users: groupedUsers[company.name].sort((a, b) => {
						const aIndex = userOrderMap.get(a.name) ?? 999999;
						const bIndex = userOrderMap.get(b.name) ?? 999999;
						return aIndex - bIndex;
					}),
					companyName: company.name
				};
			});
			// 数据初始化
			const tableData: CellItem[] = [];
			let row = 0;
			// 带薪休假记录置空
			shipManLeaveList.value = [];
			let startRowIndex: number;
			companyUsers.forEach(company => {
				if (company.users.length === 0) return;
				//标题 公司名称
				tableData.push({
					isTitle: true,
					r: row,
					c: 0,
					v: {
						ct: {
							fa: "@",
							t: "s"
						},
						fs: 14,
						ff: "宋体",
						ht: 0,
						vt: 0,
						tb: 2,
						v: company.companyName,
						m: company.companyName,
						mc: {
							r: row,
							c: 0,
							rs: 1,
							cs: header_num
						}
					}
				});
				row++;
				//表头 固定列名
				tableData.push(
					...sheetHeader.map(i => {
						const temp = JSON.parse(JSON.stringify(i));
						const tempRow = temp.r + row;
						temp.r = tempRow;
						if (temp.v.mc) {
							temp.v.mc.r = tempRow;
						}
						return temp;
					})
				);
				row += 2;
				startRowIndex = row;
				//表格内容
				company.users.forEach((item: AttendanceData, userIndex: number) => {
					/** 用户组织架构维护信息 */
					const userInfo = userNameInfoMap.get(item.name);

					// 判断岗位类型
					const position = determinePositionType(userInfo);

					// 计算日薪
					const daySalary = calculateDaySalary(position, userInfo, monthDays, restDayWeekend, tempDaySalary, month);

					// 计算小时工资
					const hourSalary = calculateHourSalary(position, daySalary, salaryOptionNumber);

					// 计算缺勤扣减
					const deductRes = calculateDeduct(
						item,
						position,
						daySalary,
						monthDays,
						restDay,
						restDayWeekend,
						salaryOptionNumber,
						month,
						userInfo,
						userLeaveMap,
						false // 不启用调试
					);
					const [deduct, remainderDays] = [Math.round(deductRes[0]), deductRes[1]];

					// 计算加班费
					const overtime = overtimeMap.get(item.name) || 0;
					const overtimeSalary = calculateOvertimePay(
						overtime,
						daySalary,
						hourSalary,
						position,
						item,
						monthDays,
						restDay,
						false // 不启用调试
					);

					// 计算伙食补贴（伙食费）
					const eatSubsidy = calculateEatSubsidy(
						item,
						userInfo,
						salaryOptionNumber.eatSubsidyShipMan,
						shipUsersMap,
						false // 不启用调试
					);

					// 计算津贴
					const allowance = position === SalaryPositionType.INTERNETMAN ? eatSubsidyInternet * Math.floor(item.workDay) : "";

					// 计算出海补贴
					const seaSubsidy = calculateSeaSubsidy(
						position,
						salaryOptionNumber,
						seaSubsidyMap.get(item.name),
						false // 不启用调试
					);

					//	单元格所需数据 临时工
					let cellData: any = {
						...item,
						index: row,
						userIndex,
						salaryPosition: userInfo?.salaryPosition?.[2] ?? "临时工", //工资分摊岗位
						eatSubsidy: eatSubsidy || "", //伙食费
						department: userInfo?.salaryPosition?.[1] ?? "",
						salary: (daySalary * item.workDay).toFixed(2), //薪酬
						seaSubsidy: seaSubsidy || "", //出海补贴
						tax: 0, //个人所得税
						communicationSubsidy: userInfo?.communicationSubsidy || "", //通讯补贴
						overtimeSalary: Math.round(overtimeSalary) || "" //加班
					};
					//生产部临时工特殊处理
					if (userInfo && userInfo.salaryPosition && userInfo.salaryPosition[1] === "生产部临时工") {
						cellData.salaryPosition = "临时工-" + daySalary;
						cellData.overtimeSalary = Math.round(overtimeSalary) || ""; //加班
					}
					//工龄工资 全月病假or调休的船员为0
					const senioritySalary =
						position === SalaryPositionType.SHIPMAN && (item.illDay || 0) + (item.compensatoryDay || 0) == monthDays
							? 0
							: Number(userInfo?.senioritySalary);
					const positionSalary = Number(userInfo?.salary) - basicSalary - Number(userInfo?.communicationSubsidy || 0); //岗位工资=工资-基本工资-通讯补贴
					// 正常员工填入其他数值
					if (userInfo && userInfo.salaryPosition && position !== SalaryPositionType.TEMPORARY && !item.dividend) {
						cellData = {
							...cellData,
							userIndex,
							basicSalary: positionSalary < 0 ? Number(userInfo.salary) : basicSalary, //岗位工资为负数时，基本工资置为0
							positionSalary: positionSalary < 0 ? 0 : positionSalary, //岗位工资计算后小于0则为0，否则为薪酬值
							salary: (Number(userInfo.salary) - Number(userInfo.secondCardSalary)).toFixed(2), //薪酬
							allowance, //津贴
							seaSubsidy, //出海补贴
							senioritySalary: senioritySalary || 0, //工龄工资
							overtimeSalary: Math.round(overtimeSalary) || "", //加班
							highTempSubsidy: calculateHighTempSubsidy(month, position), //高温
							deduct: deduct, //缺勤扣减
							remainderDays: position === SalaryPositionType.SHIPMAN ? remainderDays || 0 : "-" //剩余假期
						};
					} else if (item.dividend) {
						// 固定分红列
						cellData.salary = userInfo?.salary || "";
						cellData.endSalary = userInfo?.salary || "";
						cellData.bg = "#bdd4f7";
					}

					/** 判断是否缴纳五险一金 */
					if (userInfo && userInfo.payItems) {
						const specialOption = Object.entries(specialMap.get(userInfo.name) || {}).reduce((acc, [key, value]) => {
							if (value !== "") {
								acc[key] = Number(value);
							}
							return acc;
						}, {} as Record<string, any>);

						// 计算五险一金
						const insuranceResult = calculateInsurancesAndFund(userInfo, salaryOptionNumber, specialOption);

						if (userInfo.payItems.includes(PayItem.Insurances)) {
							cellData.socialSecurity = insuranceResult.socialSecurity;
							cellData.medicalInsurance = insuranceResult.medicalInsurance;
							cellData.unemployInsurance = insuranceResult.unemployInsurance;
						}

						if (userInfo.payItems.includes(PayItem.Fund)) {
							cellData.providentFund = insuranceResult.providentFund;
						}
					}

					if (userInfo && userInfo.secondCardSalary) {
						cellData.secondCardSalary = Number(userInfo.secondCardSalary) || 0;
					}

					// 计算主卡五险扣减
					const socialDeduct =
						(cellData.socialSecurity || 0) +
						(cellData.medicalInsurance || 0) +
						(cellData.unemployInsurance || 0) +
						(cellData.providentFund || 0);

					const salaryWithAllowance =
						Number(cellData.salary) +
						Number(cellData.allowance || 0) +
						Number(cellData.seaSubsidy || 0) +
						Number(cellData.senioritySalary || 0) +
						Number(cellData.overtimeSalary || 0);

					// 计算副卡扣减
					let needDeductInSecondCard = false;

					if (cellData.secondCardSalary) {
						const secondCardResult = calculateSecondCardDeduction(
							salaryWithAllowance,
							0,
							0,
							0,
							0,
							socialDeduct,
							deduct,
							cellData.secondCardSalary,
							false
						);

						if (secondCardResult.needDeductInSecondCard) {
							cellData.deduct = secondCardResult.mainCardDeduct;
							needDeductInSecondCard = true;
						}
					}

					if (!(userInfo && ONLY_SHOW_SECOND_CARD_NAMES.includes(userInfo.name))) {
						// 特殊人员-韩重洋不显示本人工资单
						tableData.push(...oneColumn(cellData));
						row++;
					} else {
						// 保存隐藏的工资单项
						cellData.company = company.companyName;
						formData.value.unShowData.push(cellData);
					}
					//出海补贴天数
					const seaSubsidyObj = seaSubsidyMap.get(item.name) || { seaDays: 0, workDays: 0 };

					/** 薪资流程信息记录 */
					const userProcess = {
						...userInfo,
						...item,
						position,
						remainderDays: userLeaveMap.get(item.name),
						overtime,
						...seaSubsidyObj
					};
					userProcessMap.set(cellData.name, userProcess);

					//若用户配置了副卡 额外添加一行
					if (userInfo && !!userInfo.secondCardName && !!userInfo.secondCardSalary) {
						tableData.push(
							...oneColumn({
								index: row,
								isSecondCard: true,
								fromUserName: userInfo.name,
								bg: "#bdd4f7",
								name: userInfo.secondCardName,
								salary: Number(userInfo.secondCardSalary), // 薪酬
								salaryWithAllowance: Number(userInfo.secondCardSalary), // 应发工资
								endSalary: Number(userInfo.secondCardSalary), // 实发工资
								tax: 0, // 个人所得税
								deduct: needDeductInSecondCard ? Math.min(deduct, cellData.secondCardSalary) : 0 // 缺勤扣减
							})
						);
						row++;
					}
				});
				tableData.push(
					...sheetFooter({
						row,
						startRowIndex,
						endRowIndex: row - 1,
						sumRows: ["D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U"]
					})
				);
				row += 1;
			});
			celldata.value = tableData;
			tableDataReady.value = true;
		}
	};

	//异步请求全部数据完成后计算表格数据
	watch(dataReady, () => {
		if (dataReady.value) {
			getData();
			// 设置调试工具
			setupDebugTool(userProcessMap, salaryOption.value, formData.value, shipUsersMap);
		}
	});
	const freshData = () => {
		requestReady.value = requestReady.value.map(() => false);
		getOptionData();
	};

	//重新进入页面或数据更新时重新计算表格数据
	onMounted(() => {
		freshData();
	});
	watch(
		() => formData.value,
		() => {
			freshData();
		}
	);
	onActivated(() => {
		freshData();
	});

	return {
		/** 当前计算出的 表格数据 */
		celldata,
		/** 是否计算完成 */
		dataReady: tableDataReady,
		/** 表格数据计算方法 */
		getData,
		/** 薪资配置值 */
		salaryOption,
		/** 当前计算出的 用户使用年休假列表 */
		shipManLeaveList
	};
};
