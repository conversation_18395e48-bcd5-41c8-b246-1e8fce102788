<!-- 新增工资单页面 -->
<template>
	<SinglePage type="sheet" title="创建工资单" :can-back="true" page-size="100%">
		<template #title>
			<el-steps
				simple
				style="width: 1000px; padding: 18px 48px; line-height: 0; background: none"
				:active="nowStep"
				finish-status="success"
				space="30%"
				process-status="process"
			>
				<el-step title="导入考勤表" :icon="UploadFilled" />
				<el-step title="数据对比" :icon="Operation" />
				<el-step title="系统初算" :icon="DataLine" />
				<el-step title="编辑个税" :icon="Grid" />
				<el-step title="完成创建" :icon="Select" />
			</el-steps>
		</template>
		<template #body>
			<transition appear name="fade-transform" mode="out-in">
				<!-- <keep-alive> -->
				<Step1 ref="step1Ref" v-if="nowStep == StepStatus.import"></Step1>
				<StepContrast ref="stepConRef" v-else-if="nowStep == StepStatus.contrast" @skip="stepToNext"></StepContrast>
				<Step2 ref="step2Ref" v-else-if="nowStep === StepStatus.calculate"></Step2>
				<Step3 ref="step3Ref" v-else-if="nowStep === StepStatus.tax"></Step3>
				<Step4 ref="step4Ref" v-else-if="nowStep === StepStatus.finished"></Step4>
				<!-- </keep-alive> -->
			</transition>
		</template>

		<template #bottom-button>
			<div style="display: flex">
				<el-button v-if="[StepStatus.contrast, StepStatus.calculate, StepStatus.tax].includes(nowStep)" @click="stepToNext(-1)"
					>上一步</el-button
				>
				<el-button v-if="[StepStatus.calculate, StepStatus.tax].includes(nowStep)" @click="save()">存草稿</el-button>
				<el-button v-if="nowStep !== StepStatus.finished" @click="stepToNext()" :loading="isLoading" type="primary">{{
					nowStep === StepStatus.tax ? "保存并完成" : "下一步"
				}}</el-button>
				<el-button v-else @click="router.back" type="primary">完成</el-button>
			</div>
		</template>
	</SinglePage>
</template>

<script setup lang="ts" name="EditShip">
import { ref, onMounted, provide, watch, nextTick } from "vue";
import SinglePage from "@/meta-components/MetaLayout/SinglePage.vue";
import Step1 from "./components/Step1.vue";
import Step2 from "./components/Step2.vue";
import Step3 from "./components/Step3.vue";
import Step4 from "./components/Step4.vue";
import StepContrast from "./components/StepContrast.vue";
import { useRouter } from "vue-router";
import { SaveType } from "./constants";
import { useDraft } from "./hooks/useDraft";
import { UploadFilled, Grid, DataLine, Select, Operation } from "@element-plus/icons-vue";

// 创建工资单数据
const formData = ref({} as any);
provide("formData", formData);

const isLoading = ref(false);
const step1Ref = ref();
const step2Ref = ref();
const step3Ref = ref();
const step4Ref = ref();
const stepConRef = ref();

const router = useRouter();

/**步骤枚举 */
enum StepStatus {
	/**导入考勤表 */
	import = 0,
	/** 数据对比 */
	contrast = 1,
	/**系统初算 */
	calculate = 2,
	/**编辑个税 */
	tax = 3,
	/**完成创建 */
	finished = 4
}
const nowStep = ref();

/** 存草稿 */
const save = async () => {
	if (nowStep.value === StepStatus.calculate) {
		// 初算阶段保存草稿
		await step2Ref.value.tryToNext(SaveType.DRAFT);
	} else if (nowStep.value === StepStatus.tax) {
		// 个税后存草稿
		await step3Ref.value.tryToNext(SaveType.DRAFT);
	}
};
/**尝试下一步 */
async function stepToNext(step: number = 1) {
	/**每次下一步的开始都要重置表单高度 */
	backToTop();
	if (step === -1) {
		switch (nowStep.value) {
			case StepStatus.calculate:
				formData.value.payroll = undefined;
				delete formData.value.shipManLeaveList;
				const canGoBack = step2Ref.value.saveEdit();
				if (canGoBack) {
					step -= 1;
				}
				break;
			case StepStatus.tax:
				delete formData.value.finalPayroll;
				break;
		}
		nowStep.value += step;
		return;
	}
	let canGoNext = false;
	switch (nowStep.value) {
		case StepStatus.import:
			canGoNext = await step1Ref.value.tryToNext();
			if (canGoNext) nowStep.value += 1;
			return;
		case StepStatus.contrast:
			canGoNext = await stepConRef.value.tryToNext();
			if (canGoNext) nowStep.value += 1;
			return;
		case StepStatus.calculate:
			canGoNext = await step2Ref.value.tryToNext();
			if (canGoNext) nowStep.value += 1;
			return;
		case StepStatus.tax:
			isLoading.value = true;
			canGoNext = await step3Ref.value.tryToNext();
			if (canGoNext) nowStep.value += 1;
			isLoading.value = false;
			return;
		case StepStatus.finished:
			isLoading.value = true;
			canGoNext = await step4Ref.value.tryToNext();
			// if (canGoNext) await addShip();
			isLoading.value = false;
			return;
	}
}

/**回到父组件的顶部 每次切换步骤都得重置高度 */
function backToTop() {
	const box = document.getElementsByClassName("page-container")[0];
	box.scrollTo({ top: 0 });
}
/** 根据草稿初始化步骤条 */
const initByDraft = () => {
	const { payroll, finalPayroll } = formData.value;
	if (finalPayroll) {
		nowStep.value = StepStatus.tax;
	} else if (payroll) {
		nowStep.value = StepStatus.calculate;
	} else {
		nowStep.value = StepStatus.import;
	}
};
/** 正常初始化  */
const normalInit = () => {
	nowStep.value = StepStatus.import;
};
const { saveDraft, askNeedRead, draftState } = useDraft(formData, "add-salary", initByDraft, normalInit);

const init = () => {
	if (draftState.hasDraft) {
		askNeedRead();
	} else {
		normalInit();
	}
};

watch(
	() => formData.value,
	() => {
		if (JSON.stringify(formData.value) !== "{}") {
			saveDraft();
		}
	},
	{ deep: true }
);

onMounted(async () => {
	// 初始化数据
	nextTick(() => {
		init();
	});
});
</script>

<style lang="scss" scoped></style>
