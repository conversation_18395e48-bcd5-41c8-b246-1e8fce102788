/** 是否存草稿 */
export enum SaveType {
	/** 存草稿 */
	DRAFT = 0,
	/** 提交 */
	SUBMIT = 1
}
/** 工资分摊岗位文本排序
 * 1临时工 2管理 3船员 4工人
 */
export const SalaryPositionTextOrder: { [key: string]: number } = {
	/** 临时工 */
	临时工: 1,
	/** 管理 */
	管理: 2,
	/** 船员 */
	船员: 3,
	/** 工人 */
	工人: 4
};
/** 薪资表表头静态数据 */
export const header = [
	//数据取整
	{ title: "序号", row: 0, col: 0, key: "userIndex" },
	{ title: "工资分摊岗位", row: 0, col: 1, key: "salaryPosition" },
	{ title: "姓名", row: 0, col: 2, key: "name" },
	{ title: "基本工资", row: 0, col: 3, key: "basicSalary" },
	{ title: "岗位工资", row: 0, col: 4, key: "positionSalary" },
	{ title: "通讯", row: 0, col: 5, key: "communicationSubsidy" },
	{ title: "薪酬", row: 0, col: 6, key: "salary" },
	{ title: "津贴", row: 0, col: 7, key: "allowance" },
	{ title: "出海补贴", row: 0, col: 8, key: "seaSubsidy" },
	{ title: "工龄工资", row: 0, col: 9, key: "senioritySalary" },
	{ title: "加班", row: 0, col: 10, key: "overtimeSalary" },
	{ title: "高温", row: 0, col: 11, key: "highTempSubsidy" },
	{ title: "应发工资", row: 0, col: 12, key: "salaryWithAllowance" },
	{ title: "缺勤扣减", row: 0, col: 13, key: "deduct" },
	{ title: "应扣金额", row: 0, col: 14, rs: 1, cs: 5 },

	//数据保留两位小数
	{ title: "社保", row: 1, col: 14, rs: 1, key: "socialSecurity" },
	{ title: "医疗保险", row: 1, col: 15, rs: 1, key: "medicalInsurance" },
	{ title: "失业保险", row: 1, col: 16, rs: 1, key: "unemployInsurance" },
	{ title: "公积金", row: 1, col: 17, rs: 1, key: "providentFund" },
	{ title: "个人所得税", row: 1, col: 18, rs: 1, key: "tax" },
	{ title: "实发工资", row: 0, col: 19, key: "endSalary" },
	{ title: "伙食", row: 0, col: 20, key: "eatSubsidy" },
	{ title: "所属部门", row: 0, col: 21, key: "department" },
	{ title: "备注", row: 0, col: 22, key: "remark" },
	{ title: "船员剩余假期", row: 0, col: 23, key: "remainderDays" }
	// { title: "厨师补贴", row: 0, col: 17 },
	// { title: "出海补贴", row: 0, col: 18 }
];
/** 表头celldata格式数据 */
export const sheetHeader: CellItem[] = header.map(i => ({
	r: i.row,
	c: i.col,
	v: {
		ct: {
			fa: "@",
			t: "s"
		},
		fs: 8,
		ff: "宋体",
		ht: 0,
		vt: 0,
		tb: 2,
		v: i.title,
		m: i.title,
		mc: {
			r: i.row,
			c: i.col,
			rs: i.rs || 2,
			cs: i.cs || 1
		}
	}
}));
/** 表尾合计 */
export const sheetFooter = (param: {
	row: number;
	startRowIndex: number;
	endRowIndex: number;
	sumRows: string[];
}): CellItem[] => {
	const { row, startRowIndex, endRowIndex, sumRows } = param;
	return [
		{
			r: row,
			c: 0,
			v: {
				ct: {
					fa: "@",
					t: "s"
				},
				fs: 10,
				ff: "宋体",
				ht: 0,
				vt: 0,
				tb: 2,
				v: "合计",
				m: "合计",
				mc: {
					r: row,
					c: 0,
					rs: 1,
					cs: 3
				}
			}
		},
		...sumRows.map((letter, index) => ({
			r: row,
			c: index + 3,
			v: {
				fs: 10,
				ct: {
					fa: index + 3 >= fixedStartCol ? "0.00" : "0",
					t: "n"
				},
				ff: "宋体",
				ht: 0,
				vt: 0,
				tb: 2,
				f: `=SUM(${letter}${startRowIndex + 1}:${letter}${endRowIndex + 1})`
			}
		}))
	];
};
//从社保开始后面的列保留两位 计算社保列号
const fixedStartCol = header.find(i => i.key === "socialSecurity")!.col;
/** 根据用户信息 计算得薪资表一行的数据 */
export const oneColumn = (data: UserData) => {
	const { index: colIndex } = data;
	return header
		.filter(i => i.title !== "应扣金额")
		.map((i, index) => {
			const contentItem: CellItem = {
				r: colIndex,
				c: index,
				v: {
					ct: {
						fa: "0",
						t: "n"
					},
					bg: data.bg || (data.userIndex === undefined || data.userIndex % 2 === 1 ? "#ffffff" : "#d9d9d9"),
					fs: 10,
					ff: "宋体",
					ht: 0,
					vt: 0,
					tb: 1,
					v: ((i.key && data[i.key]) ?? "") as number | string,
					m: ((i.key && data[i.key]) ?? "") as number | string
				}
			};
			const col = colIndex + 1;
			if (i.key === "endSalary") {
				//实发工资
				contentItem.v.f = `=M${col}-SUM(N${col}:S${col})`;
			} else if (i.key === "salaryWithAllowance") {
				// 应发工资
				contentItem.v.f = `=SUM(G${col}:L${col})`;
			} else if (i.key === "tax") {
				// 个税填充0
				contentItem.v.v = 0;
				contentItem.v.m = 0;
			} else if (i.key === "userIndex" && data.userIndex !== undefined) {
				// 序号
				contentItem.v.v = data.userIndex + 1;
			}
			if (i.col >= fixedStartCol) {
				contentItem.v.ct = { fa: "0.00", t: "n" };
				if (i.key === "remainderDays") {
					contentItem.v.ct = { fa: "0.0", t: "n" };
				}
			}
			return contentItem;
		});
};
export interface UserData {
	/** 副卡工资 */
	secondCardSalary?: number;
	/** 是否是副卡 */
	isSecondCard?: boolean;
	/** 序号 */
	index: number;
	/** 在用户列表中序号 */
	userIndex?: number;
	/** 背景颜色 */
	bg?: string;
	/** 姓名 */
	name?: string;
	/** 备注 */
	remark?: string;
	/** 工资分摊岗位 */
	salaryPosition?: string;
	/** 岗位工资 */
	positionSalary?: number;
	/** 薪酬 */
	salary?: number;
	/** 津贴 */
	/** 工龄工资 */
	senioritySalary?: number;
	/** 加班 */
	overtimeSalary?: number;
	/** 高温 */
	/** 薪酬扣减 */
	deduct?: number;
	/** 实发工资 */
	endSalary?: number;
	/** 基本工资 */
	basicSalary?: number;
	/** 社保 */
	socialSecurity?: number;
	/** 医保 */
	medicalInsurance?: number;
	/** 失业保 */
	unemployInsurance?: number;
	/** 公积金 */
	providentFund?: number;
	/** 应发工资 */
	salaryWithAllowance?: number;
	/** 副卡从属人姓名 */
	fromUserName?: string;
	[key: string]: number | string | boolean | undefined;
}
/** 单元格 */
export interface CellItem {
	/** 列数 */
	r: number;
	/** 行数 */
	c: number;
	/** 内容 */
	v: CellValue;
	/** 是否是标题 */
	isTitle?: boolean;
}

export interface CellValue {
	/** 格值格式 */
	ct?: {
		fa: string;
		t: string;
	};
	/** 背景颜色 */
	bg?: string;
	/** 字体 */
	fs?: number;
	/** 字体颜色 */
	ff?: string;
	/** 水平对齐 0 居中、1 左、2右 */
	ht?: number;
	/** 垂直对齐 0 中间、1 上、2下 */
	vt?: number;
	/** 文本换行 0 截断、1溢出、2 自动换行 */
	tb?: number;
	/** 原始值 */
	v?: string | number;
	/** 显示值 */
	m?: string | number;
	/** 合并单元格 */
	mc?: {
		r: number;
		c: number;
		rs: number;
		cs: number;
	};
	/** 公式 */
	f?: string;
	/** 批注 */
	ps?: {
		value: string;
	};
}

/** 考勤表数据项 */
export interface AttendanceData {
	name: string;
	workDay: number;
	illDay?: number;
	compensatoryDay?: number;
	annualLeaveDay?: number;
	shipName?: string;
	remark?: string;
	dividend?: boolean;
	lastHalfDay?: number;
	firstHalfDay?: number;
	wholeDay?: number;
	basicSalaryDay?: number;
	internet?: boolean;
}
/** 加班表数据 */
export interface OvertimeData {
	name: string;
	overtime: number;
}
/** 出海补贴数据 */
export interface SeaSubsidyData {
	name: string;
	seaDays: number;
	workDays: number;
}
/** 表单数据 */
export interface FormData {
	month: string;
	workerAttendance: AttendanceData[];
	tempAttendance: AttendanceData[];
	tempOverTime: OvertimeData[];
	internetAttendance: AttendanceData[];
	overtime: OvertimeData[];
	seaSubsidy: SeaSubsidyData[];
	restDayWeekend: number;
	restDay: number;
	unShowData: any[];
	payroll?: string;
	isFinal?: boolean;
	finalPayroll?: string;
	payrollName?: string;
	hasImport?: boolean;
	shipManLeaveList?: any[];
	/** 表格渲染数据 用于step2渲染sheet */
	celldata: any[];
	/** step2回退时 计算出的修改记录 */
	diffResult?: {
		[key: string]: {
			field: string;
			oldValue: string | number;
			newValue: string | number;
		}[];
	};
	/** 按人名存储的原始计算值 */
	originMap: { [key: string]: any[] };
	/** 按人名存储的批注记录 */
	personPsList: { [key: string]: any[] };
}
/** 薪资特殊人员 - 固定分红 */
export const DEIVIDEND_NAMES = ["王信飞", "王信辉"];

/** 薪资特殊人员 - 只显示副卡、不交五险一金 */
export const ONLY_SHOW_SECOND_CARD_NAMES = ["韩重洋"];

/** 每人每月高温补贴金额 */
export const HIGH_TEMP_SUBSIDY = 100;
