<!-- 编辑个税 -->
<template>
	<div style="height: 100%">
		<div class="header">
			<el-button @click="exportExcel">导出无个税表格</el-button>
			<ImportButton v-model="formData.hasImport" :fileSize="10" :originMap="originUserMap" />
			<p class="tag">*导出的表格不含个税信息，导入的表格姓名需与导出表格匹配</p>
		</div>
		<div class="content" v-loading="loading">
			<div style="width: 100%; height: 100%">
				<div id="luckysheet" class="excel" />
				<!-- <span>暂无数据</span> -->
			</div>
		</div>
	</div>
</template>
<script lang="ts" setup>
import { inject, Ref, ref, onMounted } from "vue";
import { exportSheetExcel, getContentUrl, getContentByUrl } from "../../utils";
import { header } from "../constants";
import { PayItem } from "@/api/interface/member";

import ImportButton from "./ImportButton.vue";
import { SalaryService, LeaveRecordService, SalaryOptionService } from "@/api/modules/salary";
import { MemberService } from "@/api/modules/member";
import { ElMessage, ElMessageBox } from "element-plus";
import { storageRef } from "@/meta-components/MetaForm/storage";
import { format } from "date-fns";
import { SaveType } from "../constants";
import { useLuckysheet } from "../hooks/useLuckySheet";
import { SalaryItem, SalaryOption } from "@/api/interface/salary.model";
import { CellValue } from "../constants";

interface CellItem {
	row: number;
	col: number;
	value: CellValue;
}

const formData = inject("formData")! as Ref<{
	[key: string]: any;
}>;
//表格中 用户姓名->行号
const originUserMap = new Map();
const loading = ref(false);
const excelData = ref();
const { initLuckysheet } = useLuckysheet({
	containerId: "luckysheet",
	title: () => formData.value.payrollName,
	data: excelData,
	getData: () => []
});
/** 根据Step2 复制一张表*/
const sheetInit = async () => {
	loading.value = true;
	try {
		const url = formData.value.finalPayroll || formData.value.payroll;
		if (!url) return;
		let detailData = await getContentByUrl(url);
		if (!detailData) return;
		let fromDraft = !!formData.value.finalPayroll;
		// 加载step2数据到在线表格中
		// 3. 批量处理数据
		const parsedData = JSON.parse(detailData);
		excelData.value = parsedData;

		//取第一张表数据
		const sheet1 = excelData.value[0];
		if (sheet1 && sheet1.config) {
			// @ts-ignore
			delete sheet1.config.colhidden;
		}
		initLuckysheet();
		const nameIndex = header.findIndex((i: { title: string }) => i.title === "姓名");
		originUserMap.clear();
		// @ts-ignore
		luckysheet.getSheetData().forEach((row, index) => {
			if (index === 0) return;
			if (row[nameIndex]?.m) {
				originUserMap.set(row[nameIndex].m, index);
			}
		});
		if (!fromDraft) {
			// 非来自草稿 初始化数据
			sheetInsert();
			formData.value.hasImport = false;
		}
		sheet1.config.authority = {
			//当前工作表的权限配置
			sheet: 1, //如果为1或true，则该工作表受到保护；如果为0或false，则该工作表不受保护。
			hintText: "当前表仅可查看" //弹窗提示的文字
		};
		// @ts-ignore
		luckysheet.setConfig(sheet1.config);
	} catch (error) {
		console.log(error);
	} finally {
		loading.value = false;
	}
};

const sheetInsert = () => {
	// @ts-ignore
	luckysheet.insertColumn(14, { number: 1 });
	let titleIndex = 0;

	const updates: CellItem[] = [];
	// @ts-ignore
	luckysheet.getSheetData().forEach((row, index) => {
		if (index === 0) return;
		//一行数据
		if (row[14]) {
			titleIndex = index;
			return;
		}
		if (index == titleIndex + 1) {
			//标题行
			updates.push({
				row: index,
				col: 14,
				value: {
					v: "本期收入",
					fs: 8,
					ff: "宋体",
					ht: 0,
					vt: 0,
					mc: { r: index, c: 14, rs: 2, cs: 1 }
				}
			});
		} else if (index != titleIndex + 2 && row[0]) {
			updates.push({
				row: index,
				col: 14,
				value: {
					bg: row[0].bg,
					fs: 10,
					ff: "宋体",
					ht: 0,
					vt: 0,
					tb: 1,
					v: (row[12].v || 0) - (row[13].v || 0) - (row[5].v || 0),
					mc: { r: index, c: 14, rs: 1, cs: 1 }
				}
			});
			//实发工资公式修改
			updates.push({
				row: index,
				col: 20,
				value: {
					v: row[20].v,
					f: row[20].f ? convertSumFormula(row[20].f) : undefined
				}
			});
		}
	});
	// 7. 批量更新单元格
	batchUpdateCells(updates);
	// 刷新公式链
	// @ts-ignore
	const sheet = luckysheet.getSheet();
	const calcChain: any[] = [];
	sheet.data.forEach((row: any[], rowIndex: number) => {
		row.forEach((cell: any, colIndex: number) => {
			if (cell?.f) {
				calcChain.push({ r: rowIndex, c: colIndex, index: 0, func: [true, cell.v, cell.f] });
			}
		});
	});
	sheet.calcChain = calcChain;
};

const convertSumFormula = (formula: string) => {
	// 匹配SUM(Nx:Sx)格式
	const sumRangeRegex = /SUM\(([A-Z])(\d+):([A-Z])\2\)/g;

	return formula.replace(sumRangeRegex, (match, startCol, rowNum) => {
		// 需要sum的列（除了O列）
		const columns = ["N", "P", "Q", "R", "S", "T"];

		// 生成新的SUM表达式
		return `SUM(${columns.map(col => `${col}${rowNum}`).join(",")})`;
	});
};

// 批量更新单元格的辅助函数
function batchUpdateCells(updates: Array<{ row: number; col: number; value: any }>) {
	// @ts-ignore
	updates.forEach(({ row, col, value }, index: number) => {
		// @ts-ignore
		luckysheet.setCellValue(row, col, value, { isRefresh: index === updates.length - 1 });
	});
}

const exportExcel = () => {
	//@ts-ignore
	//copy出一个临时sheetData，不影响原sheetData情况下修改导出数据
	const tempSheet = JSON.parse(JSON.stringify(luckysheet.getluckysheetfile()));
	// 隐藏副卡
	// tempSheet[0].data = tempSheet[0].data.filter((item: any) => item[0] && item[0].bg !== "#bdd4f7");
	const merge: { [key: string]: any } = {};
	let startIndex = 0,
		endIndex = -5;
	tempSheet[0].data.forEach((column: any, rowIndex: number) => {
		column.forEach((cell: any, columnIndex: number) => {
			if (cell && cell.mc && (cell.mc.rs || cell.mc.cs)) {
				merge[`${rowIndex}_${columnIndex}`] = {
					r: rowIndex,
					c: columnIndex,
					rs: cell.mc.rs || 1,
					cs: cell.mc.cs || 1
				};
			}
			// 转换原公式
			if (cell && cell.f && cell.f !== "") {
				if ([12, 20].includes(columnIndex) && rowIndex !== endIndex + 1) {
					//用户信息行 更新应发工资、实发工资公式
					if (startIndex === 0 || rowIndex === endIndex + 5) {
						startIndex = rowIndex;
					}
					if (columnIndex === 12) {
						//应发工资
						return (cell.f = `=G${rowIndex + 1}+H${rowIndex + 1}+I${rowIndex + 1}+J${rowIndex + 1}+K${rowIndex + 1}+L${
							rowIndex + 1
						}`);
					} else {
						//实发工资 删除公式直接显示值
						return delete cell.f;
					}
				} else if (columnIndex === 3) {
					//合计行
					endIndex = rowIndex - 1;
				}
				const letter = cell.f.split("(")[1][0];
				cell.f = `=SUM(${letter}${startIndex + 1}:${letter}${endIndex + 1})`;
			}
		});
	});
	tempSheet[0].config.merge = merge;
	tempSheet[0].calcChain = [];
	tempSheet[0].config.rowlen = {};
	//@ts-ignore
	exportSheetExcel(tempSheet, formData.value.payrollName);
};

/**保存 */
async function save(isSaveDraft: SaveType = SaveType.SUBMIT) {
	//	若为保存 需要弹窗二次确认
	if (isSaveDraft === SaveType.SUBMIT) {
		//默认提示文本
		let messageText = "提交后本次新建的数据将无法再次编辑，是否确认保存?";
		let buttonText = "确认";

		// 判断是否导入个税
		if (!formData.value.hasImport) {
			messageText = "个税信息未导入，保存后可能导致个税计算不完整。是否继续保存?";
			buttonText = "继续保存";
		}

		// 判断是否会影响后续月份
		const res = await SalaryService.getImpactMonth({ month: formData.value.month });
		if (res.data?.month && res.data.month.length > 0) {
			const monthText = format(new Date(formData.value.month), "yyyy年MM月");
			const impactMonthText = res.data.month.map((item: any) => format(new Date(item), "yyyy年MM月")).join("、");
			messageText = `修改“${monthText}”工资可能对后续已生成的“${impactMonthText}”月的工资产生影响，建议重新计算。`;
			buttonText = "保存";
		}
		try {
			await ElMessageBox.confirm(messageText, "注意", {
				confirmButtonText: buttonText,
				cancelButtonText: "取消",
				type: "warning"
			});
			// 用户点击确认，继续执行
		} catch (err) {
			// 用户点击取消，终止保存
			return false;
		}
	}
	loading.value = true;
	const { shipManLeaveList, month } = formData.value;
	// 在删除列之前先保存第12列的数据
	const saveColumn12Data = () => {
		// @ts-ignore
		const sheetData = luckysheet.getSheetData();
		return sheetData.map((row: any[]) => row[12]);
	};

	// 删除列后恢复第12列数据
	const restoreColumn12Data = (savedData: any[]) => {
		const lastIndex =
			savedData.length -
			1 -
			savedData
				.slice()
				.reverse()
				.findIndex((item: any) => item);
		savedData.forEach((value, rowIndex) => {
			if (value) {
				// @ts-ignore
				luckysheet.setCellValue(rowIndex, 12, value, { isRefresh: lastIndex === rowIndex });
			}
		});
	};

	const handleDeleteColumn = () => {
		const savedData = saveColumn12Data();
		// @ts-ignore
		luckysheet.deleteColumn(14, 14);
		restoreColumn12Data(savedData);
	};

	// 去除 本期收入 这一列
	if (isSaveDraft === SaveType.SUBMIT) {
		handleDeleteColumn();
	}
	// 强制刷公式
	// @ts-ignore
	luckysheet.refreshFormula();
	loading.value = false;

	// @ts-ignore
	// 此Api就是自动退出编辑模式的操作，主要是为了触发自动保存单元格。
	luckysheet.exitEditMode();
	// @ts-ignore
	const fileName = luckysheet.getWorkbookName();
	// @ts-ignore
	const content = JSON.stringify(luckysheet.getLuckysheetfile());
	const url = await getContentUrl(content, fileName + "含个税");
	// 保存最终文件
	formData.value.finalPayroll = url;
	if (isSaveDraft === SaveType.SUBMIT) {
		loading.value = false;

		// 保存最终文件
		formData.value.isFinal = true;
		delete formData.value.hasImport;
		const res = await SalaryService.update(formData.value);
		if (res.code === 0) {
			if (shipManLeaveList?.length) {
				await LeaveRecordService.update({ month: format(new Date(month), "yyyy-MM"), list: shipManLeaveList });
			}
			// // 更新详细工作表
			saveExcelToData();
			storageRef.clearDraft("add-salary"); //手动清除draft
			ElMessage.success("保存成功！");
		} else {
			return false;
		}
	} else {
		ElMessage.success("草稿保存成功！");
	}

	loading.value = false;
	return true;
}
// 薪资基础配置
const salaryOption = ref<SalaryOption.SalaryOption>();
// 企业五险一金
let corpSocialMap = new Map();
// 特殊五险一金
let specialSocialMap = new Map();
let userNameInfoMap = new Map();
const getSalaryOption = async () => {
	// 获取薪资基础配置及特殊五险一金、企业五险一金
	SalaryOptionService.getDetail().then(res => {
		if (res.code === 0) {
			const { specialOption, corpSocialHousingOption = [] } = res.data as SalaryOption.SalaryOption;
			if (corpSocialHousingOption) {
				corpSocialMap = new Map(corpSocialHousingOption.map((item: any) => [item.name, item]));
			}
			if (specialOption?.privateOption) {
				specialSocialMap = new Map(
					specialOption.privateOption.map((item: any) => [
						item.name,
						{ private: item, corp: specialOption.corpOption?.find(i => i.name === item.name) }
					])
				);
			}
			salaryOption.value = res.data;
		}
	});

	MemberService.getListForSalary({ offset: -1, length: -1 }).then(res => {
		if (res.data && res.data.list) {
			userNameInfoMap = new Map(res.data.list.map((item: any) => [item.name, item]));
		}
	});
};
/** excel表头名->index映射 */
const headerIndexMap = new Map(header.filter(i => i.key).map((item: any) => [item.key, item.col]));

/**保存excel数据到数据库 */
const saveExcelToData = async () => {
	try {
		// @ts-ignore
		const excelItems = luckysheet.getSheetData();
		const { list, unFindSecondCardRow } = parseExcelData(excelItems);
		handleUnshownData(list, unFindSecondCardRow);
		await saveItems(list);
	} catch (e) {
		console.error(e);
	}
};
/** 将excel数据转换为SalaryItems对象 */
const parseExcelData = (excelItems: any[]) => {
	let companyName = excelItems[0][0].m;
	const list: SalaryItem.SalaryItems[] = [];
	const unFindSecondCardRow: any[] = [];
	excelItems.forEach((row: any[], index: number) => {
		const rowType = detectRowType(row, companyName);
		switch (rowType) {
			case "COMPANY_NAME":
				companyName = excelItems[index + 1]?.[0]?.m;
				break;
			case "USER_DATA":
				//处理用户数据
				handleUserRow(row, companyName, list);
				break;
			case "VICE_CARD":
				//处理副卡行
				handleViceCardRow(row, list, unFindSecondCardRow);
				break;
			case "HEADER":
				//处理表头
				break;
			default:
				break;
		}
	});
	return { list, unFindSecondCardRow };
};
/* 工具函数 获取工资对应key单元格value */
const getCellValue = (row: any[], key: string, forceNumber = true) => {
	const cellValue = row[headerIndexMap.get(key)]?.m;
	// 处理undefined或null
	if (cellValue == null) return forceNumber ? 0 : "";

	// 检查是否为数字或可转换为数字的字符串
	const isNumeric = /^-?\d*\.?\d*$/.test(String(cellValue).trim()) && String(cellValue).trim() !== "";

	if (isNumeric || cellValue === "") {
		// 数字或空字符串，转为数字
		return forceNumber ? Number(cellValue) || 0 : cellValue;
	} else {
		// 纯文本，保持原样
		return cellValue;
	}
};
/* 判断行类型 */
const detectRowType = (row: any[], companyName: string) => {
	const firstCell = row[0]?.m;
	if (firstCell === "序号") return "HEADER";
	if (firstCell === "合计") return "COMPANY_NAME";
	if (firstCell === companyName) return "HEADER";
	if (!firstCell && row[0]?.bg === "#bdd4f7") return "VICE_CARD";
	return "USER_DATA";
};
const stringFields = ["company", "department", "month", "name", "position"];
/* 处理用户数据行 */
const handleUserRow = (row: any[], companyName: string, list: any[]) => {
	const userData: any = { company: companyName };
	headerIndexMap.forEach((value, key) => {
		userData[key] = row[value]?.m ?? (stringFields.includes(key) ? "" : 0);
	});

	if (!userData.name) return;
	const userItem = buildUserItem(userData);
	list.push(userItem);
};
/** 构成单项salaryItem */
const buildUserItem = (userData: any): SalaryItem.SalaryItems => {
	const userInfo = userNameInfoMap.get(userData.name);

	// 用户企业部分五险一金
	const corpSocial: any = {};
	const corpOption = specialSocialMap.get(userData.name)?.corp || corpSocialMap.get(userData.company) || {};
	if (userInfo?.payItems?.includes(PayItem.Insurances)) {
		corpSocial.pensionInsurance = corpOption.pensionInsurance ?? 0;
		corpSocial.medicalInsurance = corpOption.medicalInsurance ?? 0;
		corpSocial.unemployInsurance = corpOption.unemployInsurance ?? 0;
		corpSocial.workersInsurance = corpOption.workersInsurance ?? 0;
	}
	if (userInfo?.payItems?.includes(PayItem.Fund)) {
		corpSocial.providentFund = corpOption.providentFund ?? 0;
	}
	// 薪资信息
	return {
		month: format(new Date(formData.value.month), "yyyy-MM"),
		company: userData.company,
		department: userData.department,
		position: userData.salaryPosition,
		name: userData.name,
		basicSalary: Number(userData.basicSalary),
		positionSalary: Number(userData.positionSalary),
		communicationSubsidy: Number(userData.communicationSubsidy),
		salary: Number(userData.salary),
		salaryWithAllowance: Number(userData.salaryWithAllowance),
		//补贴
		allowance: Number(userData.allowance),
		seaSubsidy: Number(userData.seaSubsidy),
		senioritySalary: Number(userData.senioritySalary),
		overtimeSalary: Number(userData.overtimeSalary),
		highTempSubsidy: Number(userData.highTempSubsidy),
		deduct: Number(userData.deduct),
		//五险一金(个人)
		personalSocialSecurity:
			Number(userData.socialSecurity) + Number(userData.medicalInsurance) + Number(userData.unemployInsurance),
		personalProvidentFund: Number(userData.providentFund),
		//五险一金(企业)
		companySocialSecurity:
			Number(corpSocial.pensionInsurance ?? 0) +
			Number(corpSocial.medicalInsurance ?? 0) +
			Number(corpSocial.unemployInsurance ?? 0) +
			Number(corpSocial.workersInsurance ?? 0),
		companyProvidentFund: Number(corpSocial.providentFund ?? 0),
		tax: Number(userData.tax),
		endSalary: Number(userData.endSalary),
		eatSubsidy: Number(userData.eatSubsidy),
		//副卡
		viceCardName: userNameInfoMap.get(userData.name)?.secondCardName || "",
		viceCardSalary: Number(userNameInfoMap.get(userData.name)?.secondCardSalary) || 0,
		viceCardEndSalary: Number(userNameInfoMap.get(userData.name)?.secondCardSalary) || 0
	};
};
/** 处理副卡行数据 */
const handleViceCardRow = (row: any[], list: SalaryItem.SalaryItems[], unFindRows: any[]) => {
	const lastUser = list[list.length - 1];
	const currentName = getCellValue(row, "name");
	if (lastUser?.viceCardName === currentName) {
		updateViceCardData(lastUser, row);
	} else {
		unFindRows.push(row);
	}
};
/** 更新副卡相关属性 */
const updateViceCardData = (user: any, row: any[]) => {
	user.viceCardDeduct = getCellValue(row, "deduct");
	user.viceCardSalary = getCellValue(row, "salary");
	user.viceCardEndSalary = getCellValue(row, "endSalary");
	user.viceCardWagesPayable = getCellValue(row, "salaryWithAllowance");
};
/** 处理隐藏数据 */
const handleUnshownData = (list: SalaryItem.SalaryItems[], unFindSecondCardRow: any[]) => {
	if (formData.value.unShowData?.length) {
		formData.value.unShowData.forEach((item: any) => {
			const userInfo = userNameInfoMap.get(item.name);
			const companyName = userInfo?.salaryPosition?.[0];
			//五险一金判断
			const corpSocial: any = {};
			//企业五险一金
			const corpOption = specialSocialMap.get(item.name)?.corp || corpSocialMap.get(companyName) || {};
			if (userInfo?.payItems?.includes(PayItem.Insurances)) {
				corpSocial.pensionInsurance = corpOption.pensionInsurance ?? 0;
				corpSocial.medicalInsurance = corpOption.medicalInsurance ?? 0;
				corpSocial.unemployInsurance = corpOption.unemployInsurance ?? 0;
				corpSocial.workersInsurance = corpOption.workersInsurance ?? 0;
			}
			if (userInfo?.payItems?.includes(PayItem.Fund)) {
				corpSocial.providentFund = corpOption.providentFund ?? 0;
			}
			const userItemData: SalaryItem.SalaryItems = {
				//薪资信息
				month: format(new Date(formData.value.month), "yyyy-MM"),
				company: item.company,
				department: item.department,
				position: item.salaryPosition,
				name: item.name,
				basicSalary: Number(item.basicSalary),
				positionSalary: Number(item.positionSalary),
				communicationSubsidy: Number(item.communicationSubsidy ?? 0),
				salary: Number(item.salary),
				salaryWithAllowance:
					Number(item.salary ?? 0) +
					Number(item.allowance ?? 0) +
					Number(item.seaSubsidy ?? 0) +
					Number(item.senioritySalary ?? 0) +
					Number(item.overtimeSalary ?? 0) +
					Number(item.highTempSubsidy ?? 0),
				//补贴
				allowance: Number(item.allowance ?? 0),
				seaSubsidy: Number(item.seaSubsidy ?? 0),
				senioritySalary: Number(item.senioritySalary ?? 0),
				overtimeSalary: Number(item.overtimeSalary ?? 0),
				highTempSubsidy: Number(item.highTempSubsidy ?? 0),
				deduct: Number(item.deduct),
				//五险一金(个人)
				personalSocialSecurity:
					Number(item.socialSecurity ?? 0) + Number(item.medicalInsurance ?? 0) + Number(item.unemployInsurance ?? 0),
				personalProvidentFund: Number(item.providentFund ?? 0),
				//五险一金(企业)
				companySocialSecurity:
					Number(corpSocial.pensionInsurance ?? 0) +
					Number(corpSocial.medicalInsurance ?? 0) +
					Number(corpSocial.unemployInsurance ?? 0) +
					Number(corpSocial.workersInsurance ?? 0),
				companyProvidentFund: Number(corpSocial.providentFund ?? 0),
				tax: Number(item.tax),
				eatSubsidy: Number(item.eatSubsidy)
			};
			const secondCardRow = unFindSecondCardRow.find(
				i => i[headerIndexMap.get("name")]?.m === userNameInfoMap.get(userItemData.name)?.secondCardName
			);
			if (secondCardRow) {
				userItemData.viceCardName = secondCardRow[headerIndexMap.get("name")]?.m;
				userItemData.viceCardDeduct = Number(secondCardRow[headerIndexMap.get("deduct")]?.m);
				userItemData.viceCardSalary = Number(secondCardRow[headerIndexMap.get("salary")]?.m);
				userItemData.viceCardEndSalary = Number(secondCardRow[headerIndexMap.get("endSalary")]?.m);
				userItemData.viceCardWagesPayable = Number(secondCardRow[headerIndexMap.get("salaryWithAllowance")]?.m);
			}
			list.push(userItemData);
		});
	}
};

/** 批量保存该月薪资条项 */
const saveItems = async (list: SalaryItem.SalaryItems[]) => {
	const data = {
		month: format(new Date(formData.value.month), "yyyy-MM"),
		list
	};

	const res = await SalaryService.updateItems(data);
	if (res.code !== 0) throw new Error(res.msg);
};

onMounted(() => {
	getSalaryOption();
	sheetInit();
});
defineExpose({
	tryToNext: save
});
</script>
<style lang="scss" scoped>
.header {
	height: 72px;
	.tag {
		margin: 8px 0;
		font-size: 14px;
		color: #e6a23c;
	}
	.upload {
		display: inline-block;
		margin-left: 8px;
	}
}
.content {
	width: 100%;
	height: calc(100% - 72px);
	.excel {
		width: 100%;
		height: 100%;
		padding: 0;
		margin: 0;
	}
	.placeholder-box {
		padding: 20px 40px;
		margin-top: 20px;
		color: $color-text-primary;
		cursor: default;
		border: 1px solid #ebeef5;
		border-radius: 5px;
	}
}
</style>
