<!-- Step1.5 数据对比步骤  -->
<template>
	<div class="container" v-if="diffList.length">
		<div class="header-row">
			<div class="person-name"></div>
			<div class="col col-new">
				<el-checkbox v-model="newChecked" @change="onSelectAll('new')">新导入数据</el-checkbox>
			</div>
			<div class="col col-field"></div>
			<div class="col col-old">
				<el-checkbox v-model="oldChecked" @change="onSelectAll('old')">上次修改数据</el-checkbox>
			</div>
		</div>
		<div class="person-change-item" v-for="person in diffList" :key="person.name">
			<el-divider />
			<div class="row-group">
				<div class="person-name">{{ person.name }}：</div>
				<div class="col col-new">
					<div v-for="field in person.fields" :key="field.field" class="cell">
						<template v-if="field.newValue === field.oldValue">
							<!-- 新旧值相同，只显示一个不可选的checkbox -->
							<el-checkbox :checked="true" disabled>
								{{ field.newValue || "-" }}
							</el-checkbox>
						</template>
						<template v-else>
							<el-checkbox
								:true-label="field.newValue"
								:false-label="field.oldValue"
								v-model="selectedResult[person.name][field.field].value"
							>
								{{ field.newValue || "-" }}
							</el-checkbox>
						</template>
					</div>
				</div>
				<div class="col col-field">
					<div v-for="field in person.fields" :key="field.field" class="cell field-cell">
						{{ field.field }}
					</div>
				</div>
				<div class="col col-old">
					<div v-for="field in person.fields" :key="field.field" class="cell">
						<el-checkbox
							:true-label="field.oldValue"
							:false-label="field.newValue"
							v-model="selectedResult[person.name][field.field].value"
						>
							{{ field.oldValue || "-" }}
						</el-checkbox>
					</div>
				</div>
			</div>
		</div>
	</div>
	<div v-else v-loading="true" style="height: 100%"></div>
</template>
<script lang="ts" setup>
import { inject, Ref, ref, watch } from "vue";
import { useSalaryData } from "../hooks/useSalaryData";
import { FormData, header } from "../constants";

const formData = inject("formData")! as Ref<FormData>;
/** 解析文件数据 构成用户薪资信息、船员使用年休假列表 */
const { celldata, dataReady, shipManLeaveList } = useSalaryData(formData);
const diffList = ref<any[]>([]);
const selectedResult = ref<Record<string, Record<string, any>>>({}); // {姓名: {字段: 选中的值}}

/** 平铺celldata转二维数组形式 */
function celldataTo2DArray(celldata: { r: number; c: number; v: any }[]) {
	const result: any[][] = [];
	celldata.forEach(cell => {
		if (!result[cell.r]) result[cell.r] = [];
		result[cell.r][cell.c] = cell.v;
	});
	return result;
}

/** 根据最新计算值和历史修改数据生成 对比数据 */
const getDiffResult = () => {
	const diffArr = [];
	const twoDimArr = celldataTo2DArray(celldata.value);
	const nameIndex = header.findIndex(item => item.title === "姓名");
	const diffResult = formData.value.diffResult;
	const tempSelected: Record<string, Record<string, any>> = {};
	for (let i = 3; i < twoDimArr.length; i++) {
		const row = twoDimArr[i];
		// 当前用户新计算出的各项值
		const userFieldMap = new Map(header.map(item => [item.title, { value: row[item.col], c: item.col }]));
		const name = row[nameIndex]?.v;
		// 存在修改数据
		const changeItems = diffResult?.[name];
		if (changeItems) {
			const fields = changeItems.map(changed => {
				const newCellData = userFieldMap.get(changed.field);
				// 默认选 newValue(修改值)
				if (!tempSelected[name]) tempSelected[name] = {};
				tempSelected[name][changed.field] = { value: changed.newValue, position: { c: newCellData?.c, r: i } };
				return {
					field: changed.field,
					newValue: newCellData?.value?.v,
					oldValue: changed.newValue,
					position: {
						c: newCellData?.c,
						r: i
					}
				};
			});
			diffArr.push({
				name,
				fields
			});
			selectedResult.value = tempSelected;
		}
	}
	diffList.value = diffArr;
	if (diffArr.length === 0) {
		// 存在修改记录 但最终对比结果为空 也跳过第二步
		// 比如 修改过的人员 在二次上传时被删除
		emit("skip");
	}
};
/** 只要重新获取了cellData 存储一遍原始数据map */
const saveOrigin = () => {
	const twoDimArr = celldataTo2DArray(JSON.parse(JSON.stringify(celldata.value)));
	const nameIndex = header.findIndex(item => item.title === "姓名");
	// 存储一份原始计算值用于step2对比
	const originMap: { [key: string]: any[] } = {};
	for (let i = 3; i < twoDimArr.length; i++) {
		const row = twoDimArr[i];
		const name = row[nameIndex]?.v;
		if (name) {
			originMap[name] = row;
		}
	}
	formData.value.originMap = originMap;
};
const emit = defineEmits(["skip"]);
watch(
	() => dataReady.value,
	() => {
		if (dataReady.value === true) {
			saveOrigin();
			if (formData.value.diffResult && Object.keys(formData.value.diffResult).length > 0) {
				getDiffResult();
			} else {
				// 不存在修改记录 直接跳过选择数据这一步
				emit("skip");
			}
		}
	}
);

const newChecked = ref(false);
const oldChecked = ref(true);

// 选择某边全部
function onSelectAll(val: any) {
	const { diffResult } = formData.value;
	if (!diffResult) return;
	if (val === "new") {
		oldChecked.value = false;
		newChecked.value = true;
	} else {
		oldChecked.value = true;
		newChecked.value = false;
	}
	// if (Object.keys(selectedResult.value).length === 0) return;
	for (const person of diffList.value) {
		const name = person.name;
		if (!selectedResult.value[name]) selectedResult.value[name] = {};
		for (const field of person.fields) {
			selectedResult.value[name][field.field].value = val === "new" ? field.newValue : field.oldValue;
		}
	}
}
const save = () => {
	// 计算数据加载完成
	// 存在修改记录 保存应保留的历史修 覆盖celldata
	if (diffList.value.length) {
		const nameIndex = header.findIndex(item => item.title === "姓名");
		let curPersonName = "";
		const newCelldata = celldata.value.map(cell => {
			const { r, c, v } = cell;
			if (nameIndex === c && v.v) {
				curPersonName = v.v as string;
			}
			if (!selectedResult.value[curPersonName]) return cell;
			const curPersonData = selectedResult.value[curPersonName];
			for (const field in curPersonData) {
				if (curPersonData[field].position.r === r && curPersonData[field].position.c === c) {
					cell.v.v = curPersonData[field].value;
				}
			}
			return cell;
		});
		formData.value.celldata = newCelldata;
	} else {
		formData.value.celldata = celldata.value;
	}
	// 船员剩余休假天数按计算值保存
	formData.value.shipManLeaveList = shipManLeaveList.value;
	delete formData.value.diffResult;
	return dataReady.value;
};
defineExpose({ tryToNext: save });
</script>
<style lang="scss" scoped>
.container {
	width: 60%;
}
.header-row,
.row-group {
	display: flex;
	flex-direction: row;
	align-items: flex-start;
	justify-content: center; // 让三列整体居中
}
.col {
	display: flex;
	flex-direction: column;

	// align-items: center;
	width: 200px;
}
.col-field {
	align-items: flex-start;
	font-weight: bold;
	color: #666666;
}
.field-cell {
	height: 32px;
	margin: 4px 0;
	font-size: 14px;
	line-height: 32px;
}
.cell {
	width: 100%;
	margin: 4px 0;
	word-break: break-all;
	white-space: normal;
}
.cell :deep(label) {
	width: 100%;
}
.cell :deep(label .el-checkbox__label) {
	width: 100%;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}
.person-name {
	width: 100px;
	height: 32px;
	margin: 4px 0;
	font-size: 16px;
	font-weight: bold;
	line-height: 32px;
	color: #333333;
}
</style>
