<!-- 导入考勤表 -->
<template>
	<MetaForm
		mode="add"
		ref="form"
		:formConfig="formConfig"
		:styleConfig="{ mode: 'page', labelPosition: 'left', labelWidth: '128px' }"
		:rule-form="ruleForm"
	>
		<template #button></template>
	</MetaForm>
</template>
<script lang="ts" setup>
import { reactive, ref, onMounted, inject, watch, Ref } from "vue";
import MetaForm from "@/meta-components/MetaForm/index.vue";
import { FormConfigOptions, FormItemOptions } from "@/meta-components/MetaForm/interface";
import { UploadService } from "@/api/modules/upload";
import { format } from "date-fns";
import { parseExcelFile } from "../../utils";
import { useHoliday } from "../hooks/useHoliday";
import { ElMessage } from "element-plus";
import { SalaryOptionService } from "@/api/modules/salary";
import { AttendanceData } from "../constants";

const form = ref();
const formData = inject("formData")! as Ref<{ [key: string]: any }>;

// 更新每月休息日到formData
useHoliday(form);
let ruleForm = reactive({
	month: [{ required: true, message: "请选择发薪月份" }],
	attendance: [{ required: true, message: "请上传考勤表" }]
});
// 禁用未来的月份
const disabledDate = (date: Date) => {
	return format(date, "yyyy-MM") > format(new Date(), "yyyy-MM");
};

const formContent: Array<FormItemOptions> = [
	{
		label: "工资单基础信息",
		type: "title",
		name: ""
	},
	{
		label: "发薪月份",
		type: "month",
		placeholder: "请选择发薪月份",
		hint: "若重复创建月份，会覆盖之前的数据",
		name: "month",
		span: 7,
		disabledDate
	},
	{
		label: "考勤表",
		type: "fileUpload",
		placeholder: "请上传考勤表",
		name: "attendance",
		span: 18,
		api: UploadService.ossApi,
		hint: "上传需要导入的Excel文件，Excel 工资表中的「姓名」需与组织架构中的人员姓名匹配，不匹配的人员将按照临时工进行计算。仅支持xls，xlsx。",
		otherAttr: { showUploadPlaceholder: false, returnFile: true },
		imageOptions: {
			mode: "multiple",
			pathName: "salary-attendance",
			limitNum: 5,
			accept: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel"
		}
	},
	{
		label: "网科部考勤表",
		type: "fileUpload",
		placeholder: "请上传考勤表",
		name: "attendanceInternet",
		span: 18,
		api: UploadService.ossApi,
		hint: "上传需要导入的Excel文件，Excel 工资表中的「姓名」需与组织架构中的人员姓名匹配，不匹配的人员将按照临时工进行计算。仅支持xls，xlsx。",
		otherAttr: { showUploadPlaceholder: false, returnFile: true },
		imageOptions: {
			mode: "single",
			pathName: "salary-attendance",
			limitNum: 1,
			accept: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel"
		}
	},
	{
		label: "临时工考勤加班表",
		type: "fileUpload",
		placeholder: "请上传临时工考勤加班表",
		name: "attendanceTemp",
		span: 18,
		api: UploadService.ossApi,
		hint: "上传需要导入的Excel文件，Excel 工资表中的「姓名」需与组织架构中的人员姓名匹配。仅支持xls，xlsx。",
		otherAttr: { showUploadPlaceholder: false, returnFile: true },
		imageOptions: {
			mode: "single",
			pathName: "salary-attendance",
			limitNum: 1,
			accept: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel"
		}
	},
	{
		label: "加班时长表",
		type: "fileUpload",
		placeholder: "请上传考勤表",
		name: "overtimeFile",
		span: 18,
		api: UploadService.ossApi,
		hint: "上传需要导入的Excel文件，Excel 工资表中的「姓名」需与组织架构中的人员姓名匹配，不匹配的人员将无法录入。仅支持xls，xlsx。",
		otherAttr: { showUploadPlaceholder: false, returnFile: true },
		imageOptions: {
			mode: "single",
			pathName: "salary-attendance",
			limitNum: 1,
			accept: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel"
		}
	},
	{
		label: "出海补贴表",
		type: "fileUpload",
		placeholder: "请上传考勤表",
		name: "seaSubsidyFile",
		span: 18,
		api: UploadService.ossApi,
		hint: "上传需要导入的Excel文件，Excel 工资表中的「姓名」需与组织架构中的人员姓名匹配，不匹配的人员将无法录入。仅支持xls，xlsx。",
		otherAttr: { showUploadPlaceholder: false, returnFile: true },
		imageOptions: {
			mode: "single",
			pathName: "salary-attendance",
			limitNum: 1,
			accept: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel"
		}
	},
	{
		label: "本月参数信息",
		type: "title",
		name: ""
	},
	{
		label: "本月法定假日（包含周末）",
		type: "input",
		name: "restDayWeekend",
		placeholder: "请输入",
		span: 6,
		otherAttr: {
			inputOnlyNumber: true,
			inputOnlyNonnegativeNumber: true,
			append: "天",
			showWordLimit: false
		}
	},
	{
		label: "工人可休假天数",
		type: "input",
		name: "restDay",
		placeholder: "请输入",
		span: 6,
		otherAttr: {
			inputOnlyNumber: true,
			inputOnlyNonnegativeNumber: true,
			append: "天",
			showWordLimit: false
		}
	}
];
/** 需要保存数字类型的字段 */
const Number_Fields = ["restDayWeekend", "restDay"];
const formConfig: FormConfigOptions = {
	key: "add-salary",
	items: formContent,
	// editApi: CameraService.editCamera,
	successCallBack: handleSuccess,
	isSaveDraft: false
};
const getOptionData = async () => {
	// 获取基础配置
	const res = await SalaryOptionService.getDetail();
	if (res.data) {
		formData.value.restDay = parseInt(res.data.restDayWorkMan || "");
	}
};
const doValidate = async () => {
	let targetData: { [key: string]: any } = form.value.formData;
	for (let key in targetData) {
		formData.value[key] = Number_Fields.includes(key) ? Number(targetData[key]) : targetData[key];
	}
	return await form.value.doValidate();
};
defineExpose({
	tryToNext: doValidate
});
const emits = defineEmits(["onSuccess"]);
function handleSuccess() {
	emits("onSuccess");
}
onMounted(async () => {
	await getOptionData();
	form.value.iniForm("add", {}, {}, formData.value);
});

const fileFields = ["attendance", "attendanceInternet", "overtimeFile", "seaSubsidyFile", "attendanceTemp"];

type ExcelData = Array<Array<string | number>>;
/** 保存工人船员考勤数据 */
const saveWorkerAttendance = (fileData: ExcelData) => {
	if (fileData.length === 0) {
		return;
	}
	let nameIndex = -1,
		indexIndex = -1,
		remarkIndex = -1; // 序号和姓名的位置

	const userDayArr: AttendanceData[] = []; // 存储用户考勤天数
	//遍历excel二维数组
	let shipName = "";
	fileData.forEach((oneCol: (string | number)[]) => {
		if (oneCol[0] && oneCol[0].toString().includes("：")) {
			if (oneCol[0].toString().includes("船舶：")) {
				shipName = oneCol[0].toString().split("：")[1].trimStart().split(" ")[0];
			} else {
				shipName = "";
			}
		}
		const currentNameIndex = oneCol.findIndex((item: string | number) => item === "姓名");
		const currentIndexIndex = oneCol.findIndex((item: string | number) => item === "序号");
		const currentRemarkIndex = oneCol.findIndex((item: string | number) => item === "备注");
		if (currentNameIndex === -1 && Number.isInteger(oneCol[indexIndex])) {
			// 读取用户列
			// 合计工作天数
			let workDay = 0;
			// 全天工作天数
			let wholeDay = 0;
			// 病假天数
			let illDay = 0;
			// 调休天数
			let compensatoryDay = 0;
			// 不消耗带薪休假天数的请假天数
			let basicSalaryDay = 0;
			// 首尾半天假日期具体
			let firstHalfDay: number = 0;
			let lastHalfDay: number = 0;
			// 计算上班天数
			oneCol.forEach((item: string | number | null, index: number) => {
				if (item === null) {
					return;
				}
				if (item === "√") {
					workDay++;
					wholeDay++;
				} else if (["☆", "★"].includes(item.toString())) {
					workDay += 0.5;
					if (!firstHalfDay) {
						firstHalfDay = index - 1;
					}
					lastHalfDay = index - 1;
				} else if (item === "●") {
					illDay++;
					basicSalaryDay++;
				} else if (item === "▲") {
					compensatoryDay++;
				} else if (item === "△") {
					compensatoryDay++;
					basicSalaryDay++;
				}
			});
			oneCol[nameIndex] &&
				userDayArr.push({
					name: oneCol[nameIndex].toString().replaceAll(" ", ""), // 姓名
					workDay, // 合计上班
					wholeDay, // 全体工作天数
					basicSalaryDay, // 不消耗带薪休假天数的请假天数
					illDay, // 病假天数
					remark: oneCol[remarkIndex]?.toString(), // 备注
					shipName, // 所属船舶
					firstHalfDay, // 首尾半天假日期具体
					lastHalfDay,
					compensatoryDay // 调休天数
				});
		} else if (currentNameIndex !== -1) {
			// 获取姓名的位置
			nameIndex = currentNameIndex;
			indexIndex = currentIndexIndex !== -1 ? currentIndexIndex : indexIndex;
			remarkIndex = currentRemarkIndex !== -1 ? currentRemarkIndex : remarkIndex;
		}
	});
	// 存储用户考勤天数
	form.value.formData.workerAttendance = userDayArr;
};
/** 转换网科部考勤表 */
const saveInterNetAttendance = (fileData: ExcelData) => {
	const userDayArr: AttendanceData[] = []; // 存储用户考勤天数
	let nameIndex = -1; //人员姓名的位置
	let dayIndex = -1; //出勤天数
	let illDayIndex = -1; // 病假天数
	let annualLeaveIndex = -1; //年假天数
	fileData.forEach((column: Array<string | number>) => {
		const currentNameIndex = column.findIndex((item: string | number) => item === "姓名" || item === "人员");
		if (currentNameIndex !== -1) {
			// 获取姓名的位置
			nameIndex = currentNameIndex;
			dayIndex = column.findIndex((item: string | number) => item === "工作日出勤天数");
			illDayIndex = column.findIndex((item: string | number) => item.toString().includes("病假"));
			annualLeaveIndex = column.findIndex((item: string | number) => item.toString().includes("年假"));
			return;
		}
		if (column[nameIndex]) {
			userDayArr.push({
				name: column[nameIndex].toString().replaceAll(" ", ""), // 姓名
				workDay: Number(column[dayIndex] ?? 0), // 合计上班
				annualLeaveDay: Number(column[annualLeaveIndex] ?? 0), // 年假天数
				illDay: Number(column[illDayIndex] ?? 0), // 病假天数
				internet: true //属于网科部
			});
		}
	});
	form.value.formData.internetAttendance = userDayArr;
};
interface OverTime {
	name: string;
	overtime: number;
}
/** 转换加班时长表 */
const saveOvertime = (fileData: ExcelData) => {
	const userOverTimeArr: OverTime[] = []; // 存储用户加班时长
	let nameIndex = -1; //人员姓名的位置
	let currentUser = "";
	fileData.forEach((column: (string | number)[]) => {
		const currentNameIndex = column.findIndex((item: string | number) => item === "姓名" || item === "人员");
		if (currentNameIndex !== -1) {
			// 获取姓名的位置
			nameIndex = currentNameIndex;
			return;
		}
		if (column[nameIndex] === "合计") {
			const overTime = parseFloat(column[column.length - 1].toString());
			userOverTimeArr.push({
				name: currentUser,
				overtime: !Number.isNaN(overTime) ? overTime : 0
			});
		} else if (column[nameIndex]) {
			currentUser = column[nameIndex].toString().replaceAll(" ", "");
		}
	});

	form.value.formData.overtime = userOverTimeArr;
};
/** 转换出海补贴表 */
const saveSeaSubsidy = (fileData: ExcelData) => {
	const seaSubsidyArr: { name: string; seaDays: number; workDays: number }[] = []; // 存储用户加班时长

	let nameIndex = -1; //人员姓名的位置
	fileData.forEach((column: (string | number)[]) => {
		const currentNameIndex = column.findIndex((item: string | number) => item === "姓名" || item === "人员");
		if (currentNameIndex !== -1) {
			// 获取姓名的位置
			nameIndex = currentNameIndex;
			return;
		}
		const name = column[nameIndex];
		if (name && name !== "") {
			//出海补贴天数
			const seaDays = column.filter((i: string | number) => i === "√").length;
			//作业负责天数
			const workDays = column.filter((i: string | number) => i === "☆").length;
			if (seaDays || workDays) {
				seaSubsidyArr.push({
					name: column[nameIndex].toString().replaceAll(" ", ""),
					//出海补贴天数
					seaDays,
					//作业负责天数
					workDays
				});
			}
		}
	});
	form.value.formData.seaSubsidy = seaSubsidyArr;
};
/** 转换临时工考勤表 */
const saveAttendanceTemp = (fileDataArray: ExcelData[]) => {
	if (fileDataArray.length > 0 && fileDataArray.length !== 2) {
		ElMessage.error("请确保上传了包含临时工考勤和加班信息的文件");
	}
	const [attendSheet, overTimeSheet] = fileDataArray;
	if (!attendSheet || !overTimeSheet) {
		return;
	}
	const tempAttendanceArr: AttendanceData[] = []; // 存储临时工考勤
	const tempOverTimeArr: OverTime[] = []; // 存储临时工加班时长

	let nameIndex = -1; //人员姓名的位置
	let overTimeIndex = -1; //合计加班时间位置
	overTimeSheet.forEach((column: (string | number)[]) => {
		const currentNameIndex = column.findIndex((item: string | number) => item === "姓名");
		const currentOverTimeIndex = column.findIndex((item: string | number) => item === "合计");
		if (currentNameIndex !== -1) {
			// 获取姓名的位置
			nameIndex = currentNameIndex;
			overTimeIndex = currentOverTimeIndex;
			return;
		}
		if (nameIndex === -1) {
			return;
		}
		const name = column[nameIndex];
		if (name && name !== "") {
			//加班时长
			tempOverTimeArr.push({
				name: column[nameIndex].toString().replaceAll(" ", ""),
				overtime: Number(column[overTimeIndex] ?? 0)
			});
		}
	});
	nameIndex = -1;
	attendSheet.forEach((column: (string | number)[]) => {
		const currentNameIndex = column.findIndex((item: string | number) => item === "姓名");
		if (currentNameIndex !== -1) {
			// 获取姓名的位置
			nameIndex = currentNameIndex;
			return;
		}
		if (nameIndex === -1) {
			return;
		}
		const name = column[nameIndex];
		if (name && name !== "") {
			//考勤天数
			const workDays = column.filter((i: string | number) => i === "√").length;
			const halfWorkDays = column.filter((i: string | number) => i === "☆").length;
			if (workDays || halfWorkDays) {
				tempAttendanceArr.push({
					name: name.toString().replaceAll(" ", ""),
					//工作天数
					workDay: workDays + halfWorkDays * 0.5
				});
			}
		}
	});
	form.value.formData.tempAttendance = tempAttendanceArr;
	form.value.formData.tempOverTime = tempOverTimeArr;
};
// 在文件顶部添加持久化存储相关的方法
const getStoredFileData = (fileName: string) => {
	const key = `salary_file_data_${fileName}`;
	const stored = localStorage.getItem(key);
	return stored ? JSON.parse(stored) : [];
};

const setStoredFileData = (fileName: string, data: any[]) => {
	const key = `salary_file_data_${fileName}`;
	localStorage.setItem(key, JSON.stringify(data));
};

// 修改 fileDataChange 函数中的相关部分
const fileDataChange = async (fileName: string) => {
	const currentFiles = form.value.formData[fileName];
	if (fileName === "attendance" && currentFiles.length === 0) {
		return;
	}

	// 获取新上传的文件（有file对象的）
	const newFiles = currentFiles
		.filter((item: any) => item.file)
		.map((item: any) => ({
			uid: item.uid,
			name: item.name,
			file: item.file
		}));

	// 从 localStorage 中获取已存在的数据
	const storedData = getStoredFileData(fileName);
	const existingFiles = currentFiles
		.filter((item: any) => !item.file)
		.map((item: any) => {
			// 使用文件名匹配而不是 uid
			const savedData = storedData.find((f: { name: string }) => f.name === item.name);
			return {
				...item,
				jsonData: savedData?.jsonData
			};
		});

	// 处理新文件
	const newFileDataArray = await Promise.all(
		newFiles.map(async (file: any) => {
			const jsonData = fileName === "attendanceTemp" ? await parseExcelFile(file.file, 2) : await parseExcelFile(file.file);
			return {
				uid: file.uid,
				name: file.name,
				jsonData
			};
		})
	);

	const allFiles = [...existingFiles, ...newFileDataArray];

	// 更新存储时只保存必要的信息
	const dataToStore = allFiles.map(file => ({
		name: file.name,
		jsonData: file.jsonData
	}));

	setStoredFileData(fileName, dataToStore);

	// 更新表单数据
	form.value.formData[fileName] = allFiles;
	// 合并所有Excel数据
	const mergedFileData = allFiles.reduce((acc: any[], curr: any) => {
		return acc.concat(curr.jsonData || []);
	}, []);
	// 根据文件类型处理数据
	switch (fileName) {
		case "attendance":
			saveWorkerAttendance(mergedFileData);
			break;
		case "attendanceInternet":
			// 存储网科部考勤表
			saveInterNetAttendance(mergedFileData);
			break;
		case "overtimeFile":
			// 存储加班时长表
			saveOvertime(mergedFileData);
			break;
		case "seaSubsidyFile":
			// 存储出海补贴表
			saveSeaSubsidy(mergedFileData);
			break;
		case "attendanceTemp":
			// 存储临时工考勤表
			saveAttendanceTemp(mergedFileData);
			break;
	}
};

/** 文件上传时 将excel解析成数据存储 */
watch(
	() => fileFields.map(field => form.value?.formData[field]?.map((i: any) => i.uid).join(",")),
	(newUids, oldUids) => {
		// 比较uid变化，确定是哪个文件发生了变化
		fileFields.forEach((field, index) => {
			if (newUids[index] !== oldUids[index]) {
				fileDataChange(field);
			}
		});
	},
	{ deep: true }
);
</script>
<style lang="scss" scoped></style>
