<!-- 系统初算 -->
<template>
	<div class="container" v-loading="loading">
		<div class="excel-main">
			<div id="luckysheet" class="excel" />
		</div>
	</div>
</template>
<script lang="ts" setup>
import { inject, ref, onBeforeUnmount, Ref, onMounted } from "vue";
import { format } from "date-fns";
// import { SalaryService } from "@/api/modules/salary";
import { getContentUrl } from "../../utils";
import { ElMessage } from "element-plus";
import { useLuckysheet } from "../hooks/useLuckySheet";
import { getContentByUrl } from "../../utils";
import { SaveType, FormData, header, CellValue } from "../constants";
import { isEqual } from "lodash";

const loading = ref(false);
const formData = inject("formData")! as Ref<FormData>;
const cellDataFromDraft = ref();
/** 查询是否存在草稿 */
const getMonthData = async () => {
	if (formData.value.payroll) {
		const res = await getContentByUrl(formData.value.payroll);
		if (res) {
			cellDataFromDraft.value = JSON.parse(res);
		}
	} else {
		cellDataFromDraft.value = undefined;
	}
};
/** 解析文件数据 构成用户薪资信息、船员使用年休假列表 */
const { celldata } = formData.value;
const { initLuckysheet } = useLuckysheet({
	containerId: "luckysheet",
	title: () => format(new Date(formData.value.month), "yyyy 年 MM 月薪资表"),
	getData: () => celldata,
	data: cellDataFromDraft
});
/**销毁EXCEL对象 */
function sheetDestroy() {
	// @ts-ignore
	luckysheet && luckysheet.destroy();
}
const init = async () => {
	/**查看luckysheet.js是否加载完成 */
	loading.value = true;
	const timer = setInterval(() => {
		// @ts-ignore
		if (luckysheet) {
			loading.value = false;
			clearInterval(timer);
		}
	}, 50);
	await getMonthData();
	/**初始化表格 */
	initLuckysheet();
	//冻结表头
	// @ts-ignore
	luckysheet.setBothFrozen(true, { range: { row_focus: 2, column_focus: 6 } });
	const personPsList = formData.value.personPsList;
	const nameIndex = header.findIndex(item => item.title === "姓名");
	// 获取当前sheet所有数据
	// @ts-ignore
	const currentSheet = luckysheet.getSheetData();
	if (personPsList && Object.keys(personPsList).length) {
		for (let name in personPsList) {
			// 找到当前sheet中该用户的行号
			const row = currentSheet.findIndex((row: CellValue[]) => row[nameIndex]?.v === name);
			if (row === -1) continue; // 没找到该用户，跳过
			const psList = personPsList[name];
			psList.forEach(item => {
				setTimeout(() => {
					// @ts-ignore
					luckysheet.setCellValue(row, item.c, {
						ps: item.ps
					});
				}, 1000);
			});
		}
	}
};

onMounted(async () => {
	init();
});

/** 单元格值是否相等判断 判断计算值与excel里实际值 */
function isCellValueEqual(a: any, b: any) {
	// '' 和 undefined 视为相同
	if ((a === "" || a === undefined) && (b === "" || b === undefined)) return true;
	// 数字字符串和数字相等（如 '110.0' 和 110）
	if (!isNaN(Number(a)) && !isNaN(Number(b))) {
		return Number(a) == Number(b);
	}
	// 其他严格相等
	return a === b;
}

const saveEdit = () => {
	// @ts-ignore
	const currentSheet = luckysheet.getSheetData();
	// 1. 获取原始数据
	const { originMap } = formData.value;
	const nameIndex = header.findIndex(item => item.title === "姓名");
	const personPsList: { [key: string]: any[] } = {}; // 用户批注

	// 2. 对比 currentSheet 保存与人名关联的修改数据记录
	const diffResult: Record<string, { field: string; oldValue: any; newValue: any }[]> = {};
	for (let i = 3; i < currentSheet.length; i++) {
		const row = currentSheet[i];
		const name = row[nameIndex]?.v;
		if (!name) continue;
		// 存储属于用户的批注
		const psList = row.map((cell: any, idx: number) => ({ ...cell, r: i, c: idx })).filter((cell: any) => cell?.ps);
		if (psList.length) personPsList[name] = psList;
		// 判断单元格相较于原始数据是否更改
		const originRow = originMap[name];
		if (!originRow) {
			// 新增人员
			diffResult[name] = header
				.map((item, idx) => ({
					field: item.title,
					oldValue: undefined,
					newValue: row[idx]?.v
				}))
				.filter(item => item.newValue !== undefined && item.field !== "姓名");
			continue;
		}
		if (isEqual(originRow, row)) continue;
		for (let j = 0; j < header.length; j++) {
			const field = header[j].title;
			const col = header[j].col;
			if (field === "姓名") continue;
			const oldValue = originRow[col]?.v;
			const newValue = row[col]?.v;
			const oldFormula = originRow[col]?.f;
			// const newFormula = row[col]?.f;
			// 值不相等且不存在公式 为修改记录
			if (!isCellValueEqual(oldValue, newValue) && !oldFormula) {
				if (!diffResult[name]) diffResult[name] = [];
				diffResult[name].push({ field, oldValue, newValue });
			}
		}
	}
	formData.value.personPsList = personPsList;
	formData.value.diffResult = diffResult;
	return true;
};
onBeforeUnmount(() => {
	sheetDestroy();
});

/**保存 */
async function save(isSaveDraft: SaveType = SaveType.SUBMIT) {
	// @ts-ignore
	// 此Api就是自动退出编辑模式的操作，主要是为了触发自动保存单元格。
	luckysheet.exitEditMode();
	// @ts-ignore
	const fileName = luckysheet.getWorkbookName();
	if (!fileName) {
		ElMessage.warning("请输入文件名");
		return false;
	}
	loading.value = true;
	// @ts-ignore
	const content = JSON.stringify(luckysheet.getLuckysheetfile());
	const url = await getContentUrl(content, fileName);
	// 保存初算文件
	formData.value.payroll = url;
	formData.value.payrollName = fileName;
	loading.value = false;
	if (isSaveDraft === SaveType.DRAFT) {
		ElMessage.success("保存草稿成功");
	}
	return true;
}
defineExpose({
	tryToNext: save,
	saveEdit
});
</script>
<style lang="scss" scoped>
.container {
	// calc(100% - 192px - 65px)
	height: 100%;
	.excel-header {
		.header {
			display: flex;
			justify-content: space-between;
			margin: -10px -12px 0;
			line-height: 54px;
			.title {
				display: flex;
				align-items: center;
				margin-left: 20px;
				font-size: 16px;
				font-weight: bold;
				line-height: 54px;
				color: $color-text-primary;
			}
			.back-btn {
				margin: 0 12px 0 0;
				cursor: pointer;
			}
		}
	}
	.excel-main {
		position: relative;
		z-index: 1000 !important;

		// calc(100% - 192px - 65px)
		// height: calc(100% - 74px);
		height: 100%;
		background-color: #ffffff;

		// padding: 24px;
		border-radius: 4px;
		.excel {
			width: 100%;
			height: 100%;
			padding: 0;
			margin: 0;
		}
	}
}
</style>
