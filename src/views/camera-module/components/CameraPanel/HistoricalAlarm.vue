<!-- 历史预警 -->
<template>
	<div class="historical-alarm">
		<el-date-picker v-model="date" type="date" placeholder="请选择日期" value-format="YYYY-MM-DD" :clearable="false" />
		<TableLayout
			ref="alarmTable"
			title="智能预警"
			:table-config="tableConfig"
			style="height: calc(100% - 34px); margin-top: 24px"
		>
			<template #type="{ row }">
				<span>{{ RecognitionText[row.type] }}</span>
			</template>
			<template #urls="{ row }">
				<div class="images" v-if="row.urls && row.urls.length > 0">
					<el-image
						v-for="(url, index) in row.urls"
						:key="url"
						:src="url"
						fit="cover"
						style="width: 40px; height: 40px; margin-right: 5px"
						class="img-in-table"
						:lazy="true"
						preview-teleported
						:hide-on-click-modal="true"
						:preview-src-list="row.urls.slice(index).concat(row.urls.slice(0, index))"
					/>
				</div>
				<span v-else>-</span>
			</template>
			<template #alertContent="{ row }">
				<div v-if="row.alertContent && row.alertContent.length > 0">
					<el-tag v-for="item in row.alertContent" :key="item" type="success" effect="plain">
						{{ item }}
					</el-tag>
				</div>
				<div v-else>-</div>
			</template>
		</TableLayout>
	</div>
</template>
<script setup lang="ts">
import { ref, reactive, watch } from "vue";
import { format } from "date-fns";
import TableLayout from "@/meta-components/MetaTable/index.vue";
import { ColumnProps, TableConfig } from "@/meta-components/MetaTable/interface";
import { CameraService } from "@/api/modules/camera";
import { Camera } from "@/api/interface/camera.model";
const { RecognitionText } = Camera;

const props = defineProps({
	uuid: { type: String, default: "" }
});

let alarmTable = ref();
let date = ref(format(new Date(), "yyyy-MM-dd"));

let columnsConfig: Array<ColumnProps> = [
	{
		label: "预警类别",
		slotName: "type",
		width: "100px"
	},
	{
		label: "预警图像",
		slotName: "urls",
		minWidth: "200px"
	},
	{
		label: "预警时间",
		prop: "createdTime",
		width: "180px",
		showType: "text"
	},
	{
		label: "预警推送",
		slotName: "alertContent",
		minWidth: "300px"
	}
];

let tableConfig: TableConfig = reactive({
	key: "camera-alarm-mgr",
	columns: columnsConfig,
	requestApi: params => {
		return CameraService.getAlarmList({
			...params,
			cameraUuid: props.uuid,
			warningDate: format(new Date(date.value), "yyyy-MM-dd")
		});
	},
	selectType: "none",
	mode: "mini",
	selectId: "id",
	pagination: true,
	staticParam: {},
	canChangeHead: false,
	pageSize: 20
});
watch(
	[date, () => props.uuid],
	() => {
		alarmTable.value && alarmTable.value.search();
	},
	{
		deep: true
	}
);
</script>
<style scoped lang="scss">
.historical-alarm {
	height: 100%;
}
</style>
