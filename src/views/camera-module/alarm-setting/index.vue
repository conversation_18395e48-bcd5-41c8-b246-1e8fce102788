<!-- 预警人员设置页面 -->
<template>
	<Layout title="预警人员设置" pageSize="100%">
		<template #body>
			<div class="msa-container">
				<el-result v-show="isEmptyForm" style="margin-top: 120px">
					<template #title>当前企业暂无可用摄像头</template>
				</el-result>
				<MetaForm
					v-show="!isEmptyForm"
					mode="edit"
					ref="form"
					:formConfig="formConfig"
					:styleConfig="{ mode: 'page', labelPosition: 'right', labelWidth: '100px' }"
				>
					<template #button></template>
				</MetaForm>
			</div>
		</template>
		<template #bottom-button>
			<el-button type="primary" @click="form?.submit">保存</el-button>
			<el-button @click="close">取消</el-button>
		</template>
	</Layout>
</template>
<script setup lang="ts">
import Layout from "@/meta-components/MetaLayout/SinglePage.vue";
import { ref, reactive, onMounted, computed } from "vue";
import MetaForm from "@/meta-components/MetaForm/index.vue";
import { FormConfigOptions, FormItemOptions } from "@/meta-components/MetaForm/interface";
import { useRouter } from "vue-router";
import { CameraService } from "@/api/modules/camera";
import { Camera } from "@/api/interface/camera.model";
import { MemberService } from "@/api/modules/member";
import { Member } from "@/api/interface/member";
import { YN } from "@/enums/global-enums";

const router = useRouter();
const formContent: Array<FormItemOptions> = reactive([]);
const isEmptyForm = computed(() => {
	return formContent.length === 0;
});
const dataCallBack = (data: Camera.ShipCameraWarningUserListResp) => {
	const { list } = data;
	if (list && list.length > 0) {
		list.forEach(item => {
			const { cameraId, corpUserIds } = item;
			const key = `alarmPerson_${cameraId}`;
			(data as any)[key] = corpUserIds;
		});
	}
	return data;
};
const formConfig = reactive<FormConfigOptions>({
	key: "alarm-setting",
	items: formContent,
	editApi: CameraService.updatePerson,
	// successCallBack: handleSuccess,
	beforeSubmit: beforeSubmit,
	detailApi: CameraService.getPersonList,
	dataCallBack,
	detailKey: "id",
	closeFunction: close,
	closeFormAt: "both"
});

const userList: Member.SimpleDetail[] = reactive([]);
const form = ref();
function beforeSubmit(data: any) {
	const list: Camera.WarningUserInfo[] = [];
	Object.keys(data).forEach(key => {
		if (key.startsWith("alarmPerson_")) {
			const cameraId = key.split("_")[1];
			const alarmPerson = data[key];
			const item: Camera.WarningUserInfo = {
				cameraId: Number(cameraId),
				corpUserIds: alarmPerson
			};
			list.push(item);
		}
	});
	return { list };
}
function close() {
	router.back();
}
onMounted(() => {
	MemberService.getSimpleList({ offset: -1, length: -1 }).then(res => {
		if (res.code === 0 && res.data && res.data.list) {
			userList.splice(0, userList.length, ...res.data.list);
			// 获取摄像头列表
			CameraService.getCameraList({
				shipGroupState: YN.Yes,
				isOpenAlgRecognition: YN.Yes
			}).then(res => {
				if (res.code === 0) {
					if (res.data && res.data.list && res.data.list.length > 0) {
						const cameraList: FormItemOptions[] = res.data.list.reduce((pre: FormItemOptions[], shipItem) => {
							if (shipItem.cameraList.length > 0) {
								shipItem.cameraList.forEach((camera: Camera.CameraListObj) => {
									pre.push(
										{
											label: "",
											type: "text",
											name: `${shipItem.shipName ? shipItem.shipName + "-" : ""}${camera.name}`,
											span: 13
										},
										{
											label: "短信通知人员",
											type: "singleSelect",
											placeholder: "请输入姓名或手机号搜索",
											name: `alarmPerson_${camera.id}`,
											api: params => {
												return new Promise(res => {
													res({
														code: 0,
														data: {
															list: params.field
																? userList.filter(
																		item => item.name.includes(params.field) || item.contactMobile.includes(params.field)
																  )
																: userList
														}
													});
												});
											},
											otherAttr: {
												remote: true,
												remoteLabelKey: "userName",
												multiple: true,
												labelKey: "name",
												valueKey: "id",
												max: 50,
												subLabelKey: "contactMobile",
												appendOptionToFormDataWithKey: [
													{ optionKey: "contactMobile", formKey: "phone" },
													{ optionKey: "name", formKey: "userName" }
												]
											},
											span: 13
										}
									);
								});
							}
							return pre;
						}, []);
						formContent.splice(0, 0, ...cameraList);
						if (form.value) {
							form.value.iniForm("edit", {}, {});
						}
					}
				}
			});
		}
	});
});
</script>
<style scoped lang="scss">
:deep(.sub-title) {
	font-weight: normal !important;
	color: #606266;
}
</style>
