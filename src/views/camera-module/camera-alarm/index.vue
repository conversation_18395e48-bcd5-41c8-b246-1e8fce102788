<!-- 智能预警界面 -->
<template>
	<div class="page-container">
		<TableLayout ref="alarmTable" title="智能预警" :filter-config="filterConfig" :table-config="tableConfig">
			<template #button>
				<el-button v-auth="'预警人员新增'" type="primary" @click="openSetting">预警人员设置</el-button>
			</template>
			<template #type="{ row }">
				<span>{{ RecognitionText[row.type] }}</span>
			</template>
			<template #urls="{ row }">
				<div class="images" v-if="row.urls && row.urls.length > 0">
					<el-image
						v-for="(url, index) in row.urls"
						:key="url"
						:src="url"
						fit="cover"
						style="width: 40px; height: 40px; margin-right: 5px"
						class="img-in-table"
						:lazy="true"
						preview-teleported
						:hide-on-click-modal="true"
						:preview-src-list="row.urls.slice(index).concat(row.urls.slice(0, index))"
					/>
				</div>
				<span v-else>-</span>
			</template>
			<template #alertContent="{ row }">
				<div v-if="row.alertContent && row.alertContent.length > 0">
					<el-tag v-for="item in row.alertContent" :key="item" type="success" effect="plain" style="margin-right: 5px">
						{{ item }}
					</el-tag>
				</div>
				<div v-else>-</div>
			</template>
		</TableLayout>
	</div>
</template>

<script setup lang="ts" name="CameraAlarm">
import TableLayout from "@/meta-components/MetaTable/index.vue";
import { ref, reactive } from "vue";
import { ColumnProps, TableConfig, FilterConfigOptions } from "@/meta-components/MetaTable/interface";
import { CameraService } from "@/api/modules/camera";
import { Camera } from "@/api/interface/camera.model";
import { useRouter } from "vue-router";
const router = useRouter();
const { RecognitionType, RecognitionText } = Camera;
let alarmTable = ref();

/**船舶保险编辑弹窗的相关字段 */

/**筛选框配置数量 */
let filterConfig: Array<FilterConfigOptions> = reactive([
	{
		name: "cameraName",
		type: "input",
		span: 3,
		placeholder: "摄像头名称"
	},
	{
		name: "createdTimes",
		type: "dateRange",
		span: 6,
		placeholder: "选择预警时间段"
	},
	{
		name: "type",
		type: "singleSelect",
		span: 3,
		placeholder: "筛选预警类别",
		staticOptions: [
			{ label: "疲劳驾驶", value: RecognitionType.FatigueDriving },
			{ label: "人员离岗", value: RecognitionType.PersonnelOffPost },
			{ label: "使用手机", value: RecognitionType.UseMobilePhone },
			{ label: "人员吸烟", value: RecognitionType.PersonnelSmoking }
		]
	}
]);

let columnsConfig: Array<ColumnProps> = [
	{
		label: "预警类别",
		slotName: "type",
		width: "80px"
	},
	{
		label: "摄像头名称",
		prop: "cameraName",
		minWidth: "100px"
	},
	{
		label: "预警图像",
		slotName: "urls",
		minWidth: "180px"
	},
	{
		label: "预警时间",
		prop: "createdTime",
		width: "180px",
		showType: "text"
	},
	{
		label: "预警推送",
		slotName: "alertContent",
		minWidth: "300px"
	}
];

let tableConfig: TableConfig = reactive({
	key: "camera-alarm-mgr",
	columns: columnsConfig,
	requestApi: CameraService.getAlarmList,
	selectType: "none",
	selectId: "id",
	pagination: true,
	staticParam: {},
	canChangeHead: false
});

const openSetting = () => {
	router.push({
		name: "AlarmSetting"
	});
};
</script>

<style scoped></style>
