<!-- 船舶设备展示 -->
<template>
	<div class="ship-detail-container">
		<BorderBox7 style="">
			<div class="detail-outer">
				<div class="detail-inner">
					<!-- <div class="detail-title">满洋安澜 - 多功能施工船</div>
					<Decoration2 style="width: 90%; height: 5px; margin-bottom: 24px"></Decoration2> -->
					<div v-for="(data, index) in detailData" :key="index">
						<div class="detail-item">
							<div class="detail-item-name">{{ data.name }}</div>
							<div class="detail-item-value">
								<div class="detail-item-sub" v-for="item in data.values" :key="item">{{ item }}</div>
								<!-- <div class="detail-item-sub">{{ data.sub }}</div> -->
							</div>
						</div>
						<div v-if="index != detailData.length - 1" class="detail-line"></div>
					</div>
				</div>
			</div>
		</BorderBox7>
	</div>
</template>

<script setup lang="ts">
import { BorderBox7 } from "@kjgl77/datav-vue3";
import { ref } from "vue";
const detailData = ref([
	{
		name: "主机推进设备",
		values: ["G6300ZC74B 367kW·2台"]
	},
	{
		name: "发电设备",
		values: ["SB-HW4-400-6P 400kW·2组", "SB-HW4.D-120 120kW·1组", "SB-HW4.D-700 700kW·1组"]
	},
	{
		name: "油柜",
		values: ["燃油舱：258.2吨", "淡水舱：310.34吨", "浮油回收舱：525.62吨"]
	}
]);
</script>

<style scoped lang="scss">
.ship-detail-container {
	position: absolute;
	bottom: 190px;
	left: 80px;
	z-index: 1000;
	width: 400px;
	height: 236px;
}
.detail-outer {
	width: calc(100% - 20px);
	height: calc(100% - 20px);
	padding: 10px;
	color: white;
}
.detail-inner {
	width: calc(100% - 12px);
	height: 100%;
	padding-left: 12px;
	background-color: rgb(0 0 0 / 30%);
}
.detail-title {
	padding-top: 24px;
	margin-bottom: 12px;
	font-size: 18px;
	font-weight: bold;
	color: #f0f0f0;
}
.detail-item {
	display: flex;
	align-items: center;
	margin-bottom: 12px;
}
.detail-item-name {
	width: 128px;
	margin-left: 4px;
	color: #c7c7c7;
}
.detail-item-value {
	display: flex;
	flex: 1;
	flex-direction: column;
	justify-content: flex-start;
	color: #dfdfdf;
}
.detail-item-sub {
	margin-top: 4px;
	font-size: 14px;
	color: #c6c6c6;
}
.detail-line {
	width: 90%;
	height: 1px;
	margin-top: 4px;
	margin-bottom: 12px;
	background-color: #6c85e5aa;
}
</style>
