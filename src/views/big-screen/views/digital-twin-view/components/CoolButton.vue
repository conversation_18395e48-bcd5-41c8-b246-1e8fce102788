<!--大屏用酷炫按钮-->
<template>
	<div class="info" @click="check">
		<div class="bg-container">
			<img :src="StatisticBg" style="width: 100%; height: 100%" />
		</div>
		<div class="value">
			<div class="value-text" :style="{ color }">{{ title }}</div>
		</div>
		<div class="bottom-decoration"></div>
	</div>
</template>

<script setup lang="ts">
import StatisticBg from "@/views/big-screen/assets/images/statistic-bg.svg";

defineProps({
	title: { type: String },
	color: { type: String, default: "#fff" }
});

function check() {
	console.log("check");
}
</script>

<style scoped lang="scss">
.info {
	position: relative;
	width: 200px;
	height: 60px;
	cursor: pointer;
}
.bg-container {
	position: relative;

	// width: 92px;
	// height: 35px;
	width: 100%;
	height: 100%;
}
.value {
	position: absolute;
	top: 50%;
	left: 50%;
	display: flex;
	align-items: flex-end;
	transform: translate(-50%, -50%);
	.value-text {
		height: 28px;
		font-family: YouSheBiaoTiHei;
		font-size: 32px;
		line-height: 28px;
	}
	.value-unit {
		height: 24px;
		margin-left: 4px;
		font-size: 16px;
		line-height: 24px;
	}
}
.bottom-decoration {
	position: absolute;
	bottom: 1px;
	left: 50%;
	width: 12px;
	height: 2px;
	background: $color-yellow-6;
	transform: translateX(-50%);
	animation: decoration-animation 4s infinite;
}
@keyframes decoration-animation {
	0% {
		width: 12px;
	}
	50% {
		width: 58px;
	}
	100% {
		width: 12px;
	}
}
</style>
