<!-- 船舶信息展示 -->
<template>
	<div class="ship-detail-container">
		<BorderBox13 style="">
			<div class="detail-outer">
				<div class="detail-inner">
					<div class="detail-title">满洋安澜 - 多功能施工船</div>
					<Decoration2 style="width: 90%; height: 5px; margin-bottom: 24px"></Decoration2>
					<div v-for="(data, index) in detailData" :key="index">
						<div class="detail-item">
							<div class="detail-item-name">{{ data.name }}</div>
							<div class="detail-item-value">{{ data.value }}</div>
						</div>
						<div v-if="index != detailData.length - 1" class="detail-line"></div>
					</div>
				</div>
			</div>
		</BorderBox13>
	</div>
</template>

<script setup lang="ts">
import { BorderBox13, Decoration2 } from "@kjgl77/datav-vue3";
import { ref } from "vue";
const detailData = ref([
	{
		name: "船舶类型",
		value: "多功能施工船"
	},
	{
		name: "建造完工时间",
		value: "2024-04-30"
	},
	{
		name: "总长",
		value: "79.8m"
	},
	{
		name: "船宽",
		value: "19.2m"
	},
	{
		name: "型深",
		value: "5.5m"
	},
	{
		name: "总吨",
		value: "2969吨"
	},
	{
		name: "满载吃水",
		value: "3m"
	},
	{
		name: "船体材料",
		value: "钢质"
	},
	{
		name: "满载排水",
		value: "4339吨"
	},
	{
		name: "航速",
		value: "8kn"
	},
	{
		name: "续航力",
		value: "2490n mile"
	},
	{
		name: "船员定员",
		value: "10P"
	},
	{
		name: "工作人员",
		value: "16P"
	},
	{
		name: "自持力",
		value: "15天"
	}
]);
</script>

<style scoped lang="scss">
.ship-detail-container {
	position: absolute;
	top: 16px;
	right: 48px;
	z-index: 1000;
	width: 350px;
	height: 650px;
}
.detail-outer {
	width: calc(100% - 20px);
	height: calc(100% - 20px);
	padding: 10px;
	color: white;
}
.detail-inner {
	width: calc(100% - 12px);
	height: 100%;
	padding-left: 12px;
	background-color: rgb(0 0 0 / 30%);
}
.detail-title {
	padding-top: 24px;
	margin-bottom: 12px;
	font-size: 18px;
	font-weight: bold;
	color: #f0f0f0;
}
.detail-item {
	display: flex;
	align-items: center;
	margin-bottom: 8px;
}
.detail-item-name {
	width: 128px;
	margin-left: 4px;
	color: #c7c7c7;
}
.detail-item-value {
	flex: 1;
	color: #dfdfdf;
}
.detail-line {
	width: 90%;
	height: 1px;
	margin-top: 4px;
	margin-bottom: 8px;
	background-color: #6c85e588;
}
</style>
