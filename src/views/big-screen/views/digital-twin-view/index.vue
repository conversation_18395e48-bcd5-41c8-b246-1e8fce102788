<!-- 数字孪生页面 -->
<template>
	<div style="position: relative; height: 100%">
		<ShipDetail />
		<ShipDevice />

		<div id="parent-container" style="position: relative; width: 100%; height: 100%">
			<div class="opr-area">
				<div style="height: 48px"></div>
				<CoolButton title="切 换" @click="doSwitch" />
				<div style="height: 24px"></div>
				<CoolButton title="吊 臂" @click="doArm" />
				<div style="height: 24px"></div>
				<CoolButton title="月 池" @click="doMoon" />
				<div style="height: 24px"></div>
				<CoolButton title="无人艇" @click="doBoat" />
			</div>
			<!-- 船各个行为对应文字接受 -->
			<div class="act-content">{{ actContent }}</div>
			<div class="ship-title" @click="refreshIframe"><div>满 洋 安 澜</div></div>
			<div class="ratio-container">
				<iframe ref="iframeRef" :src="internalUrl" class="iframe-content" @load="onIframeLoad"></iframe>
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
import CoolButton from "./components/CoolButton.vue";
import { ref, onMounted, onBeforeUnmount, computed } from "vue";
import ShipDetail from "./components/ShipDetail.vue";
import ShipDevice from "./components/ShipDevice.vue";
// import VScaleScreen from "v-scale-screen";

const actType = ref<"switch" | "arm" | "moon" | "boat">("switch");
const actContent = computed(() => {
	switch (actType.value) {
		case "switch":
			return "满洋安澜号多功能施工船，一方面可作为海上应急抢险的综合性功能平台，另一方面预置了无人艇布放与作业支持功能。";
		case "arm":
			return "起重功能：船舶中部设置一台德国利勃海尔公司制造的100吨全回转起重机，作业半径10-54米，最大起重能力100吨。";
		case "moon":
			return "潜水月池及定位桩功能：一方面潜水员可以从月池入水，增加了潜水安全性和潜水作业时间，另一方面，月池可作为定位桩孔，由起重船将定位桩插入桩孔，实现船舶定位。";
		case "boat":
			return "无人艇作业支持功能：将无人艇布置在抛艇装置上面，通过卷扬机实现无人艇的释放和回收，给无人艇作业提供支持平台。";
		default:
			return "";
	}
});

const iframeRef = ref();
const internalUrl = "https://meta.yeedo-tech.com/digital-twin"; // 内部地址同源地址

const refreshIframe = () => {
	iframeRef.value.contentWindow.location.href = "https://meta.yeedo-tech.com/digital-twin?time=" + new Date().getTime();
};

// 同源下可直接操作DOM
const onIframeLoad = () => {
	const iframeDoc = iframeRef.value.contentDocument;
	// 修改嵌入页面样式（如去除头部/页脚）
	iframeDoc.querySelector("header")?.remove();
	iframeDoc.body.style.margin = "0";
	initResize();
};

// 动态计算容器尺寸
const calculateSize = () => {
	const parent = document.querySelector("#parent-container")!;
	const maxWidth = parent.clientWidth;
	const maxHeight = parent.clientHeight;

	// 根据父容器尺寸计算16:9的最大适配值
	const widthByHeight = (maxHeight * 16) / 9;
	const heightByWidth = (maxWidth * 9) / 16;

	return {
		width: Math.min(maxWidth, widthByHeight),
		height: Math.min(maxHeight, heightByWidth)
	};
};

// 响应式调整
const initResize = () => {
	const { width, height } = calculateSize();
	iframeRef.value.style.width = `${width}px`;
	iframeRef.value.style.height = `${height}px`;
};

// 窗口变化监听
const resizeObserver = new ResizeObserver(initResize);
onMounted(() => {
	resizeObserver.observe(document.querySelector("#parent-container")!);
	window.addEventListener("resize", initResize);
});

onBeforeUnmount(() => {
	resizeObserver.disconnect();
	window.removeEventListener("resize", initResize);
});

function doMoon() {
	actType.value = "moon";
	try {
		iframeRef.value.contentWindow.postMessage({ action: "moon" }, "https://meta.yeedo-tech.com/");
	} catch (error) {
		console.error("方法调用失败:", error);
	}
}
function doBoat() {
	actType.value = "boat";
	iframeRef.value.contentWindow.postMessage({ action: "boat" }, "https://meta.yeedo-tech.com/");
}
function doArm() {
	actType.value = "arm";
	iframeRef.value.contentWindow.postMessage({ action: "arm" }, "https://meta.yeedo-tech.com/");
}
function doSwitch() {
	actType.value = "switch";
	iframeRef.value.contentWindow.postMessage({ action: "switch" }, "https://meta.yeedo-tech.com/");
}
</script>

<style scoped>
.ship-title {
	position: absolute;
	top: 24px;
	z-index: 10000;
	display: flex;
	justify-content: center;
	width: 100%;
	font-family: YouSheBiaoTiHei;
	font-size: 48px;
	font-weight: normal;
	line-height: 28px;
	color: #d1e8f4;
}

/* 16:9比例容器 */
.ratio-container {
	position: relative;
	width: 100%;
	height: 0;
	padding-top: 56.25%; /* 9/16=56.25% */
}

/* iframe全屏填充 */
.iframe-content {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	border: none;
}
.opr-area {
	position: absolute;
	top: 0;
	left: 32px;
	z-index: 10000;
	display: flex;
	flex-flow: wrap;
	align-items: center;
	justify-content: center;
	width: 440px;
	height: 160px;
	margin-top: 48px;
}
.act-content {
	position: absolute;
	top: 250px;
	left: 80px;
	z-index: 1000;
	width: 360px;
	line-height: 28px;
	color: #f0f0f0;
}
</style>
