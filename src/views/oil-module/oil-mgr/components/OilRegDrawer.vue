<!-- 油料登记弹窗 -->
<template>
	<el-drawer
		ref="dialog"
		:title="mode === 'add' ? '油料登记' : '记录编辑'"
		v-model="isShow"
		size="40%"
		destroy-on-close
		@open="iniData"
		:before-close="confirmClose"
		@close="close"
	>
		<MetaForm
			:mode="mode"
			ref="form"
			:formConfig="formConfig"
			:styleConfig="{ mode: 'drawer', labelWidth: '100px' }"
			:rule-form="ruleForm"
		>
		</MetaForm>
	</el-drawer>
</template>

<script lang="ts" setup>
import MetaForm from "@/meta-components/MetaForm/index.vue";
import { FormConfigOptions, FormItemOptions } from "@/meta-components/MetaForm/interface";
import { PropType, reactive, ref } from "vue";
import { RegTypeEnum, RegTypeTextEnum } from "../index";
import { ShipOptions } from "@/api/interface/ship";
import _ from "lodash";
import { OilService } from "@/api/modules/oil";
import { useDialog } from "@/meta-components/MetaHooks/useDialog";

const props = defineProps({
	shipList: {
		type: Object as PropType<ShipOptions.DetailInSearchList[]>,
		default() {
			return [];
		}
	}
});

let emits = defineEmits(["onSuccess"]);
let isShow = ref(false);
let mode = ref<"add" | "edit">("add");
let regType = ref<number>(RegTypeEnum.ADD);
let provideShipId = ref<number>();
let receiveShipId = ref<number>();
let editData = ref<{ [key: string]: any }>({});
let defaultShipId = -1;
function open(modeParam: "add" | "edit", otherParams: any) {
	isShow.value = true;
	mode.value = modeParam;

	if (otherParams.defaultShipId) {
		defaultShipId = otherParams.defaultShipId;
		provideShipId.value = defaultShipId;
	}
	modeParam === "edit" && (editData.value = otherParams);
}
function close() {
	isShow.value = false;
	defaultShipId = -1;
	oilPrice.value = "";
	cacheTotalPrice.value = "";
	provideShipId.value = -1;
	receiveShipId.value = -1;
}

const formContent: Array<FormItemOptions> = [
	{
		label: "登记类型",
		type: "radio",
		name: "type",
		staticOptions: [
			{
				label: "加油",
				value: 1
			},
			{
				label: "驳油",
				value: 2
			},
			{
				label: "核油",
				value: 3
			}
		],
		visible() {
			return mode.value !== "edit";
		}
	},
	{
		label: "登记船舶",
		type: "singleSelect",
		name: "registerShipId",
		api: getOptions,
		visible() {
			return regType.value !== RegTypeEnum.TRANSFER;
		},
		disabled() {
			return defaultShipId !== -1;
		},
		otherAttr: { labelKey: "name", valueKey: "id" }
	},
	{
		label: "供油船舶",
		type: "singleSelect",
		name: "provideShipId",
		api: getOptions,
		visible() {
			return regType.value === RegTypeEnum.TRANSFER;
		},
		otherAttr: {
			labelKey: "name",
			valueKey: "id",
			disabledOption(item) {
				return item.value === receiveShipId.value;
			}
		}
	},
	{
		label: "受油船舶",
		type: "singleSelect",
		name: "receiveShipId",
		api: getOptions,
		visible() {
			return regType.value === RegTypeEnum.TRANSFER;
		},
		otherAttr: {
			labelKey: "name",
			valueKey: "id",
			disabledOption(item) {
				return item.value === provideShipId.value;
			}
		}
	},
	{
		label: "登记油量",
		type: "input",
		name: "registerOil",
		placeholder: "请填写",
		otherAttr: { showWordLimit: false, append: "t", inputOnlyNumber: true, inputOnlyNonnegativeNumber: true, precision: 3 }
	},
	{
		label: () => {
			return RegTypeTextEnum[regType.value] + "时间";
		},
		type: "dateTime",
		name: "completeTime",
		placeholder: "请选择",
		disabledDate: (date: Date) => {
			return date.getTime() > Date.now();
		}
	},
	{
		label: "单价",
		type: "input",
		name: "oilPrice",
		placeholder: "请填写",
		otherAttr: { showWordLimit: false, append: "元/t", inputOnlyNumber: true, inputOnlyNonnegativeNumber: true, precision: 2 },
		visible() {
			return regType.value === RegTypeEnum.ADD;
		}
	},
	{
		label: "总价",
		type: "input",
		name: "oilTotalPrice",
		placeholder: "请填写",
		otherAttr: { showWordLimit: false, append: "元", inputOnlyNumber: true, inputOnlyNonnegativeNumber: true, precision: 2 },
		disabled() {
			return (oilPrice.value + "").length > 0;
		},
		visible() {
			return regType.value === RegTypeEnum.ADD;
		}
	},
	{
		label: "备注",
		type: "textarea",
		name: "remark",
		placeholder: "请填写",
		otherAttr: { max: 200 }
	}
];

const formConfig: FormConfigOptions = {
	key: "oil-reg-form",
	items: formContent,
	addApi: OilService.addOilReg,
	editApi: OilService.updateOilReg,
	detailApi: OilService.getOilReg,
	successCallBack: handleSuccess,
	beforeSubmit: beforeSubmit,
	detailKey: "uuid",
	closeFunction: close,
	closeFormAt: "both",
	dataCallBack: dataCallBack,
	hintMessage: ["登记成功", "保存成功"]
};
function handleSuccess() {
	emits("onSuccess");
}

function beforeSubmit(data: any) {
	data.registerOil = Number(data.registerOil);
	if (data.type === RegTypeEnum.TRANSFER || regType.value === RegTypeEnum.TRANSFER) {
		data.registerShipId = receiveShipId.value;
		data.provideShipId = provideShipId.value;
	}
	data.oilPrice && (data.oilPrice = (data.oilPrice * 100).toFixed(2));
	!data.oilPrice && (data.oilTotalPrice = cacheTotalPrice.value);
	data.oilTotalPrice && (data.oilTotalPrice = (data.oilTotalPrice * 100).toFixed(2));
	return data;
}

function dataCallBack(data: any) {
	mode.value === "edit" && (regType.value = data.type);
	if (data.type === RegTypeEnum.TRANSFER) {
		receiveShipId.value = data.registerShipId;
		data.receiveShipId = data.registerShipId;
		provideShipId.value = data.provideShipId;
	}
	data.oilPrice && (data.oilPrice = (Number(data.oilPrice) / 100).toFixed(2));
	data.oilTotalPrice && (data.oilTotalPrice = (Number(data.oilTotalPrice) / 100).toFixed(2));
	return data;
}

//手动调用metaForm的初始化
let form = ref();

const { confirmClose } = useDialog(form);

async function iniData() {
	let addParams: { [key: string]: any } = { type: RegTypeEnum.ADD };
	if (defaultShipId !== -1) {
		addParams.provideShipId = defaultShipId;
		addParams.registerShipId = defaultShipId;
	}
	form.value.iniForm(mode.value, editData, {}, addParams);
}

let ruleForm = reactive({
	type: [{ validator: validateType, required: true }],
	registerShipId: [{ message: "请选择登记船舶", required: true }],
	receiveShipId: [{ validator: validateReceive, message: "请选择受油船舶", required: true }],
	provideShipId: [{ validator: validateProvide, message: "请选择供油船舶", required: true }],
	registerOil: [{ validator: validateOil, message: "请填写登记油量", required: true }],
	completeTime: [{ message: "请选择完成时间", required: true }],
	oilPrice: [{ validator: validatePrice }]
});
/** 登记类型检验 */
function validateType(rule: any, value: any, callback: any) {
	if (value) {
		mode.value === "add" && (regType.value = value);
		if (defaultShipId !== -1) {
			form.value.formData.provideShipId = defaultShipId;
			form.value.formData.registerShipId = defaultShipId;
		}
		form.value && form.value.clearValidate();
		callback();
	} else {
		callback(new Error("请选择登记类型"));
	}
}

let oilPrice = ref("");
// 缓存总价，因为提交表单前会出发一次单价校验，如果单价为空总结会被清空，所以用这个变亮保存，在提交前重新赋值
let cacheTotalPrice = ref("");
function validatePrice(rule: any, value: any, callback: any) {
	oilPrice.value = value || "";
	form.value && (cacheTotalPrice.value = form.value.formData.oilTotalPrice);
	if (value) {
		form.value.formData.registerOil &&
			(form.value.formData.oilTotalPrice = (Number(value) * Number(form.value.formData.registerOil || 0)).toFixed(2));
	} else if (value === "") {
		form.value.formData.oilTotalPrice = "";
	}
	callback();
}

function validateOil(rule: any, value: any, callback: any) {
	if (value) {
		form.value.formData.oilPrice &&
			(form.value.formData.oilTotalPrice = (Number(value) * Number(form.value.formData.oilPrice ?? 0)).toFixed(2));
		callback();
	} else {
		form.value.formData.oilPrice && (form.value.formData.oilTotalPrice = "");
		callback(new Error(rule.message));
	}
}

function validateReceive(rule: any, value: any, callback: any) {
	if (value || value === 0) {
		receiveShipId.value = value;
		callback();
	} else {
		callback(rule.message);
	}
}

function validateProvide(rule: any, value: any, callback: any) {
	if (value || value === 0) {
		provideShipId.value = value;
		callback();
	} else {
		callback(rule.message);
	}
}

function getOptions() {
	let list = _.cloneDeep(props.shipList);
	return new Promise(resolve => {
		regType.value === RegTypeEnum.TRANSFER &&
			list.push({
				id: 0,
				name: "非本公司船舶"
			});
		resolve({
			code: 0,
			data: {
				list: list
			}
		});
	});
}

defineExpose({
	open,
	close
});
</script>

<style lang="scss" scoped></style>
