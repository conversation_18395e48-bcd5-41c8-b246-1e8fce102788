<!-- 基础表单组件-->
<template>
	<div>
		<el-form
			:inline="true"
			:label-position="styleConfig.labelPosition ? styleConfig.labelPosition : styleConfig.mode === 'drawer' ? 'top' : 'right'"
			:style="{ 'margin-bottom': styleConfig.mode === 'drawer' ? '40px' : '0' }"
			ref="ruleFormRef"
			:model="formData"
			:rules="ruleForm"
			:label-width="styleConfig.labelWidth ?? '80px'"
			:scroll-to-error="true"
			:inline-message="true"
			@validate="handleValidate"
		>
			<el-row :gutter="10" style="width: 100%">
				<FormItem ref="formItemRef" v-for="(item, index) of formConfig.items" :key="index" :config="item">
					<template v-for="(slot, slotIndex) in customSlots" :key="slotIndex" #[slot.name!.toString()]>
						<slot :name="slot.name" :formData="formData"></slot>
					</template>
					<template v-if="item.hintSlot" #[item.hintSlot!]>
						<slot :name="item.hintSlot"></slot>
					</template>
					<template v-if="item.type === 'title'" #[item.name!.toString()]>
						<slot :name="item.name"></slot>
					</template>
					<template #[item.name!.toString().concat(`Label`)]>
						<slot :name="`${item.name}Label`"></slot>
					</template>
				</FormItem>
				<el-col v-if="styleConfig.mode !== 'drawer'" :span="24">
					<el-form-item label=" " style="width: 100%; margin: 12px 0 0">
						<div
							class="form-button-group"
							:style="{
								'flex-direction': buttonGroupDirection
							}"
						>
							<el-button
								v-if="!underBtns"
								:disabled="submitDisabled_file || submitDisabled_image"
								type="primary"
								@click="submitForm"
								:loading="isSubmitLoading"
								>{{ confirmBtnText }}</el-button
							>
							<el-button v-if="!underBtns" @click="cancel">{{ styleConfig.cancelBtnText ?? "取消" }}</el-button>
						</div>
						<!--自定义按钮插槽-->
						<slot name="button" :formData="formData" :formRef="ruleFormRef"></slot>
					</el-form-item>
				</el-col>
			</el-row>
		</el-form>
		<div v-if="styleConfig.mode === 'drawer'" class="form-button-group-drawer">
			<el-button
				v-if="!underBtns"
				type="primary"
				:disabled="submitDisabled_file || submitDisabled_image"
				@click="submitForm"
				:loading="isSubmitLoading"
				>{{ confirmBtnText }}</el-button
			>
			<el-button v-if="!underBtns" @click="cancel">{{ styleConfig.cancelBtnText ?? "取消" }}</el-button>
			<!--自定义按钮插槽-->
			<slot name="button" :formData="formData" :formRef="ruleFormRef"></slot>
		</div>
	</div>
</template>

<script setup lang="ts">
import { ref, provide, PropType, computed, useSlots, onBeforeUnmount } from "vue";
import type { FormInstance, FormItemProp, FormRules } from "element-plus";
import FormItem from "./form-items/index.vue";
import { FormConfigOptions, StyleConfigOptions } from "./interface";
import { useForm } from "./useForm";
import { Arrayable } from "element-plus/es/utils/typescript";

const ruleFormRef = ref<FormInstance>();
const formItemRef = ref();
let isSubmitLoading = ref(false);

// const formData: { [key: string]: any } = reactive({});

//用于检测底部slot插槽是否被占用。被占用则不显示默认提交
let underBtns = useSlots().button;

const props = defineProps({
	/**表单详细配置项，具体参考interface文件 */
	formConfig: { type: Object as PropType<FormConfigOptions>, required: true },
	/**表单样式配置项，可不传 */
	styleConfig: {
		type: Object as PropType<StyleConfigOptions>,
		default: () => {
			return {
				mode: "page",
				labelWidth: "120px",
				labelPosition: "right",
				createBtnText: "创建",
				editBtnText: "保存",
				cancelBtnText: "取消"
			};
		}
	},
	/**表单校验项。参考element */
	ruleForm: {
		type: Object as PropType<FormRules>,
		default: () => {
			return {};
		}
	}
});

const { formData, _iniForm, clear, submit, mode, clearDraft, isFormDataChanged } = useForm({
	formRef: ruleFormRef,
	addApi: props.formConfig.addApi,
	editApi: props.formConfig.editApi,
	dataCallBack: props.formConfig.dataCallBack,
	beforeSubmit: props.formConfig.beforeSubmit,
	detailKey: props.formConfig.detailKey,
	detailApi: props.formConfig.detailApi,
	successCallBack: props.formConfig.successCallBack,
	hintMessage: props.formConfig.hintMessage ?? ["创建成功", "保存成功"],
	formKey: props.formConfig.key,
	isSaveDraft: props.formConfig.isSaveDraft ?? false,
	beforeSaveDraft: props.formConfig.beforeSaveDraft,
	beforeReadDraft: props.formConfig.beforeReadDraft,
	itemOptions: props.formConfig.items,
	changeLogApi: props.formConfig.changeLogApi
});

provide("formData", formData);
/**校验规则 */
provide("ruleForm", props.ruleForm);
/**是否禁用提交按钮。upload组件将inject本变量并进行操作 */
const submitDisabled_file = ref(false);
provide("submitDisabled_file", submitDisabled_file);
const submitDisabled_image = ref(false);
provide("submitDisabled_image", submitDisabled_image);

/**根据不同传参计算按钮文字 */
let confirmBtnText = computed(() => {
	if (mode.value === "add") {
		return props.styleConfig.createBtnText ?? "创建";
	} else {
		return props.styleConfig.editBtnText ?? "保存";
	}
});

let buttonGroupDirection = computed(() => {
	if (props.styleConfig?.mode == "dialog") {
		return "row-reverse";
	} else {
		return "row";
	}
});

/**计算自定义slot的数组，用于动态生成插槽 */
let customSlots = computed(() => {
	let res = [];
	for (let key in props.formConfig.items) {
		if (props.formConfig.items[key].type === "slot") {
			res.push(props.formConfig.items[key]);
		}
	}
	return res;
});

/**所有表单项的验证结果 */
const validateResults = ref({} as { [key: string]: boolean });
provide("validateResults", validateResults);
/**任意项进行校验时触发 */
function handleValidate(itemProp: string, result: boolean) {
	validateResults.value[itemProp] = result;
}

const emits = defineEmits(["onSubmit", "onErrorSubmit", "onCancel"]);

/**提交表单 */
const submitForm = async () => {
	const formEl = ruleFormRef.value;
	if (!formEl) return false;
	isSubmitLoading.value = true;
	try {
		await formEl.validate(async (valid, fields) => {
			if (valid) {
				try {
					let submitResult = await submit();
					console.log(submitResult, "submitResult");
					if (submitResult) {
						emits("onSubmit", formData);
						tryCloseForm("onSuccess");
					}
					setTimeout(() => {
						isSubmitLoading.value = false;
					}, 1000);
					return submitResult;
				} catch (err) {
					console.error("表单提交报错", err);
					isSubmitLoading.value = false;
					return false;
				}
			} else {
				emits("onErrorSubmit", fields);
				isSubmitLoading.value = false;
				return false;
			}
		});
	} catch {
		isSubmitLoading.value = false;
		return false;
	}
};

/**单纯进行表单校验并返回结果。主要用于外部调用 */
const doValidate = async () => {
	if (!ruleFormRef?.value) return false;
	try {
		let r = await ruleFormRef.value.validate(async valid => {
			if (valid) {
				return true;
			} else {
				return false;
			}
		});

		return r;
	} catch (err) {
		console.error("sth wrong in doValidate", err);
		return false;
	}
};

/**对单一表单项进行校验 */
const doSingleValidate = async (propName: string) => {
	if (!ruleFormRef?.value) return false;
	try {
		let r = await ruleFormRef.value.validateField([propName], async valid => {
			if (valid) {
				return true;
			} else {
				return false;
			}
		});

		return r;
	} catch (err) {
		console.error("sth wrong in doValidate", err);
		return false;
	}
};

/**副按钮操作，取消/返回等 */
const cancel = () => {
	emits("onCancel", formData);
	tryCloseForm("onCancel");
};

/**尝试退出 */
async function tryCloseForm(enterBy: "onSuccess" | "onCancel") {
	//config中未指定关闭时机，或指定both，或与进入本函数的时机相同，则触发
	if ((props.formConfig.closeFormAt ?? "both") === "both" || (props.formConfig.closeFormAt ?? "both") === enterBy) {
		props.formConfig.closeFunction && props.formConfig.closeFunction();
	}
}

onBeforeUnmount(() => {
	// saveDraft();
});
function iniForm(mode: "add" | "edit", detailData: object, staticParams: object = {}, iniData: object = {}) {
	_iniForm(mode, detailData, staticParams, iniData);
	formItemRef.value.forEach((element: any) => {
		element.init();
	});
}

function clearValidate(props?: Arrayable<FormItemProp> | undefined) {
	ruleFormRef.value && ruleFormRef.value.clearValidate(props);
}

/**表单中，所有下拉组件候选项的数据汇总 */
const selectionOptionsData = ref({} as { [key: string]: any });
provide("selectionOptionsData", selectionOptionsData);

//外部可调用clear函数清空表单数据
defineExpose({
	formData,
	selectionOptionsData,
	clear,
	submit,
	iniForm,
	doValidate,
	clearDraft,
	submitForm,
	doSingleValidate,
	clearValidate,
	isFormDataChanged,
	submitDisabled: computed(() => submitDisabled_file.value || submitDisabled_image.value)
});
</script>

<style lang="scss">
.form-button-group {
	display: flex;
	width: 100%;
	.el-button + .el-button {
		margin: 0 12px;
	}
}
.form-button-group-drawer {
	position: absolute;
	right: 0;
	bottom: 0;
	z-index: 10;
	display: flex;
	flex-direction: row-reverse;
	width: calc(100% - 40px);
	padding: 12px 20px;
	background-color: $color-blank;
	border-top: solid 1px #ebeef5;
	.el-button + .el-button {
		margin: 0 12px;
	}
}
</style>
