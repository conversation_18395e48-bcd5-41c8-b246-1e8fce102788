<!-- 筛选组件中的单选下拉菜单组件 -->
<template>
	<el-select
		:clearable="config.clearable ?? true"
		filterable
		:remote="config.otherAttr?.remote"
		:remote-method="remoteMethod"
		:loading="loading"
		:remote-show-suffix="true"
		:default-first-option="true"
		:allow-create="config.otherAttr?.allowCreate ?? false"
		v-model="formData[config.name as string]"
		:placeholder="isFunction(config.placeholder) ? config.placeholder() : config.placeholder"
		:multiple="config.otherAttr?.multiple ?? false"
		style="width: 100%"
		:disabled="config.disabled ? config.disabled(formData) : false"
		@change="handleSelect"
		@clear="handleClear"
		:reserve-keyword="false"
	>
		<el-option
			v-for="item in options"
			:key="item.value"
			:label="item.label"
			:value="config.otherAttr?.valueIsAllData ? JSON.stringify(item.data) : item.value"
			:disabled="config.otherAttr?.disabledOption ? config.otherAttr?.disabledOption(item) : false"
		>
			<span style="float: left">{{ item.label }}</span>
			<span v-if="config.otherAttr?.subLabelKey" style="float: right; font-size: 12px; color: var(--el-text-color-secondary)">{{
				item.data[config.otherAttr?.subLabelKey] ?? ""
			}}</span>
			<span
				v-if="!config.otherAttr?.subLabelKey && config.otherAttr?.formatSubLabel"
				style="float: right; font-size: 12px; color: var(--el-text-color-secondary)"
			>
				{{ config.otherAttr?.formatSubLabel(item) }}
			</span>
		</el-option>
	</el-select>
</template>

<script setup lang="ts">
import { inject, reactive, onMounted, PropType, ref, watch, Ref } from "vue";
import { AppendOption, FormItemOptions, SelectOption } from "../interface";
import { getSelectionOptions } from "@/meta-components/MetaUtils/FormUtils";
import { isFunction } from "lodash";

let options = reactive<Array<SelectOption>>([]);
let formData = inject("formData") as { [key: string]: any };
let props = defineProps({
	config: {
		type: Object as PropType<FormItemOptions>,
		required: true
	}
});

//远程搜索相关内容
let loading = ref(false);
const remoteMethod = async (query: string) => {
	if (query) {
		loading.value = true;
		await getOptions(query);
		loading.value = false;
	} else {
		for (let i = options.length; i > options.length; i--) {
			options.splice(i, 1);
		}
	}
};
/**清空时，若为远程搜索，重新搜索获取候选项 */
async function handleClear() {
	if (!props.config.otherAttr?.remote) return;
	loading.value = true;
	await getOptions("");
	loading.value = false;
}

/**选项发生变化时 */
function handleSelect(v: any) {
	//在候选项中拿到对应的label，根据配置项appendLabelToFormDataWithKey添加进formData中
	if (props.config.otherAttr?.appendLabelToFormDataWithKey) {
		let targetLabel;
		if (Array.isArray(v)) {
			// 多选收集所有 label到一个数组
			targetLabel = v.map(val => options.find(o => o.value === val)?.label);
		} else {
			// 单选
			targetLabel = options.find(o => o.value === v)?.label;
		}
		if (!targetLabel || (Array.isArray(targetLabel) && targetLabel.length === 0)) {
			//不存在对应候选项时，删除此前对应添加进formData的数据
			if (formData.value) {
				delete formData.value[props.config.otherAttr.appendLabelToFormDataWithKey];
			} else {
				delete formData[props.config.otherAttr.appendLabelToFormDataWithKey];
			}
			return;
		}
		if (formData.value) {
			formData.value[props.config.otherAttr.appendLabelToFormDataWithKey] = targetLabel;
		} else {
			formData[props.config.otherAttr.appendLabelToFormDataWithKey] = targetLabel;
		}
	}

	//在候选项中拿到对应的候选项数据，根据配置项appendLabelToFormDataWithKey添加进formData中
	if (props.config.otherAttr?.appendOptionToFormDataWithKey) {
		let targetOption = options.find(o => o.value === v);
		if (!targetOption) {
			// 首次回显 不删除formData原始带过来的数据
			if (firstRead) return;
			//不存在对应候选项时，删除此前对应添加进formData的数据
			for (let option of props.config.otherAttr?.appendOptionToFormDataWithKey) {
				deleteOptionKey(option);
			}
			return;
		}
		for (let option of props.config.otherAttr?.appendOptionToFormDataWithKey) {
			addOptionKey(option, targetOption);
		}
	}

	function addOptionKey(appendOption: AppendOption, theOption: SelectOption) {
		let { formKey, optionKey } = appendOption;
		if (formData.value) {
			formData.value[formKey] = theOption.data[optionKey];
		} else {
			formData[formKey] = theOption.data[optionKey];
		}
	}

	function deleteOptionKey(appendOption: AppendOption) {
		let { formKey } = appendOption;
		if (formData.value) {
			delete formData.value[formKey];
		} else {
			delete formData[formKey];
		}
	}
}
onMounted(() => {
	getOptions();
});

let _remoteOptions: any = {};

let firstRead = true;
let firstGotValue: any = ""; //数据回填时，第一次获取到的值。之后需要和候选项进行比对，查看是否在候选项中
watch(
	() => formData.value,
	(newV: any, oldV: any) => {
		//记录第一次数据回填获取到的值
		if (newV[props.config.name as string] && !oldV[props.config.name as string]) {
			handleSelect(newV[props.config.name as string]); //第一次数据回填时，需要触发handleChange
			firstGotValue = newV[props.config.name as string];
			checkOptionExsist(firstGotValue, options);
		}
		//首次获取到时，若为远程加载，手动添加候选项
		if (props.config.otherAttr?.remote === true && newV[props.config.name as string] && !oldV[props.config.name as string]) {
			if (_remoteOptions && _remoteOptions[props.config.name as string]) {
				options.splice(0, options.length, ..._remoteOptions[props.config.name as string]);
			} else {
				let obj: { [key: string]: any; label: string; value: any } = {
					value: newV[props.config.name as string],
					label: formData.value[props.config.otherAttr!.remoteLabelKey!],
					data: {}
				};
				if (props.config.otherAttr?.subLabelKey) {
					obj.data[props.config.otherAttr?.subLabelKey] = formData.value[props.config.otherAttr!.subLabelKey!];
				}
				options.splice(0, options.length, obj);
			}
		}
		//针对读取草稿的情况，每次保存formData时，额外保存options。忽略第一次
		if (props.config.otherAttr?.remote === true && !firstRead) {
			if (!_remoteOptions) {
				_remoteOptions = {
					[props.config.name as string]: options
				};
			} else {
				_remoteOptions[props.config.name as string] = options;
			}
		}
		if (firstRead) {
			firstRead = false;
		}
	},
	{ deep: true }
);

const selectionOptionsData: Ref<{ [key: string]: any }> = inject("selectionOptionsData")!;

/**记录是否已获取到候选项 */
let didGetOptions = false;
/**获取候选项 */
async function getOptions(query?: string) {
	let optionsData = await getSelectionOptions(props.config, query);
	if (!optionsData) return;
	didGetOptions = true;
	if (optionsData) {
		options.splice(0, options.length, ...optionsData);
	} else {
		options.splice(0, options.length);
	}
	checkOptionExsist(firstGotValue, optionsData);

	//记录候选项以备用
	selectionOptionsData?.value && (selectionOptionsData.value[props.config.name as string] = options);
}

/**确认回填的数据是否在候选项中存在 */
function checkOptionExsist(insertValue: any, options: { label: string; value: any; [key: string]: any }[]) {
	if (!insertValue || props.config.otherAttr?.remote || !didGetOptions) return;
	let isExsist = false;
	//多选与单选的判断方式不同
	if (props.config.otherAttr?.multiple) {
		isExsist = options.some(o => (insertValue as any[]).includes(o.value));
	} else {
		isExsist = options.some(o => o.value == insertValue);
	}

	if (!isExsist) {
		delete formData.value[props.config.name as string];
	} else {
		handleSelect(insertValue);
	}
}
</script>

<style scoped></style>
