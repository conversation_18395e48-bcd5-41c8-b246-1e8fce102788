import { FormItemOptions } from "@/meta-components/MetaForm/interface";

export namespace Table {
	export interface Pageable {
		pageNum: number;
		pageSize: number;
		total: number;
	}
	export interface TableStateProps {
		tableData: any[];
		sortData: string[];
		pageable: Pageable;
		staticParam: {
			[key: string]: any;
		};
		searchInitParam: {
			[key: string]: any;
		};
		totalParam: {
			[key: string]: any;
		};
		icon?: {
			[key: string]: any;
		};
	}
}

/*
 * 筛选配置的interface
 */
export interface FilterConfigOptions {
	/**筛选项name */
	name: string;
	/**筛选类型 */
	type:
		| "input" //文本输入
		| "singleSelect" //单选
		| "singleSelectPro" //单选添加远程搜索
		| "multiSelect" //多选
		| "cascader" //级联
		| "dateRange" //日期区间
		| "monthRange" //月份区间
		| "timeRange"; //时间区间
	/**筛选项占据栅格数，以4/6/8/12取值为优 */
	span: number;
	/**筛选框占位符，即提示文字 必须是字符串类型或者是返回字符串的函数类型 */
	placeholder: string | (() => string);
	/**获取下拉菜单数据的接口地址。若已设置静态候选项staticOptions，则忽略本项 */
	api?: (params?: any) => Promise<any>;
	/**静态下拉菜单候选项 */
	staticOptions?: Array<SelectOptions>;
	/**其他参数。当对输入框有其他要求时，可修改对应组件并传入本项 */
	otherAttr?: OtherAttr;
	/**是否可见。默认可见 */
	visible?: () => boolean;
}

interface OtherAttr {
	/**有些组件可用，等同于组件本身的props属性。目前支持cascader */
	props?: any;
	/**选择器组件用。候选项label字段在后端返回数据中对应的key */
	labelKey?: string;
	/**选择器组件用。候选项value字段在后端返回数据中对应的key */
	valueKey?: string;
	/**选择器组件用，候选项的列表字段对应的key */
	listKey?: string;
	/**选择器组件用。是否可多选 */
	multiple?: boolean;
	/**输入框 最大输入长度 默认 30 */
	maxlength?: number;
	/**选择器组件用。是否开启远程搜索。开启后，itemOption中的api将作为搜索用api */
	remote?: boolean;
	/**选择器选择内容后，同时将label对应的值也添加到filterResult中。本项即为对应的key。若不填则不添加 */
	appendLabelToFormDataWithKey?: string;
	/**选择器选择内容后，同时额外将选项中的其他内容添加到filterResult中。若不填则不添加 */
	appendOptionToFormDataWithKey?: AppendOption[];
	/**选择器组件用。当开启远程搜索时，编辑表单的情况下，需要在选择器组件中进行数据回显。通过本属性从detailData中找到对应数据并进行回显 */
	remoteLabelKey?: string;
	/**选择器组件用，选项右侧的提示文字，不填则不展示 */
	subLabelKey?: string;
}

/**appendOptionToFormDataWithKey的接口。从候选项中找到optionKey对应值，添加到filterResult中[formKey]字段上 */
export interface AppendOption {
	/**后选项中的key */
	optionKey: string;
	/**添加到filterResult中时使用的key */
	formKey: string;
}

/**
 * 下拉候选菜单的interface
 */
interface SelectOptions {
	/**显示文字。与element-plus不同，项目中需必填 */
	label: string;
	/**值 */
	value: string | number;
}

/**
 * 标签页配置的interface
 */
export interface TabsConfig {
	/**标签页数据，对象数组 */
	tabList: Array<TabList>;
	/**默认在外展示的标签页数量。多余的将放入“更多”按钮中 */
	showCount: number;
	/**tab选项注入筛选时，对应的字段名 */
	filterAttr: string;
	/**子tab（children）注入筛选时，对应的字段名 */
	subFilterAttr?: string;
}

/**tablist中每项tab的interface */
export interface TabList {
	/**展示用label */
	label: string;
	/**筛选用对应值 */
	value: any;
	/**二级标签 */
	children?: Array<TabList>;
	/** 是否展示 */
	visible?: boolean;
	/** 红点配置 */
	badge?: {
		/** 是否是以小圆点形式展示 否则显示徽标数 */
		isDot?: boolean;
		/** 未读数量 */
		count: number | (() => number);
	};
}

/*
 * 表格本体的配置项
 */
export interface TableConfig {
	/**表格的key，用于记录和读取表格配置文件 */
	key: string;
	/**是否展示标题区域。包括分页标签、主功能按钮所在的区域。默认true */
	showTitleArea?: boolean;
	/**default或不填-表格页样式；mini-详情页或弹窗、表单等处使用的样式，更迷你 */
	mode?: "default" | "mini";
	/**是否可调整列宽。默认可调整 */
	resizable?: Boolean;
	/**是否可调整表头。默认true */
	canChangeHead?: Boolean;
	/**列配置项 */
	columns: Array<ColumnProps>;
	/**数据请求api */
	requestApi: (params: any) => Promise<any>;
	/**api获取数据后，根据listKey读取data下的字段。默认list。用于后端未使用list返回列表数据的特殊情况 */
	listKey?: string;
	/**表格是否支持选择。不支持|单选|多选 //TODO 还需可支持展开 */
	selectType: "none" | "single" | "multi";
	/**选择组件的id字段的名称。视数据本身id、uuid等。仅选择类型为single或multi时可用 */
	selectId?: string;
	/**选择组件是否记录被勾选项的全部信息。默认false */
	isSelectItem?: boolean;
	/**列表支持单选或多选时，设置行是否可选的判断条件。 */
	selectable?: (data?: any, index?: number) => any;
	/**是否存在翻页组件 */
	pagination: boolean;
	/**分页页码 */
	pageNum?: number;
	/**分页大小 */
	pageSize?: number;
	/**固定筛选参数 */
	staticParam?: { [key: string]: any };
	/**初始化筛选参数 */
	initParam?: { [key: string]: any };
	/**当数据存在 children 时，指定 children key 名字 ==> 非必传（默认为"children"）*/
	childrenName?: string;
	/**数据处理回调 */
	dataCallback?: (data: any) => any;
	/**数据获取完毕后执行的函数回调 */
	callBack?: (data: any) => any;
	//TODO 未实现。排序的API排序API和状态修改API尚未实现
	sortApi?: (params: any) => Promise<any>;
	statusApi?: (params: any) => Promise<any>;
	/**是否可进行行内编辑。主要影响是否在表格底部增加一行添加按钮 */
	canInlineEdit?: boolean;
	/**是否在表格底部展示添加数据按钮（占据一整行），默认true。为false时，需要通过其他方式手动调起行内数据添加功能 */
	showInlineAddBtn?: boolean;
	/**行内编辑新增，从顶部or底部插入新行，默认bottom*/
	inlineInsertArea?: "top" | "bottom";
	/**行内编辑 添加按钮文案 */
	inlineAddText?: string;
	/**打开行内编辑前执行的方法, 返回值决定是否继续执行 */
	beforeEnterInlineMode?: (params: any) => boolean;
	/**行内编辑的添加数据API */
	inlineAddApi?: (params: any) => Promise<any>;
	/**行内编辑的编辑数据API */
	inlineEditApi?: (params: any) => Promise<any>;
	/**行内编辑，添加和编辑调用接口时额外传入的参数 */
	inlineStaticParams?: { [key: string]: any };
	/**行内编辑，提交表单时的自定义校验 */
	inlineSubmitValidator?: (params: any) => InlineSubmitValidatorOptions;
	/**批量操作功能区是否显示在行内。默认true。为false时自动变为position:absolute */
	isSelectionInline?: boolean;
	/**批量操作功能区的style。仅在非行内模式时生效 */
	selectionStyle?: object;
	/**树形结构表单的子节点属性名。默认children */
	childrenKey?: string;
	/**树形表格是否自动展开全部。默认true */
	defaultExpandAll?: boolean;
	/**从缓存中重新取出时是否需要刷新表格。 */
	needRefreshOnActivated?: boolean;
	/**树形表格是否需要导出子节点，默认导出，导出的子节点是childrenKey */
	exportChildren?: boolean;
	/** 同时显示标签页和所选择结果，若为false，则勾选任意表格内的内容都会导致tabs消失，只留下选择的选项数，默认为false。 */
	showTabsAndSelected?: boolean;
}

/**行内编辑表单提交的返回参数 */
interface InlineSubmitValidatorOptions {
	success: boolean;
	msg: string;
	[key: string]: any;
}

/**
 * 表格每列的配置项
 */
export interface ColumnProps {
	label: string; // 单元格标题 字符串类型（非特殊类型必填）
	/**插槽名。若以简单数据形式显示数据则使用prop+type，若自定义单元格复杂内容则使用slotName。【注意】操作按钮列必须使用slotName:"opr"*/
	slotName?: string;
	prop?: string; // 单元格数据（非特殊类型必填）
	type?:
		| "default" // 默认文本
		| "index"; // 显示索引
	/**展示类型。使用prop时，使用本类型指定数据的展示形式。默认文本text;date：将时间字符串接取至年月日;time：将日期字符串截取至时分秒 */
	showType?: "text" | "date" | "time";
	width?: number | string; // 列宽
	minWidth?: number | string; // 最小列宽
	sortable?: boolean; // 是否可排序（动态排序）
	sortAttr?: string; //排序时传参的属性。仅在sortable=true时生效
	fixed?: "left" | "right"; // 固定列
	/**是否展示本列。供表格筛选使用 */
	isShow?: boolean;
	/**是否可以设置本列的显隐。默认true。若设为false，则无法在表头控制组件中对其进行操作 */
	canChangeIsShow?: boolean;
	/**进行行内编辑时的相关配置。不填则无法进行行内编辑 */
	inlineEdit?: FormItemOptions;
	/**表头提示内容*/
	headerHint?: string;
	/**是否设置为特殊列，目前支持sort（自带特殊表头且支持表格内双击修改排序值）。需设置tableConfig中canInlineEdit为true */
	special?: "sortNum";
	/**是否可见。默认可见 */
	visible?: (param?: any) => boolean;
	/**自定义表头插槽名称 */
	customHeader?: string;
	/**每一列的key值，可选，不填时为index，避免组件库多个tab切换排序时造成sort-by为undefined情况 */
	key?: string;
}

/**行内编辑参数的interface */
export interface InlineEditPropsOption {
	/**是否开启行内编辑 */
	open: boolean;
	/**目标行数据的id。若为undefined则为新建数据 */
	targetId: string | number | undefined;
	/**编辑对象的原数据 */
	data: any;
	/**是否聚焦在特殊字段上。目前仅支持sort。若设为sort，则会仅开启sort列的行内编辑 */
	focusOnSpecial?: "sortNum" | undefined;
}

/**
 * 下拉候选菜单的interface
 */
export interface SelectOption {
	/**显示文字。与element-plus不同，项目中需必填 */
	label: string;
	/**值 */
	value: any;
	/**包含label和value在内的，后端给到的其他数据。特殊情况下使用 */
	data?: any;
	children?: SelectOption[];
}

/**
 * 数据导出参数的interface
 */
export interface ExportConfig<T> {
	/**
	 * 请求分页参数
	 * 默认 offset = 0, length = 100
	 */
	pageParams?: { offset: number; length: number };
	/** 
	 * 字段转换输出规则
	 * 属性参考案例：
	 * principalName: data => {
			 return `${data.principalName} ${data.principalMobile}`;
		 },
	 */
	formatRule?: {
		[key: string]: (data: T) => string | number;
	};
	/** 文件名 */
	fileName: string;
	/** 导出按钮文字 */
	buttonText?: string;
	/** 是否可导出 */
	isExport?: boolean;
	/** 排除字段 */
	excludes?: () => Array<string>;
	/** 额外表头配置 用于双表头 */
	tableHeaderArray?: TableHeaderArrayConfig;
}

/**
 * 定义额外表头 TableHeaderArrayConfig 为数组
 */
export type TableHeaderArrayConfig = Array<{ [pro: string]: string }>;

/**
 * 数据导出参数的interface，包含多个导出按钮的配置
 */
export interface ExportButtonConfig<T> {
	/** 导出按钮的列表 */
	buttons: ExportConfig<T>[];
}
