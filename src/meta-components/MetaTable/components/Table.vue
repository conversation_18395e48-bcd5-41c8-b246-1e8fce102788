<!-- 标准表格 -->
<template>
	<div class="table-inner-container" :class="{ disabled: disabled }">
		<HeadConfig
			ref="tableHeader"
			:enable="(tableConfig.canChangeHead ?? true) && !(tableConfig.canInlineEdit ?? false)"
			class="config-icon"
			:tableKey="tableConfig.key"
			:mode="tableConfig.mode"
			:columnConfig="tableConfig.columns"
			@on-change-config="handleChangeHeadConfig"
		/>
		<!--行内编辑状态下，顶部新增一块添加数据用的table -->
		<el-table
			v-if="canShowInlineAddTable && (tableConfig?.inlineInsertArea ?? 'bottom') === 'top'"
			class="meta-table"
			ref="editTable"
			:border="true"
			:show-header="true"
			:data="inlineEditProps.open && !inlineEditProps.targetId ? inlineEditData : []"
			:select-on-indeterminate="tableConfig.selectType !== 'single'"
			:row-key="tableConfig.selectId ?? 'uuid'"
			:tree-props="{ children: tableConfig.childrenKey ?? 'children' }"
			@selection-change="selectionChange"
			@sort-change="handleSortChange"
			:header-cell-class-name="handleClassName"
			:header-cell-style="calcHeadCellStyle"
			:cell-style="calcCellStyle"
			:default-expand-all="tableConfig.defaultExpandAll ?? true"
		>
			<template #empty>
				<div
					v-if="canShowInlineAdd"
					style="
						display: flex;
						align-items: center;
						justify-content: center;
						width: 100%;
						height: 100%;
						font-size: 14px;
						cursor: pointer;
					"
					class="inline-add-block"
					@click="enterInlineMode"
				>
					<el-icon class="el-icon--left">
						<Plus />
					</el-icon>
					{{ tableConfig.inlineAddText }}
				</div>
			</template>

			<el-table-column
				v-if="tableConfig.selectType === 'multi' || tableConfig.selectType === 'single'"
				type="selection"
				:reserve-selection="true"
				fixed="left"
			>
			</el-table-column>
			<template v-for="(item, index) in showColumns" :key="item.key ? item.key : index">
				<el-table-column
					v-if="item.isShow !== false && !inlineEditProps.targetId && item.special !== 'sortNum' && checkVisible(item.visible)"
					:prop="item.prop"
					:label="item.label"
					:width="item.width"
					:min-width="item.minWidth"
					:sortable="item.sortable ? 'custom' : false"
					:sort-by="item.sortAttr"
					:show-overflow-tooltip="Boolean(item.prop)"
					:resizable="tableConfig.resizable ?? true"
					:fixed="item.fixed"
				>
					<template #default>
						<FormItem :show-message="false" v-if="item.inlineEdit" :config="item.inlineEdit">
							<template #[item.inlineEdit.name!.toString()]>
								<slot :name="item.inlineEdit.name" :formData="inlineEditProps.data"></slot>
							</template>
						</FormItem>
						<div v-else>
							<div v-if="item.slotName === 'opr'">
								<el-link style="margin-right: 12px" :underline="false" type="primary" @click="inlineSave">保存</el-link>
								<el-link style="margin-right: 12px" :underline="false" type="primary" @click="inlineCancel">取消</el-link>
							</div>
							<div v-else class="text-color-hint">-</div>
						</div>
					</template>
				</el-table-column>

				<!-- 特殊列sort: 可在表格内点击编辑 -->
				<el-table-column
					v-if="item.isShow !== false && item.special === 'sortNum' && checkVisible(item.visible)"
					:resizable="tableConfig.resizable ?? true"
					:prop="item.prop"
					:label="item.label"
					:width="item.width"
					:min-width="item.minWidth"
					:sortable="item.sortable ? 'custom' : false"
					:sort-by="item.sortAttr"
					:show-overflow-tooltip="Boolean(item.prop)"
					:fixed="item.fixed"
				>
					<template #header>
						<el-tooltip class="box-item" effect="dark" content="排序值较大的将显示在前。点击可修改排序值" placement="top">
							<div style="display: flex; align-items: center">
								排序值
								<el-icon style="font-size: 14px" class="el-icon--right">
									<QuestionFilled />
								</el-icon>
							</div>
						</el-tooltip>
					</template>
					<template #default="scope">
						<FormItem
							:show-message="false"
							v-if="
								inlineEditProps.open &&
								inlineEditProps.targetId === scope.row.id &&
								(!inlineEditProps.focusOnSpecial || inlineEditProps.focusOnSpecial === 'sortNum')
							"
							:config="
								item.inlineEdit ?? {
									placeholder: '',
									type: 'number',
									name: 'sortNum'
								}
							"
						>
							<template #[item.inlineEdit!.name!.toString()]>
								<slot :name="item.inlineEdit!.name" :formData="inlineEditProps.data"></slot>
							</template>
						</FormItem>
						<slot v-else :name="item.slotName" :row="scope.row">
							<div @click="enterInlineEditMode(scope.row, item, 'sortNum')" style="width: 100%; cursor: pointer">
								<text v-if="item.prop && (item.showType ?? 'text') === 'text'">
									<text v-if="!_.isEmpty(scope.row[item.prop]) || _.isNumber(scope.row[item.prop])">{{
										scope.row[item.prop]
									}}</text>
									<text v-else class="no-data-placeholder">-</text>
								</text>
								<text v-else-if="item.prop && (item.showType === 'date' || item.showType === 'time')">
									<text v-if="!_.isEmpty(reformTimeString(item.showType, scope.row[item.prop]))">{{
										reformTimeString(item.showType, scope.row[item.prop])
									}}</text>
									<text v-else class="no-data-placeholder">-</text>
								</text>
							</div>
						</slot>
						<!-- 否则为"none"即不显示 -->
					</template>
				</el-table-column>
			</template>
		</el-table>

		<el-table
			class="meta-table meta-table-1"
			:class="{ 'meta-table-without-pagination': !tableConfig.pagination }"
			v-if="canShowMainTable"
			:show-header="showMainTableHeader"
			ref="table"
			:border="true"
			:data="tableData"
			:row-key="tableConfig.selectId ?? 'uuid'"
			height="100%"
			@selection-change="selectionChange"
			@sort-change="handleSortChange"
			:header-cell-class-name="handleClassName"
			:cell-style="calcCellStyle"
			:header-cell-style="calcHeadCellStyle"
			:select-on-indeterminate="tableConfig.selectType !== 'single'"
			:tree-props="{ children: tableConfig.childrenKey ?? 'children' }"
			:default-expand-all="tableConfig.defaultExpandAll ?? true"
		>
			<template #empty>
				<el-empty v-if="tableConfig.mode !== 'mini'" description="暂无数据" />
			</template>
			<el-table-column
				:resizable="false"
				v-if="tableConfig.selectType === 'multi' || tableConfig.selectType === 'single'"
				:selectable="
					tableConfig.selectable
						? tableConfig.selectable
						: () => {
								return true;
						  }
				"
				type="selection"
				:reserve-selection="true"
				fixed="left"
			>
			</el-table-column>

			<template v-for="(item, index) in showColumns" :key="item.key ? item.key : index">
				<el-table-column
					v-if="item.isShow !== false && !item.special && checkVisible(item.visible)"
					:resizable="tableConfig.resizable ?? true"
					:type="item.type"
					:prop="item.prop"
					:label="item.label"
					:width="item.width"
					:min-width="item.minWidth"
					:sortable="item.sortable ? 'custom' : false"
					:sort-by="item.sortAttr"
					:show-overflow-tooltip="Boolean(item.prop)"
					:fixed="item.fixed"
				>
					<template #header v-if="item.headerHint">
						<div style="display: flex; align-items: center">
							<div>{{ item.label }}</div>
							<el-tooltip class="box-item" :content="item.headerHint" placement="top">
								<el-icon class="text-color-hint el-icon--right" style="font-size: 14px; transform: rotateX(180deg)"
									><Warning
								/></el-icon>
							</el-tooltip>
						</div>
					</template>
					<!-- 自定义配置每一列 slot（使用作用域插槽） -->
					<template #default="scope">
						<!-- cell模式为data或opr，则读取slot配置显示 -->
						<slot
							v-if="checkCellConentMode(scope.row, item) === 'data' || checkCellConentMode(scope.row, item) === 'common-opr'"
							:name="item.slotName"
							:row="scope.row"
						>
							<text v-if="item.prop && (item.showType ?? 'text') === 'text'">
								<text v-if="!_.isEmpty(scope.row[item.prop]) || _.isNumber(scope.row[item.prop])">{{
									scope.row[item.prop]
								}}</text>
								<text v-else class="no-data-placeholder">-</text>
							</text>
							<text v-else-if="item.prop && (item.showType === 'date' || item.showType === 'time')">
								<text v-if="!_.isEmpty(reformTimeString(item.showType, scope.row[item.prop]))">{{
									reformTimeString(item.showType, scope.row[item.prop])
								}}</text>
								<text v-else class="no-data-placeholder">-</text>
							</text>
						</slot>
						<!-- cell模式为行内编辑opr，则显示保存和取消按钮 -->
						<div v-else-if="checkCellConentMode(scope.row, item) === 'editmode-opr'" :name="item.slotName" :row="scope.row">
							<el-link style="margin-right: 12px" :underline="false" type="primary" @click="inlineSave">保存</el-link>
							<el-link style="margin-right: 12px" :underline="false" type="primary" @click="inlineCancel">取消</el-link>
						</div>
						<!-- cell模式为行内编辑表单组件，则根据配置显示行内表单 -->
						<FormItem
							:show-message="false"
							v-else-if="checkCellConentMode(scope.row, item) === 'form-item'"
							:config="item.inlineEdit ?? { type: 'text', name: '-' }"
						>
							<template #[item.inlineEdit!.name!.toString()]>
								<slot :name="item.inlineEdit!.name!.toString()" :formData="inlineEditProps.data"></slot>
							</template>
						</FormItem>
						<!-- 否则为"none"即不显示 -->
					</template>
				</el-table-column>

				<!-- 特殊列sort: 可在表格内点击编辑 -->
				<el-table-column
					v-if="item.isShow !== false && item.special === 'sortNum' && checkVisible(item.visible)"
					:resizable="tableConfig.resizable ?? true"
					:prop="item.prop"
					:label="item.label"
					:width="item.width"
					:min-width="item.minWidth"
					:sortable="item.sortable ? 'custom' : false"
					:sort-by="item.sortAttr"
					:show-overflow-tooltip="Boolean(item.prop)"
					:fixed="item.fixed"
				>
					<template #header>
						<el-tooltip class="box-item" effect="dark" content="排序值较大的将显示在前。点击可修改排序值" placement="top">
							<div style="display: flex; align-items: center">
								排序值
								<el-icon style="font-size: 14px" class="el-icon--right">
									<QuestionFilled />
								</el-icon>
							</div>
						</el-tooltip>
					</template>
					<template #default="scope">
						<FormItem
							:show-message="false"
							v-if="
								inlineEditProps.open &&
								inlineEditProps.targetId === scope.row.id &&
								(!inlineEditProps.focusOnSpecial || inlineEditProps.focusOnSpecial === 'sortNum')
							"
							:config="
								item.inlineEdit ?? {
									placeholder: '',
									type: 'number',
									name: 'sortNum'
								}
							"
						>
							<template #[item.inlineEdit!.name!.toString()]>
								<slot :name="item.inlineEdit!.name" :formData="inlineEditProps.data"></slot>
							</template>
						</FormItem>
						<slot v-else :name="item.slotName" :row="scope.row">
							<div @click="enterInlineEditMode(scope.row, item, 'sortNum')" style="width: 100%; cursor: pointer">
								<text v-if="item.prop && (item.showType ?? 'text') === 'text'">
									<text v-if="!_.isEmpty(scope.row[item.prop]) || _.isNumber(scope.row[item.prop])">{{
										scope.row[item.prop]
									}}</text>
									<text v-else class="no-data-placeholder">-</text>
								</text>
								<text v-else-if="item.prop && (item.showType === 'date' || item.showType === 'time')">
									<text v-if="!_.isEmpty(reformTimeString(item.showType, scope.row[item.prop]))">{{
										reformTimeString(item.showType, scope.row[item.prop])
									}}</text>
									<text v-else class="no-data-placeholder">-</text>
								</text>
							</div>
						</slot>
						<!-- 否则为"none"即不显示 -->
					</template>
				</el-table-column>
			</template>
		</el-table>

		<!--行内编辑状态下，底部新增一块添加数据用的table-->
		<el-table
			v-if="canShowInlineAddTable && (tableConfig?.inlineInsertArea ?? 'bottom') === 'bottom'"
			class="meta-table"
			ref="editTable"
			:border="true"
			:show-header="canShowSubTableLeaf"
			:data="inlineEditProps.open && !inlineEditProps.targetId ? inlineEditData : []"
			:select-on-indeterminate="tableConfig.selectType !== 'single'"
			:row-key="tableConfig.selectId ?? 'uuid'"
			:tree-props="{ children: tableConfig.childrenKey ?? 'children' }"
			@selection-change="selectionChange"
			@sort-change="handleSortChange"
			:header-cell-class-name="handleClassName"
			:header-cell-style="calcHeadCellStyle"
			:cell-style="calcCellStyle"
			:default-expand-all="tableConfig.defaultExpandAll ?? true"
		>
			<template #empty>
				<div
					v-if="canShowInlineAdd"
					style="
						display: flex;
						align-items: center;
						justify-content: center;
						width: 100%;
						height: 100%;
						font-size: 14px;
						cursor: pointer;
					"
					class="inline-add-block"
					@click="enterInlineMode"
				>
					<el-icon class="el-icon--left">
						<Plus />
					</el-icon>
					{{ tableConfig.inlineAddText }}
				</div>
			</template>

			<el-table-column
				v-if="tableConfig.selectType === 'multi' || tableConfig.selectType === 'single'"
				type="selection"
				:reserve-selection="true"
				fixed="left"
			>
			</el-table-column>
			<template v-for="(item, index) in showColumns" :key="item.key ? item.key : index">
				<el-table-column
					v-if="item.isShow !== false && !inlineEditProps.targetId && item.special !== 'sortNum' && checkVisible(item.visible)"
					:prop="item.prop"
					:label="item.label"
					:width="item.width"
					:min-width="item.minWidth"
					:sortable="item.sortable ? 'custom' : false"
					:sort-by="item.sortAttr"
					:show-overflow-tooltip="Boolean(item.prop)"
					:resizable="tableConfig.resizable ?? true"
					:fixed="item.fixed"
				>
					<template #default>
						<FormItem :show-message="false" v-if="item.inlineEdit" :config="item.inlineEdit">
							<template #[item.inlineEdit.name!.toString()]>
								<slot :name="item.inlineEdit.name" :formData="inlineEditProps.data"></slot>
							</template>
						</FormItem>
						<div v-else>
							<div v-if="item.slotName === 'opr'">
								<el-link style="margin-right: 12px" :underline="false" type="primary" @click="inlineSave">保存</el-link>
								<el-link style="margin-right: 12px" :underline="false" type="primary" @click="inlineCancel">取消</el-link>
							</div>
							<div v-else class="text-color-hint">-</div>
						</div>
					</template>
				</el-table-column>

				<!-- 特殊列sort: 可在表格内点击编辑 -->
				<el-table-column
					v-if="item.isShow !== false && item.special === 'sortNum' && checkVisible(item.visible)"
					:resizable="tableConfig.resizable ?? true"
					:prop="item.prop"
					:label="item.label"
					:width="item.width"
					:min-width="item.minWidth"
					:sortable="item.sortable ? 'custom' : false"
					:sort-by="item.sortAttr"
					:show-overflow-tooltip="Boolean(item.prop)"
					:fixed="item.fixed"
				>
					<template #header>
						<el-tooltip class="box-item" effect="dark" content="排序值较大的将显示在前。点击可修改排序值" placement="top">
							<div style="display: flex; align-items: center">
								排序值
								<el-icon style="font-size: 14px" class="el-icon--right">
									<QuestionFilled />
								</el-icon>
							</div>
						</el-tooltip>
					</template>
					<template #default="scope">
						<FormItem
							:show-message="false"
							v-if="
								inlineEditProps.open &&
								inlineEditProps.targetId === scope.row.id &&
								(!inlineEditProps.focusOnSpecial || inlineEditProps.focusOnSpecial === 'sortNum')
							"
							:config="
								item.inlineEdit ?? {
									placeholder: '',
									type: 'number',
									name: 'sortNum'
								}
							"
						>
							<template #[item.inlineEdit!.name!.toString()]>
								<slot :name="item.inlineEdit!.name" :formData="inlineEditProps.data"></slot>
							</template>
						</FormItem>
						<slot v-else :name="item.slotName" :row="scope.row">
							<div @click="enterInlineEditMode(scope.row, item, 'sortNum')" style="width: 100%; cursor: pointer">
								<text v-if="item.prop && (item.showType ?? 'text') === 'text'">
									<text v-if="!_.isEmpty(scope.row[item.prop]) || _.isNumber(scope.row[item.prop])">{{
										scope.row[item.prop]
									}}</text>
									<text v-else class="no-data-placeholder">-</text>
								</text>
								<text v-else-if="item.prop && (item.showType === 'date' || item.showType === 'time')">
									<text v-if="!_.isEmpty(reformTimeString(item.showType, scope.row[item.prop]))">{{
										reformTimeString(item.showType, scope.row[item.prop])
									}}</text>
									<text v-else class="no-data-placeholder">-</text>
								</text>
							</div>
						</slot>
						<!-- 否则为"none"即不显示 -->
					</template>
				</el-table-column>
			</template>
		</el-table>

		<Pagination
			style="margin-top: 8px"
			v-if="tableConfig.pagination"
			:pageable="pageable"
			:handleSizeChange="handleSizeChange"
			:handleCurrentChange="handleCurrentChange"
			:class="{ disabled: disabled || inlineEditProps.open }"
		/>
	</div>
</template>

<script setup lang="ts">
import { ref, PropType, watch, defineEmits, inject, computed, provide, onMounted } from "vue";
import { useSelection } from "../hooks/useSelection";
import { TableConfig, ColumnProps, InlineEditPropsOption } from "../interface";
import Pagination from "./Pagination.vue";
import { useTable } from "../hooks/useTable";
import HeadConfig from "./HeadConfig.vue";
import FormItem from "../../MetaForm/form-items/index.vue";
import { ElMessage } from "element-plus";
import { reformTimeString } from "../../MetaUtils/FormUtils";
import _ from "lodash";

/**行内操作相关数据 */
let inlineEditProps: InlineEditPropsOption = inject("inlineEditProps")!;
provide("formData", inlineEditProps!.data);

// 表头ref
let tableHeader = ref();

// 通过hook处理选项变化

const emit = defineEmits(["onSelectionChange"]);

const table = ref();
const editTable = ref();

const props = defineProps({
	tableConfig: {
		type: Object as PropType<TableConfig>,
		required: true
	},
	/**表格是否有标签页。会影响表格初始获取数据的时机 */
	containTabs: {
		type: Boolean,
		default: false
	},
	/**disable */
	disabled: { type: Boolean, default: false }
});

/**表格展示用的列 */
let showColumns = ref([] as ColumnProps[]);

function handleChangeHeadConfig(newConfig: ColumnProps[]) {
	showColumns.value = _.cloneDeep(newConfig);
}

/**校验每一列是否visible */
function checkVisible(visibleFunction: undefined | ((param?: any) => boolean)) {
	if (!visibleFunction) return true;
	return visibleFunction();
}

/**是否展示行内编辑的添加按钮 */
let canShowInlineAdd = computed(() => {
	//未设置行内编辑模式
	if (!props.tableConfig.canInlineEdit) return false;
	//如果设置了不显示新增按钮，则不显示
	if (props.tableConfig.showInlineAddBtn === false) return false;
	//正在行内编辑，直接false
	if (inlineEditProps.open) return false;
	return true;
});

// 非行内编辑、 行内编辑底部新增模式、行内编辑顶部新增模式且未展示新增表格 展示header
let showMainTableHeader = computed(() => {
	return (
		!props.tableConfig?.canInlineEdit ||
		(props.tableConfig?.inlineInsertArea ?? "bottom") === "bottom" ||
		(props.tableConfig.inlineInsertArea === "top" && !canShowInlineAddTable.value)
	);
});

/**是否展示新增表格 */
let canShowInlineAddTable = computed(() => {
	//未设置行内编辑模式，直接false
	if (!props.tableConfig.canInlineEdit) return false;
	//如果不显示新增按钮，则若当前没有打开行内编辑模式时，不显示新增表格;但若当前主表格无数据，则需展示新增表格，用于显示“暂无数据”一行
	if (props.tableConfig.showInlineAddBtn === false && !inlineEditProps.open && tableData.value.length !== 0) return false;
	//可行内编辑时，仅在编辑状态且非编辑自己时不展示
	if (inlineEditProps.open && inlineEditProps.targetId) return false;
	//其余均展示
	return true;
});

/**是否展示主表格（行内编辑模式下，主表无数据时，需要由副表显示表头和添加数据行。此时主表不显示） */
let canShowMainTable = computed(() => {
	//未设置行内编辑模式，永远true
	if (!props.tableConfig.canInlineEdit) return true;
	if (tableData.value.length > 0) return true;
	return false;
});

/**副表的表头是否显示 */
let canShowSubTableLeaf = computed(() => {
	return !canShowMainTable.value;
});

/**
 * 根据参数，判断主表的单元格应显示什么内容
 * @param rowData 当前行的数据
 * @param item 当前列的配置项
 * @return "data"：表格本身数据展示 | "form-item"：行内输入组件 | "special": 特殊组件，目前仅sort | "common-opr"：表格原本的操作 | "editmode-opr"：编辑模式下对应行的保存删除操作 | "none"：不展示
 */
function checkCellConentMode(
	rowData: any,
	item: ColumnProps
): "data" | "form-item" | "special" | "common-opr" | "editmode-opr" | "none" {
	// 序号列 不展示data
	if (item.type === "index") {
		return "none";
	}
	if (item.special) return "special";
	// 无inlineEdit，或者本列未设置过inlineEdit配置，则根据slotName判断，显示data或普通opr|
	if (!inlineEditProps?.open) return item.slotName === "opr" ? "common-opr" : "data";
	// item本身未设置inlineEdit配置，且不为opr，则显示data
	if (!item.inlineEdit && item.slotName !== "opr") return "data";

	// inlineEdit模式，且本列配置了inlineEdit配置项
	// 针对opr，未开启行内编辑时只显示commonopr；编辑本行时显示保存取消按钮，其他状态（编辑非本行或新建时）行不显示；
	if (item.slotName === "opr") {
		if (!inlineEditProps?.open) return "common-opr";
		return inlineEditProps?.targetId === rowData.id ? "editmode-opr" : "none";
	}

	//针对非opr列，编辑本行,且非focus模式时，显示form-item
	if (inlineEditProps?.targetId === rowData.id && !inlineEditProps?.focusOnSpecial) return "form-item";
	//否则显示data
	return "data";
}

/**进入inlineEdit模式 */
function enterInlineEditMode(rowData: any, item: ColumnProps, focus?: "sortNum") {
	console.log(rowData, "rowData");
	inlineEditProps.open = true;
	inlineEditProps.targetId = rowData.id;
	if (focus) inlineEditProps.focusOnSpecial = focus;
	for (let key in inlineEditProps.data) {
		delete inlineEditProps.data![key];
	}
	for (let key in rowData) {
		inlineEditProps.data[key] = rowData[key];
	}
}

/**表格各项数据匹配所使用的id字段。默认uuid */
let theId = props.tableConfig.selectId ?? "uuid";

const { selectionChange, selectedListIds, selectedList, _setSelectionList } = useSelection(
	theId,
	props.tableConfig.selectType,

	table,
	props.tableConfig.isSelectItem ?? false
);

// 表格操作 Hooks 暂时去掉了search和reset
const { tableData, pageable, totalParam, getTableList, handleSizeChange, search, handleCurrentChange, handleSortChange, reset } =
	useTable(
		props.tableConfig.requestApi,
		props.tableConfig.staticParam,
		props.tableConfig.initParam,
		props.tableConfig.pagination,
		props.tableConfig.pageNum,
		props.tableConfig.pageSize,
		props.tableConfig.dataCallback,
		props.tableConfig.callBack,
		props.tableConfig.listKey ?? "list",
		props.containTabs
	);

/**行内编辑的data */
let inlineEditData = computed(() => {
	let obj = {} as { [key: string]: any };
	for (let key in tableData.value[0] as { [key: string]: any }) {
		obj[key] = undefined;
	}
	return [obj];
});

// /**
//  * 处理单选变化
//  * 【注意】element-table的单选选项变化的方法即为currentChange，与分页组件中的currentchange容易混淆
//	* 后续实现单选变化时，注意不要重复使用该词
//  */
// function handleCurrentChange(e: any) {
// 	console.log("current change:", e);
// }

/** 监听selection变化，变化后通知布局组件,顶部tab部分可能会因此变化 */
watch(
	() => selectedListIds,
	newValue => {
		emit("onSelectionChange", newValue);
	},
	{ deep: true }
);

/**清空selection操作 */
function clearSelection() {
	table.value.clearSelection();
}

/**为排序的列设置表头高亮 */
function handleClassName(e: any) {
	let { column } = e;
	if (column.order === "descending" || column.order === "ascending") {
		return "highlightCell";
	}
}

/**mini模式下，设置单元格高度为40 */
function calcCellStyle() {
	if (props.tableConfig.mode === "mini") {
		return {
			padding: "0px !important",
			"min-height": "40px!important"
		};
	}
}
function calcHeadCellStyle() {
	if (props.tableConfig.mode === "mini") {
		return {
			padding: "0px !important"
		};
	}
}

/**强行手动修改tableData */
function setTableData(data: any) {
	tableData.value = JSON.parse(JSON.stringify(data));
}

/**进入inlineEdit模式 */
function enterInlineMode() {
	if (props.tableConfig.beforeEnterInlineMode && !props.tableConfig.beforeEnterInlineMode(inlineEditProps)) return;
	inlineEditProps.targetId = undefined;
	inlineEditProps.open = true;

	// for (let key in inlineEditProps.data) {
	// 	delete inlineEditProps.data![key];
	// }
}

/**行内编辑取消 */
function inlineCancel() {
	inlineEditProps.open = false;
	clearInlineEditProps();
}

/**清空inlineEditProps的所有参数，防止数据遗留 */
function clearInlineEditProps() {
	for (let key in inlineEditProps.data) {
		delete inlineEditProps.data![key];
	}
	inlineEditProps.targetId = undefined;
	inlineEditProps.focusOnSpecial = undefined;
}

/**行内编辑保存 */
async function inlineSave() {
	let params = props.tableConfig.inlineStaticParams
		? Object.assign(inlineEditProps.data, props.tableConfig.inlineStaticParams!)
		: inlineEditProps.data;

	//根据参数进行校验
	if (!props.tableConfig.inlineSubmitValidator) {
		console.error("未设置行内编辑的校验方法");
	} else {
		let { success, msg } = props.tableConfig.inlineSubmitValidator!(params);
		if (!success) {
			ElMessage.warning(msg);
			return;
		}
	}

	if (!inlineEditProps.targetId) {
		//添加成功后，关闭行内编辑模式，刷新列表
		let res = await props.tableConfig.inlineAddApi!(params);
		if (res?.code !== 0) return;
		inlineEditProps.open = false;
		clearInlineEditProps();
		search();
	} else {
		//若为编辑sort，则使用sort修改API
		let theApi = props.tableConfig.inlineEditApi!;
		let theParam = params;
		if (inlineEditProps.focusOnSpecial === "sortNum" && props.tableConfig.sortApi) {
			theApi = props.tableConfig.sortApi;
			theParam = { uuid: params.uuid, sortNum: Number(params.sortNum.toFixed(0)) ?? 0 };
		}
		let res = await theApi(theParam);
		if (res?.code !== 0) return;
		inlineEditProps.open = false;
		clearInlineEditProps();
		search();
	}
}

/**标签栏切换的时候 就刷新headConfig数据 */
function headConfigRefresh() {
	tableHeader.value.reset();
}

function setSelectionList(datas: any[]) {
	for (let data of datas) {
		table.value.toggleRowSelection(data, true);
	}
	_setSelectionList(datas);
}

defineExpose({
	clearSelection,
	setSelectionList,
	getTableList,
	search,
	setTableData,
	enterInlineMode,
	headConfigRefresh,
	reset,
	tableData,
	totalParam,
	tableHeader,
	selectedList,
	pageable,
	table,
	showColumns
});

onMounted(() => {});
</script>

<style lang="scss">
.minTableHead {
	padding: 0 !important;
}
.table-inner-container {
	position: relative;
	height: fit-content;
	max-height: 100%;
	overflow: auto;
}
.highlightCell {
	background-color: var(--el-color-primary-light-9) !important;
}

// 表格内的表单不需要margin-bottom
.meta-table {
	position: relative;
	max-height: calc(100% - 40px);
	.el-form-item--default {
		margin-bottom: 0 !important;
	}
	.el-form-item {
		margin-bottom: 0 !important;
	}

	// text-area 增加上下边距
	.el-form-item__content .el-textarea {
		margin-top: 8px;
		margin-bottom: 8px;
	}
}
.meta-table-1 {
	// 将表格空状态下的高度设为335px（原本未设置，自动缩为单行数据的高度）
	.el-table__empty-block {
		height: fit-content !important;
	}
}
.meta-table-without-pagination {
	max-height: 100% !important;
}
.config-icon {
	position: absolute;
}
.disabled {
	pointer-events: none;
	opacity: 0.45;
}
.inline-add-block:hover {
	background-color: var(--el-fill-color-light);
}
.no-data-placeholder {
	color: $color-text-hint;
}
</style>
