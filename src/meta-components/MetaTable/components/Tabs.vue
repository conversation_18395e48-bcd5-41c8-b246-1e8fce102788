<!--
  表格页面内使用的标签页切换组件
  已在layout组件内使用。请勿修改本组件

  version: 1.0.0
  date: 2022-10-12 15:50:55
  auth: Andrew
-->

<template>
	<div class="table-tabs">
		<el-tabs v-model="activeName" @tab-click="handleClick" :before-leave="handleBeforeLeave">
			<el-tab-pane v-for="(item, index) of showtabs" :key="index" :label="item.label" :name="item.label">
				<template #label>
					{{ item.label }}
					<!-- tab红点 -->
					<el-badge
						v-if="item.badge && getBadgeCount(item.badge) !== 0"
						:value="getBadgeCount(item.badge)"
						:is-dot="item.badge.isDot"
						class="badge-item"
						style="margin-left: 8px"
					></el-badge>
				</template>
			</el-tab-pane>
			<el-tab-pane v-if="moretabs.length > 0" name="more">
				<template #label>
					<el-dropdown @command="handleCommand" style="align-items: center; height: 100%">
						<span
							class="el-dropdown-link"
							:class="activeName === 'more' ? 'tabSelected' : 'tabUnselected'"
							style="margin-bottom: 2px; line-height: 100%"
						>
							{{ moreTabText }}
							<el-icon class="el-icon--right">
								<arrow-down />
							</el-icon>
						</span>
						<template #dropdown>
							<el-dropdown-menu>
								<el-dropdown-item
									style="height: 32px"
									v-for="(item, index) of moretabs"
									:key="index"
									:command="index"
									:class="selectedCommand === index ? 'tabSelected' : 'tabUnselected'"
								>
									{{ item.label }}
								</el-dropdown-item>
							</el-dropdown-menu>
						</template>
					</el-dropdown>
				</template>
			</el-tab-pane>
		</el-tabs>
	</div>
</template>

<script setup lang="ts">
import { ref, defineProps, computed, reactive, nextTick, PropType, onMounted } from "vue";
import type { TabsPaneContext } from "element-plus";
import { ArrowDown } from "@element-plus/icons-vue";
import { TabsConfig } from "../interface";
/**定义接收参数 */
const props = defineProps({
	tabsConfig: { type: Object as PropType<TabsConfig>, required: true }
});
/** 过滤隐藏的tab */
const visibleTabList = props.tabsConfig?.tabList.filter(e => e.visible !== false);

/**emit方法 */
const emit = defineEmits(["onTabChange", "onTabInit"]);

/**选中的标签页名称 */
const activeName = ref(props.tabsConfig ? visibleTabList[0].label : "");

/**被选中的标签页的信息 */
let selectedTabInfo = reactive({});
function getSelectedTabInfo() {
	return selectedTabInfo;
}

/** 设置选中项 */
function setCurrentTab(label: string) {
	let foundTabIndex = -1;
	const foundTab = visibleTabList?.find((e, index) => {
		foundTabIndex = index;
		return e.label === label;
	});
	if (foundTab) {
		activeName.value = foundTab.label;
		emit("onTabChange", { name: foundTab.label, value: foundTab.value, index: foundTabIndex });
	} else {
		console.warn("cannot find tab");
	}
}
defineExpose({ getSelectedTabInfo, setCurrentTab });

/**
 * 下拉菜单被选中的command
 */
let selectedCommand = ref<number | undefined>(undefined);

/**
 * 默认显示在外的标签的列表
 */
let showtabs = computed(() => {
	if (visibleTabList) {
		return visibleTabList.slice(0, props.tabsConfig.showCount);
	} else {
		return [];
	}
});
/**
 * 被包含在更多按钮内的标签的列表
 */
let moretabs = computed(() => {
	if (visibleTabList) {
		return visibleTabList.slice(props.tabsConfig.showCount);
	} else {
		return [];
	}
});
/**
 * 更多标签的显示文字
 */
let moreTabText = computed(() => {
	return selectedCommand.value !== undefined ? moretabs.value[selectedCommand.value].label : "更多";
});
/** 获取红点数量 */
function getBadgeCount(badge?: { count: number | (() => number); isDot?: boolean }) {
	if (!badge) return 0;
	const count = badge.count;
	return typeof count === "function" ? count() : count;
}
/**默认展示的标签页被点击时 */
const handleClick = (tab: TabsPaneContext) => {
	//index在count之内，提交
	if (tab.index && tab.paneName && parseInt(tab.index) < props.tabsConfig.showCount) {
		selectedTabInfo = {
			index: parseInt(tab.index),
			value: visibleTabList[parseInt(tab.index)].value,
			name: tab.paneName.toString()
		};
		emit("onTabChange", selectedTabInfo);
	}
};

/**
 * 选择下拉菜单内的标签页时
 * @param command 下拉菜单候选项的index
 */
function handleCommand(command: number) {
	activeName.value = ""; //保证更多标签内，文字长度不同时，高亮划线自动改变宽度
	nextTick(() => {
		activeName.value = "more"; //标签页组件，光标移至更多一栏
		selectedCommand.value = command; //标记目前选中的选项
		selectedTabInfo = {
			index: props.tabsConfig.showCount + command,
			value: moretabs.value[selectedCommand.value].value,
			name: moretabs.value[selectedCommand.value]
		};
		emit("onTabChange", selectedTabInfo);
	});
}

/**
 * 处理标签页点击事件
 * @param name 新选择的标签页的名称
 * @param oldname 离开的标签页的名称
 */
function handleBeforeLeave(name: string, oldname: string) {
	if (oldname === "more") {
		selectedCommand.value = undefined;
	}
	//若手动点击“更多”按钮，则阻止切换（因为需要悬浮后从下拉菜单中进行选择）
	if (name === "more" && selectedCommand.value === undefined) {
		return false;
	}
}

/**加载完毕后，自动触发一次tabChange，修改表格组件中的staticParam */
onMounted(() => {
	if (props.tabsConfig) {
		selectedTabInfo = {
			index: 0,
			value: visibleTabList[0].value,
			name: visibleTabList[0].label
		};
		emit("onTabInit", selectedTabInfo);
	}
});
</script>

<style lang="scss">
.tabSelected {
	color: $color-primary !important;
}
.tabUnselected {
	color: $color-text-primary;
}
.table-tabs {
	.el-tabs {
		--el-tabs-header-height: 54px !important;
	}
	.el-tabs__nav-wrap {
		padding: 0 20px;
	}
	.el-tabs__nav-wrap::after {
		height: 0;
	}
}
</style>
