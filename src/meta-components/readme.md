# 日志

## 当前总版本：v 0.7.84

## 最新更新日期：2025/6/26

## 更新日志

### 0.7.84 2025/6/26
- MetaForm - SingleSelect appendOptionToFormDataWithKey修改
  - 修复 appendOptionToFormDataWithKey 编辑时回显将remoteLabelKey清除 导致label回显异常问题

### 0.7.83 2025/6/19
- MetaForm - submitDisabled
  - expose 新增 submitDisabled 属性 用于将提交按钮的禁用状态传递给表单实例

### 0.7.82 2025/6/18
- MetaTable-Tabs
  - MetaTable Tabs TabList 新增 badge 属性
  - 支持tab 显示红点

### 0.7.81 2025/6/16
- MetaForm-SingleSelect
  - MetaForm SingleSelect appendLabelToFormDataWithKey 修改
  - appendLabelToFormDataWithKey回显label到表单 现支持多选 

### 0.7.80 2025/6/09
- MetaTable-ColumnProps
  - 添加 MetaTable ColumnProps 列配置项type, 支持展示表格序号index

### 0.7.79 2025/5/30
- MetaTable-Tabs
  - 添加 Tabs TabList项visible属性 , 用于判断tab权限显示隐藏

### 0.7.78 2025/5/28 
- MetaForm-FileUpload
  - 添加 filePreviewMap url映射 , 用于特殊情况时(可下载url与formData中url不同)支持文件下载 

### 0.7.78 2025/5/23 
- MetaForm-FileUpload
  - 添加 pathName 文件夹名，用于 OSS 归类文件

### 0.7.77 2025/5/23 
- MetaForm
  - 新增 otherArr 输入框配置项 rows(文本域默认行数)

### 0.7.76 2025/2/11 from 大船师
- MetaForm
  - 新增 returnFile 配置项 用于FileUpload表单项value返回值 增加File对象
- SinglePage
  - 新增 type="sheet"类型 兼容luckySheet正常显示
  
#### 0.7.75 2024/11/28
- MetaForm - SingleSelect
  - 修复 options失去响应式 导致下拉框候选项更新失败的问题

#### 0.7.74 2024/11/08
- MetaForm
  - 增加FormItem-Date类型 reformDateRange方法的判断，支持formData-reactive类型

#### 0.7.73 2024/10/22

- MetaTable
  - 修复必须配置 isExport 才能展示导出按钮的 BUG
  - 修复必须多选数据才能导出的 BUG
  - 新增根据表头配置进行导出的功能
- MetaForm
  - 修复行内编辑时错误展示了 label 的问题

#### 0.7.72 2024/10/16

- MetaTable
  - 大船师专用，因为调整了表格整体行高，因此将右上角表头配置按钮缩小了

#### 0.7.71 2024/10/8

- MetaForm
  - el-upload 增加下载组件，用于下载图片，默认禁用

#### 0.7.70 2024/9/30

- MetaTable
  - el-table
    - row-key 修改为 :row-key="tableConfig.selectId ?? 'uuid'"，动态指定唯一键，获取 tableConfig.selectId 的值

#### 0.7.69 2024/9/29

- MetaTable
  - exportToXlsx 增加了 tableHeaderArray 数组，用于双表头的显示

#### 0.7.68 2024/9/27

- MetaTable
  - ExportBtn 增加了 exportConfigList 接口，用于多个导出按钮的使用

#### 0.7.67 2024/9/25

- MetaTable
  - header.table-header 增加了 tips 插槽，用于显示标题后的提示信息或其他内容

#### 0.7.66 2024/9/24

- MetaForm-ImageUpload
  - 添加 pathName prop，文件夹名，用于 OSS 归类文件
- MetaForm-RichEditor
  - 添加 pathName prop，文件夹名，用于 OSS 归类文件

#### 0.7.65 2024/9/23

- MetaTable
- MetaForm
  -dateRange

  -解决 dateRange 组件以函数形式动态传入 placeholder 时缺少尾端的'开始''截止'文字问题

#### 0.7.64 2024/9/18

- MetaTable
  - SelectionPreview 增加了 total 插槽，用于选择后显示所选项的总数或其他数据

### 0.7.63 2024/9/18

- MetaForm
  - FormItems
    - ImageUpload 中 EditOptions 增加了 isPrivate 属性 ，用于判断图片编辑中是否需要加密

#### 0.7.62 2024/9/14

- MetaTable
  - SingleSelectPro 改为输入框为空时不调取接口查询

#### 0.7.61 2024/9/14

- MetaForm
  - FormItems
    - ImageUpload 增加了 chunk 字段，用于开启分片上传，默认为 false

#### 0.7.60 2024/9/12

- MetaTable
  - ExportConfig 增加了 buttonText isExport 导出按钮文字及是否显示导出 类型，用于限制导出按钮的显示

#### 0.7.59 2024/9/9

- MetaForm
  - FormItems
    - ImageUpload 增加了 accept 类型，用于限制文件上传类型

#### 0.7.58 2024/9/2

- MetaTable-hooks-useTable
- tabs 切换时，筛选项参数需根据是否可见判断是否传参

#### 0.7.57 2024/8/27

- MetaForm-styleConfig 的 label-position
  - 修改 label 的配置项逻辑错误

#### 0.7.56 2024/8/21

- MetaForm
  -FormItems
  -Number 数字输入组件,增加严格步进如果 stepStrictly 这个属性被设置为 true，则只能输入步进(step)的倍数

#### 0.7.55 2024/8/12

- MetaTable-ColumnProps 列配置项
  - 每一列的 key 值，可选，不填时为 index，避免组件库多个 tab 切换排序时造成 sort-by 为 undefined 情况
- MetaTable-hooks-useTable
  - 0.7.50“按照列排序时，排序字段改为从 columns 配置项中拿，避免排序时为 undefined”遇到的问题，通过上面修改可以解决且不会产生新的 bug,所以将 0.7.50“按照列排序时，排序字段改为从 columns 配置项中拿，避免排序时为 undefined”做的修改移除
- MetaForm-FormItems-imgUpload(imageUpload)
  - 避免上传四个文件，删掉一个，再上传时提示最多上传四个。内部 el-image 组件有 limit 限制，所以在删除的时候要把组件的上传列表中对应的数据删除掉

#### 0.7.54 2024/8/8

- MetaForm
  -ImageEditor 图片裁剪组件,增加传入图片地址为响应式数据时的处理(get-oss-image 方法返回的数据)

#### 0.7.53 2024/8/8

- MetaTable-inlineEditProps inlineSave
  - 排序值四舍五入，防止输入小数点时报接口错误(排序值只能为整数)

#### 0.7.52 2024/8/7

- MetaTable
  -FilterSingleSelectPro 改为下拉框收起时失去焦点

#### 0.7.51 2024/8/6

- MetaTable-FilterCascader

  -解决页面缓存回显问题

#### 0.7.50 2024/8/5

- MetaTable-hooks-useTable

  - 按照列排序时，排序字段改为从 columns 配置项中拿，避免排序时为 undefined
  - FilterSingleSelectPro 选择后失去焦点，避免选择后再次点击无下拉框出现

  #### 0.7.49 2024/8/1

- MetaForm-FormItems-FileUpload
  - 增加 ImageOptions 中 accept 属性配置 用于限制文件上传类型
- MetaDetail
  - 判断 label 为空时 不显示 label 插槽

#### 0.7.48 2024/7/29

- MetaForm-ImageEidtor, MetaForm-Uploader, MetaForm-UploadImg, MetaForm-WangEditor

  - OSS 文件上传请求头添加 Content-Type: application/octet-stream，二进制流，避免 Safari 浏览器下载 ai 文件时添加.ps 后缀

  #### 0.7.47 2024/7/23

- MetaDetail
  - 增加 label 右侧插槽位 用于放置 icon 展示信息。

#### 0.7.46 2024/7/22

- MetaForm-ImageUpload
  - 解决未传入校验规则时的报错问题

#### 0.7.45 2024/7/19

- MetaTable-FilterItems
  - FilterSingleSelectPro 解决组件切换后回显问题

#### 0.7.44 2024/7/17

- MetaTable-FilterItems
  - FilterSingleSelectPro 组件添加 automatic-dropdown 属性

#### 0.7.43 2024/7/16

- MetaTable-FilterItems
  - 添加 FilterSingleSelectPro 组件,在原来 FilterSingleSelect 组件基础上添加远程搜索

#### 0.7.42 2024/7/16

- MetaTable
  - 优化 Tabs,和 SubTabs 的数据初始化和 initParam 同时存在时，和 TabChange 事件分离，不进行 search

#### 0.7.41 2024/7/15

- MetaTable-NumberInput
  - 防止数字输入框出现 e + 等数字导致报错, 修改数字输入框最大值为 100000

#### 0.7.40 2024/7/11

- MetaForm-Input
  - 防止数字框出现 NaN，一旦出现便清空输入框

#### 0.7.39 2024/7/9

- MetaForm-Cascader
  - 点击整行文字也能选择
- MetaTable-FilterCascader
  - 点击整行文字也能选择

#### 0.7.38 2024/7/8

- MetaForm-Date
  - 解决组件 daterange 方法 reformDateRange 空值报错问题

#### 0.7.37 2024/7/8

- MetaForm
  - 提交成功后的提交按钮的 isSubmitLoading 延迟 1s 取消，防止弹窗关闭动画完成前用户又点击确定提交按钮导致重复提交

#### 0.7.36 2024/7/1

- MetaForm-RichEditor
  - 解决组件库若没有配置 rule 判断校验出错

#### 0.7.35 2024/6/30

- MetaTable-FilterCascader
  - 解决级联选择器多选情况下将展示框撑开很高，和搜索时点击清空按钮不能清空的问题

#### 0.7.34 2024/6/29

- MetaForm-FormItems-ImageUpload
  - 添加 id 配置，解决表单两个 signal 上传图片编辑异常问题

#### 0.7.33 2024/6/25

- MetaForm
  - 添加表单项 label 的 slot
  - 增加使用表单可以自定义表单项 label 的灵活性 例如 表单项 name="name"则 label 的 slot 为 nameLabel

#### 0.7.32 2024/6/19

- MetaTable
  - table-config 新加规则：showTabsAndSelected，可自定义是否同时显示 tabs 和已选择的结果数。
- MetaTable
  - tabs 根据 showTabsAndSelected 增加是否显示的判断。

#### 0.7.31 2024/6/17

- MetaForm-FormItems
  - 给 ImageUpload 自定义事件 check-validate 并绑定存在的 form-item 校验方法 reValidate
- MetaForm-FormItems-ImageUpload
  - 根据校验规则进行 change 校验
- MetaForm-FormItems-RichEditor
  - 根据校验规则进行 blur 校验

#### 0.7.30 2024/6/13

- MetaForm
  - 提供校验规则 provide ruleForm
- MetaForm-FormItems
  - 给 RichEditor 自定义事件 re-validate 并绑定存在的 form-item 校验方法 reValidate
- MetaForm-FormItems-RichEditor
  - 根据校验规则进行 change 校验

#### 0.7.29 2024/6/13

- MetaTable
- 修复配置项 initParam 失效问题, 设置异步确保 filterResult 不会被清空

#### 0.7.28 2024/5/29

- MetaTable
  - 修复表格筛选项, 超出一行且部分筛选项隐藏状态下的样式问题
  - 修复表格切换分页时, 筛选组件的 visible 方法失效,导致筛选项未正确显示隐藏问题

#### 0.7.27 2024/5/23

- MetaTable
  - 修复 MetaTable 的导出存在子节点时，导出内容存在 NaN

#### 0.7.26 2024/5/23

- MetaForm
  - 修复 input 输入框失焦时精确为整数失效

#### 0.7.25 2024/5/23

- MetaForm
  - 修复快速点击确定按钮，导致多次发送相同请求

#### 0.7.24 2024/5/20

- MetaUtils
  - 变更记录从 changelog 结构体中解析数据，输出文本如果目标 key 查询不到则返回空

#### 0.7.23 2024/4/29

- MetaForm
  - input-textarea 最小行数从 3 行改为 5 行

#### 0.7.22 2024/4/28

- MetaTable
  - 导出 reset 方法

#### 0.7.21 2024/4/3

- MetaForm
  - 修复 input 数字输入框，在设置最大值时，输入框显示的值不正确的问题，因为 max 属性 inpt 用来设置最大长度了
  - 改为使用 maxNum 和 minNum 设置 input 数字输入框最大值和最小值

#### 0.7.20 2024/4/2

- MetaForm
  - 增加 input 可以使用 max，min 设置最大值，最小值功能

#### 0.7.19 2024/3/29

- MetaHooks
  - usePageMemory 新增缓存导航栏数据
- MetaTable-SubTabs
  - 新增 setCurrentTab 函数，用于父组件初始化导航栏位置
  - 新增 isManualSet 变量，用来判断是否是手动设置导航栏位置，如果是就不需要重置导航栏
- MetaTable
  - 新增 initTab 函数，用来通过 tableConfig.initParam 来设置导航栏（一级、二级）初始位置

#### 0.7.18 2024/3/21

- MetaTable
  - 增加表格是否可以导出子节点，默认是导出子节点
  - 如果不导出子节点,示例：exportChildren：false

#### 0.7.17 2024/3/21

- MetaTable
  - 增加了表格筛选项级联选择器可以快捷地搜索选项
  - 示例：otherAttr: {
    props: {
    filterable: true
    }
    }

#### 0.7.16 2024/3/20

- MetaTable
  - 增加了表格筛选项级联选择器可配置选择任意一级选项
  - 示例：otherAttr: {
    props: {
    checkStrictly: true
    }
    }

#### 0.7.15 2024/3/6

- MetaForm
  - 修复了 FileUpload 组件上传文件后没有重新校验表单项的 bug

#### 0.7.14 2024/2/21

- MetaTable
  - useTable 钩子函数中新增读取项目中 metaconfig 文件中的配置项来获取并设置默认值
- MetaConfig
  - 使用方式：
  - 1. 跟 meta-components 同级目录，新建 meta-config 文件夹并新建 index.ts 文件
  - 2. 导出默认对象，该对象中属性名跟组件名挂钩，比如 metaTable 组件对应属性名就叫 tableDefaultConfig，metaForm 组件对应属性名就叫 formDefaultConfig
  - 3. 在对应属性下配置项目对应默认值（配置项也是对象），属性名就是你需要设置默认值的那个属性，比如 klee 需要配置 metaTable 每页数量为 50，就设置 tableDefaultConfig: { pageSize: 50 }
  - 示例：export default {
    /**表格配置默认值 \*/
    tableDefaultConfig: {
    /**每页数量 _/
    pageSize: 50
    },
    /\*\*表单项配置默认值 _/
    formDefaultConfig: {
    /\*_输入框最大长度 _/
    inputMaxLength: 30
    }
    };

#### 0.7.13-klee 2024/2/21

klee 临时版本

- MetaTable
  - useTable 钩子函数 pageSize 设置为默认 50

#### 0.7.13 2024/2/1

- MetaForm
  - 表单项 hint 属性新增函数类型

#### 0.7.12 2024/1/30

- MetaForm
  - 修复多图上传图片预览时，叉号显示位置错误的 bug
  - 修复了下拉多选表单项有必填校验时，初始化出现校验信息的 bug

#### 0.7.11 2024/1/30

- MetaForm
  - 修复多图上传 add 模式下，无法上传图片的 bug

#### 0.7.10 2024/1/30

- MetaForm
  - 修正图片上传后，图片框底部多了一条白条的 bug
  - 真正修复了多图上传的 BUG，可以多图上传了

#### 0.7.9 2024/1/23

- MetaForm
- 修复了单个图片上传表单，非空校验出现提示信息，上传图片后，提示信息依然存在的 bug

#### 0.7.8 2024/1/12

- MetaForm
  - 在 MetaForm/form-items/index.vue 文件中，将表单内容类型为 slot 时，校验信息改成不在行内显示
  - 在 MetaForm/form-items/SingleSelect.vue 文件中,将下拉选择 reserve-keyword 设置为 false。

#### 0.7.7 2024/1/10

- MetaTable
  - 修改了缓存中取出表格数据时是否需要刷新表格数据的逻辑，默认不刷新

#### 0.7.6 2024/1/5

- MetaTable
  - Klee 项目中需要表格分页中 pageSize 默认为 50，特殊处理

#### 0.7.5 2023/12/7

- MetaForm
  - 给 form-items 中 IMageUpload 组件新增一个文件改变发送事件通知父组件的功能

#### 0.7.4 2023/12/5

- MetaForm
  - 修复图片上传组件编辑图片时只能获取第一个图片上传表单内容的 bug

#### 0.7.3

- MetaForm
  - MetaForm 表单配置项中 otherAttr 新增 showUploadPlacehoder 字段，用来控制提示文案后是否拼接默认文案
- MetaTable
  - MetaTable 中的 FilterConfigOptions 中 otherAttr 新增输入框最大输入长度 maxlength,默认值为 30

#### 0.7.2 2023/11/24

- MetaForm
  - 修复图片上传组件多图模式的 bug

#### 0.7.1 2023/11/20

- MetaTable
  - 1.Filter 中 placeholder 类型中新增了返回字符串类型的函数类型
  - 2.MetaForm 中表单项的 placeholder 新增了返回字符串类型的函数类型

#### 0.7.0 2023/11/16

- MetaTable
  - 1.新增了 Filter 中筛选项可通过 visible 控制展示隐藏
  - 2.优化了 HeadConfig 中的数据同步列名

#### 0.6.19 2023/11/10

- MetaForm
  - 修复了 imageUpload 单文件上传模式无法删除文件的 bug

#### 0.6.18 2023/10/11

-MetaForm - 新增表单项类型“year”用于进行年份选择

#### 0.6.17 2023/8/14

- MetaForm
  - 修复 autocomplete 中，appendOptionToBody 在重复 key 值时的错误

#### 0.6.16 2023/8/11

- MetaForm
  - successCallBack 方法增加第三个参数，回传请求成功后的响应数据

#### 0.6.15 2023/7/18

- MetaTable
  - 增加 beforeEnterInlineMode 属性，用于处理行内编辑打开前的逻辑数据处理

#### 0.6.14 2023/7/16

- MetaForm
  - 增加 clearable 属性，控制表单控件值是否可清除

#### 0.6.13 2023/7/14

- MetaForm
  - 修复因 remoteOption 导致的草稿错误问题：修改 singleSelet 组件中，remoteOption 的存储方式，不再赋值给 formData
  - ImageOptions 里的 ImageEditOptions 增加 components 属性，控制图片编辑时的功能列表，默认马赛克与裁剪都开启
- MetaTable
  - 表格行内编辑新增行顶部底部可选
  - 表格内表单 textarea 类型增加上下边距

#### 0.6.12 2023/7/13

- MetaTable
  - 表格列配置项增加 headerHint 以增加表头提示

#### 0.6.11 2023/7/10

- MetaTable
  - 表格排序升序时后端要求格式 sortFields: ["字段名 asc"]

#### 0.6.10 2023/7/7

- MetaTable
  - 修复了在行内编辑模式下，inlineEdit.type 为 slot 时，自定义插槽不生效的 bug

#### 0.6.9 2023/7/6

- MetaTable
  - 增加 pageNum 属性，暴露 pageable 分页状态，增加并暴露获取与设置表格滚动高度的方法
- MetaHooks
  - 增加 usePageMemory，用于缓存列表页前往详情页时记录表格状态并在返回时恢复状态。
- MetaLayout
  - SinglePage 组件在离开时，增加调用 usePageMemory 里的方法，配合实现缓存列表状态

#### 0.6.8 2023/7/1

- MetaForm
  - 暴露 clearValidate 方法，用于清除校验提示；label 扩展 Function 类型，用于动态变化的 label

#### 0.6.7 2023/6/30

- MetaForm
  - 图片编辑样式调整，暂时强制裁剪
- MetaTable
  - 导出优化，请求到数据后执行一次 tableConfig 的 dataCallback，保持和表格数据一致

#### 0.6.6 2023/6/29

- MetaForm
  - imageOptions 增加 editOptions 属性，用于配置图片上传后裁剪，打码的相关配置

#### 0.6.5 2023/6/27

- MetaForm
  - SingleSelect 增加 disableOption，控制选项是否禁用
- SinglePage
  - 增加 beforeBack 属性，用于返回拦截

#### 0.6.4 2023/6/09

- ExportBtn
  - 导出优化，提取方法，方便在表格外部使用

#### 0.6.3 2023/6/07

- MetaSocket
  - 断网情况下不进行重连，网络重连时 socket 才尝试重连

#### 0.6.2 2023/6/06

- MetaTable
  - 表格增加数据项为空时的占位符 "-"
- MetaUtils
  - getChangeLog 增加 date 日期以及 datetime 日期时间的处理

#### 0.6.1 2023/5/31

- MetaForm
  - datetime 增加 disabledTime 属性，控制不可选择日期
- MetaTable
  - 增加 pageSize 属性，配置分页大小，默认 10

#### 0.6.0 2023/5/23

- socket
  - 完善 socket 组件，实现关闭链接、自动重连等功能

#### 0.5.5 2023/05/19

- MetaForm
  - 新增 inputOnlyNonnegativeNumber 属性，控制输入非负数

#### 0.5.4 2023/05/15

- MetaUtils
  - MetaUtils 工具， getChangeLog 方法增加 suffix 参数，用于配置变更记录描述的后缀，优化 extraFormContent 与 formContents 的合并，变更记录为删除时显示删除的字段信息

#### 0.5.3 2023/05/06

- metaUtils
  - 修复 FormUtils 中 findChildren 的世纪 bug
- metaForm
  - SingleSelect otherAttrs 增加 formatSubLabel 属性，用于自定义 subLabel 的显示内容

#### 0.5.2 2023/05/05

- metaLayout
  - TwoPartsPage 组件， 增加并暴露 onChangeExpand 方法

#### 0.5.1 2023/04/27

- metaform
  - 修复 date 组件：手动输入日期时，后一位不为 23：59：59 的 bug

#### 0.5.0 2023/4/20

- metaTable
  - 列配置新增 visible 属性，传入函数，返回 boolean，决定列是否显示。默认显示

#### 0.4.1 2023/4/20

- metaForm
  - 外层 col 增加 100%width 样式

#### 0.4.0 2023/4/10

- metaTable
  - 新增导出功能，填写 exportConfig 后可进行数据导出

#### 0.3.1 2023/4/6

- metaForm
  - 修复 singleSelect 组件，第一次数据回填时不触发 onChange 函数的 bug

#### 0.3.0 2023/4/6

- metaUtils
  - 新增 validator.ts 文件，用于放置常用表单自定义校验函数
  - validator.ts 新增身份证号校验

#### 0.2.1 2023/4/6

- metaDetail
  - 修复数值为 0 被判定为无数据的 BUG

#### 0.2.0 2023/4/6

- metaHook
  - 新增 metaHook 文件夹，用于存放全项目通用的 hooks
  - 新增 useDialog，是所有弹窗和抽屉的通用 hook
- common
  - 新增 DateTime 组件，用于展示时间，且可根据是否过期、临期，自动展示不同颜色

#### 0.1.0 2023/4/5

- metaForm
  - 完善草稿功能
  - 对外暴露 isFormDataChanged 属性，标记表单内容是否发生变化
  - 修复 singleselect 组件数据回填时，未支持多选组件的 bug

#### 0.0.15 2023/4/3

- metaForm
  - singleSelect 组件，修复数据回填时未校验是否已获取到候选项数据的 BUG

#### 0.0.14 2023/4/3

- Layout
  - 双列布局新增展开收起功能

#### 0.0.13 2023/3/29

- klee 与 hyrule 版本统一
- metaTable
  - 新增 SetSelection 方法
  - 新增 setCurrentTab
- metaForm
  - 修复文件上传 size 过大时的 bug
- OprDelete
  - 新增 type 属性，可控制删除按钮颜色

#### 0.0.12 2023/2/22

- metaForm
  - 所有获取候选项的组件中，在 getOption 方法内，当获取到的候选项数据为 string 格式时，会自动尝试进行 JSON 格式化。该举措是用于适配可能从 storage 中获取数据的情况（例如船舶创建时，船舶类型的候选项获取）

#### 0.0.11 2023/2/20

- metaForm
  - FormConfigOption 新增 changeLogApi 属性，可传入用于提交变更记录的接口
- utils
  - 新增 changeLogUtils 文件，用于生成 changelog 提交用数据，和解析该数据

#### 0.0.10 2023/2/16

- metaTable
  - 修复 tableConfig 若设置 showTitleArea 为 false 时，表格高度未自动计算的 bug
- metaForm
  - 新增特性：singleSelect 组件中，当给定的内容（例如编辑时回填 detail 内容）在候选项中不存在时，自动删除该值，以确保让用户手动进行选择
  - 新增特性：input 组件中，当首次获取到给定的内容时，立即进行一次小数点校验。保证数字类 input 在获取到数字时，自动根据配置显示正确的小数点
  - 优化：useForm 文件中，提交表单时，将表单内容先进行深拷贝，再进行后续操作

#### 0.0.9 2023/2/13

- metaTable
  - 实现 tableConfig 中 isSelectItem 属性
  - 优化 OprDelete 组件，可传递 width 属性，控制弹出框的宽度

#### 0.0.8 2023/2/10

- metaTable
  - 新增自动计算 filter 区域中展开清空按钮所占 span 的宽度
  - 实现 tableConfig 中 selectType 为 single 时的单选逻辑

#### 0.0.7 2023/2/9

- metaTable
  - tableConfig 新增 isSelectionInline 和 selectionStyle 属性，用于控制多选选择区域是否脱离文档流

#### 0.0.6 2023/2/8

- metaLayout
  - 增加 threeParts 三列式页面样式
- metaTable
  - otherAttr 新增 collapseTag 字段控制下拉多选的已选内容是否折叠
  - 修正表格内部可以上下轻微滚动的 bug
- metaForm
  - 草稿读取功能适配远端读取候选项的下拉选择组件
  - 删除了不符合规范的下拉多选属性

#### 0.0.5 2023/1/11

- 完成 fileupload 组件。目前仅支持多文件数组形式

#### 0.0.4 2023/1/9

- meta-table 的 tableConfig 新增 showTitleArea 属性，影响表格顶部 title 区域是否显示。

#### 0.0.3 2023/1/6

- 更新上传图片组件
- 更新富文本组件
