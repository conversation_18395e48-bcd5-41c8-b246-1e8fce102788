import { defineStore } from "pinia";
import { MenuState } from "../interface";
import piniaPersistConfig from "@/config/piniaPersist";
import { useMenuRedCount } from "@/hooks/useMenuRedCount";

const { startPolling } = useMenuRedCount();
// MenuStore
export const MenuStore = defineStore({
	id: "MenuState",
	state: (): MenuState => ({
		// menu collapse
		isCollapse: false,
		// menu List
		menuList: [],
		// 全局红点数记录
		redCount: {
			// 体系管理-审批
			"system-mgr-approval": 0,
			// 体系管理-抄送
			"system-mgr-copy": 0
		}
	}),
	getters: {},
	actions: {
		async setCollapse() {
			this.isCollapse = !this.isCollapse;
		},
		async setMenuList(menuList: Menu.MenuOptions[]) {
			this.menuList = menuList;
			startPolling();
		},
		async setMenuBadgeCount(menuName: string, count: number) {
			setMenuBadgeCount(this.menuList, menuName, count);
		},
		async setRedCount(menuName: string, count: number) {
			this.redCount[menuName] = count;
		}
	},
	persist: piniaPersistConfig(import.meta.env.VITE_META_CODE + "MenuState")
});
// 设置菜单红点数
function setMenuBadgeCount(menuList: Menu.MenuOptions[], menuName: string, count: number): boolean {
	for (const item of menuList) {
		if (item.name === menuName) {
			item.badgeCount = count;
			return true;
		}
		if (item.children) {
			if (setMenuBadgeCount(item.children, menuName, count)) {
				// 父菜单红点数为所有子菜单红点数之和
				item.badgeCount = item.children.reduce((sum, child) => sum + (child.badgeCount || 0), 0);
				item.isMini = true;
				return true;
			}
		}
	}
	return false;
}
