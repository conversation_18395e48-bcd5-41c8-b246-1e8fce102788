import { User } from "@/api/interface/user";
import { SalaryOption } from "@/api/interface/salary.model";

/* themeConfigProp */
export interface ThemeConfigProp {
	layout: string;
	primary: string;
	isDark: boolean;
	isGrey: boolean;
	isWeak: boolean;
	breadcrumb: boolean;
	tabs: boolean;
	footer: boolean;
}

/* GlobalState */
export interface GlobalState {
	token: string;
	userInfo: User.UserInfo;
	assemblySize: string;
	language: string;
	themeConfig: ThemeConfigProp;
	aisToken: string;
}

/* MenuState */
export interface MenuState {
	isCollapse: boolean;
	menuList: Menu.MenuOptions[];
	/** 全局需要轮询的红点数量 */
	redCount: {
		[key: string]: number;
	};
}
/* ReportState */
export interface ReportState {
	/** 日报uuid */
	dailyId: string;
	/** 日报id */
	id: string;
}

/* SailorState */
export interface SailorState {
	/** 船舶库视图模式 */
	viewMode: string;
}

/* CertState */
export interface CertState {
	/** 船舶证书视图模式 */
	viewMode: string;
}

/* TabsState */
export interface TabsState {
	tabsMenuValue: string;
	tabsMenuList: TabsOptions[];
}

/* AuthState */
export interface AuthState {
	authButtons: {
		[key: string]: any;
	};
	authRouter: string[];
	homePath: string;
	keepAliveRouter: Array<string>;
}

/* DispatchState */
export interface DispatchState {
	/**按地点搜索的搜索记录 */
	searchHistory: Array<HistoryOption>;
}

export interface HistoryOption {
	/**内容 */
	value: string;
	/**是历史搜索项的标记 */
	isHistory: boolean;
}

/**CacheTableState */
export interface CacheTableState {
	/**需要缓存的表格信息 */
	tables: Array<TableInfo>;
}

/**表格信息 */
export interface TableInfo {
	/**该表格存在的页面名称 */
	name: string;
	/**表格滚动的位置 */
	position: ScrollPosition;
}

/**表格滚动位置信息 */
export interface ScrollPosition {
	/**横向 */
	scrollX: number;
	/**纵向 */
	scrollY: number;
}

/**海图信息记录 */
export interface SeamapState {
	/**关闭的船舶对象信息 */
	hiddenNoticeShips: Array<hiddenNoticeShipInfo>;
}

/** */
export interface hiddenNoticeShipInfo {
	/**uuid */
	uuid: string;
	/**关闭的时间 时间戳 */
	closeTime: number;
}

/** 请假 */
export interface LeaveState {
	/** 请假指定人员配置 */
	option: {
		userName: string;
		phone: string;
		userId: number;
	};
}

/** SalaryState */
export interface SalaryState {
	/** 工资表相关存储 */
	option: SalaryOption.SalaryOption;
}
