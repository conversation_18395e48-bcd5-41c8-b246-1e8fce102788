import { defineStore, create<PERSON><PERSON> } from "pinia";
import { GlobalState, ThemeConfigProp } from "./interface";
import { User } from "@/api/interface/user";
import { DEFAULT_PRIMARY } from "@/config/config";
import piniaPersistConfig from "@/config/piniaPersist";
import piniaPluginPersistedstate from "pinia-plugin-persistedstate";
import { MetaSocket } from "@/meta-components/Socket";
import { useMenuRedCount } from "@/hooks/useMenuRedCount";

const { stopPolling } = useMenuRedCount();
// defineStore 调用后返回一个函数，调用该函数获得 Store 实体
export const GlobalStore = defineStore({
	// id: 必须的，在所有 Store 中唯一
	id: "GlobalState",
	// state: 返回对象的函数
	state: (): GlobalState => ({
		// token
		token: "",
		/**ais系统token */
		aisToken: "",
		// userInfo

		userInfo: {} as User.UserInfo,
		// element组件大小
		assemblySize: "default",
		// language
		language: "",
		// themeConfig
		themeConfig: {
			// 布局切换 ==>  纵向：vertical | 经典：classic | 横向：transverse | 分栏：columns
			layout: "transverse",
			// 默认 primary 主题颜色
			primary: DEFAULT_PRIMARY,
			// 深色模式
			isDark: false,
			// 灰色模式
			isGrey: false,
			// 色弱模式
			isWeak: false,
			// 面包屑导航
			breadcrumb: true,
			// 标签页
			tabs: false,
			// 页脚
			footer: false
		}
	}),
	getters: {},
	actions: {
		// setToken
		setToken(token: string) {
			this.token = token;
			//token变化需socket重连
			MetaSocket.close();
			if (token) {
				MetaSocket.reconnect();
			}
			//token设空时，取消红点轮询
			if (!token) {
				stopPolling();
			}
		},
		setAisToken(token: string) {
			this.aisToken = token;
		},
		// setUserInfo
		setUserInfo(userInfo: any) {
			this.userInfo = userInfo;
		},
		// setAssemblySizeSize
		setAssemblySizeSize(assemblySize: string) {
			this.assemblySize = assemblySize;
		},
		// updateLanguage
		updateLanguage(language: string) {
			this.language = language;
		},
		// setThemeConfig
		setThemeConfig(themeConfig: ThemeConfigProp) {
			this.themeConfig = themeConfig;
		},
		// 设置corpId
		setCorpId(corpId: number) {
			this.userInfo.corpId = corpId;
		}
	},
	persist: piniaPersistConfig(import.meta.env.VITE_META_CODE + "GlobalState")
});

// piniaPersist(持久化)
const pinia = createPinia();
pinia.use(piniaPluginPersistedstate);

export default pinia;
