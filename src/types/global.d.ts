// * Menu
declare namespace Menu {
	export interface MenuOptions extends MenuBase {
		/**
		 * 菜单按钮
		 */
		btns?: MenuBtn[];
		children?: Detail[];
		/**
		 * 菜单参数
		 */
		params?: MenuParam[];
		query: any;
		/** 菜单红点数量 */
		badgeCount?: number;
		/** 是否是小红点形式 */
		isMini?: boolean;
	}

	export interface MenuBase {
		/**
		 * 是否自动关闭tab 1:关闭 2:不关闭
		 */
		closeTab?: number;
		/**
		 * 前端文件路径
		 */
		component?: string;
		/**
		 * 创建时间
		 */
		createdAt?: string;
		/**
		 * 删除时间
		 */
		deletedAt?: string;
		/**
		 * 平台端ID
		 */
		endpointId?: number;
		/**
		 * 1:隐藏 2:不隐藏
		 */
		hidden?: number;
		/**
		 * 图标
		 */
		icon?: string;
		/**
		 * id
		 */
		id?: number;
		/**
		 * 1:缓存 2:不缓存
		 */
		keepAlive?: number;
		/**
		 * 路由名称
		 */
		name: string;
		/**
		 * 父菜单ID
		 */
		parentId?: number;
		/**
		 * 父菜单标题
		 */
		parentTitle?: string;
		/**
		 * 路由path
		 */
		path: string;
		/**
		 * 平台ID
		 */
		platformId?: number;
		/**
		 * 排序值
		 */
		sort?: number;
		/**
		 * 菜单名
		 */
		title: string;
		/**
		 * 更新时间
		 */
		updatedAt?: string;
		/**
		 * uuid
		 */
		uuid?: string;
		/**
		 * 是否在浏览器新标签页中打开
		 */
		openNewPage?: number;
	}
	/**
	 * 菜单关联按钮信息
	 */
	export interface MenuBtn {
		api?: { [key: string]: any };
		/**
		 * 创建时间
		 */
		createdAt?: string;
		/**
		 * uuid
		 */
		id?: number;
		/**
		 * 参数key
		 */
		key?: string;
		/**
		 * 菜单ID
		 */
		menuId?: number;
		/**
		 * 更新时间
		 */
		updatedAt?: string;
	}

	/**
	 * 菜单参数信息
	 */
	export interface MenuParam {
		/**
		 * 创建时间
		 */
		createdAt?: string;
		/**
		 * uuid
		 */
		id?: number;
		/**
		 * 参数key
		 */
		key?: string;
		/**
		 * 菜单ID
		 */
		menuId?: number;
		/**
		 * 携带参数类型
		 */
		type?: string;
		/**
		 * 更新时间
		 */
		updatedAt?: string;
		/**
		 * 参数value
		 */
		value?: string;
	}
}

declare interface TabsOptions {
	title: string;
	path: string;
	query?: any;
	close: boolean;
}

// * Vite
declare type Recordable<T = any> = Record<string, T>;

declare interface ViteEnv {
	VITE_API_URL: string;
	VITE_PORT: number;
	VITE_OPEN: boolean;
	VITE_GLOB_APP_TITLE: string;
	VITE_DROP_CONSOLE: boolean;
	VITE_PROXY_URL: string;
	VITE_BUILD_GZIP: boolean;
	VITE_REPORT: boolean;
}
