// 体系管理
import { Apply, Approval, Ledger } from "../interface/system.model";
import { PORT1 } from "@/api/config/servicePort";
import { ResPage } from "../interface";
import http from "@/api";

export class ApplyService {
	//申请详情
	static detail(params: Apply.DetailReq) {
		return http.get<Apply.Detail>(PORT1 + "/corp-api/v1/sys/manage/detail", params);
	}
	// 申请列表
	static getList(params: Apply.ApplyListReq) {
		let { offset, length } = params;
		return http.post<ResPage<Apply.ApplyListInfo>>(`/corp-api/v1/sys/manage/apply/list/${offset}/${length}`, params);
	}
	// 申请编辑
	static update(params: Apply.ApplyUpdateInfo) {
		return http.post(PORT1 + "/corp-api/v1/sys/manage/apply/update", params);
	}
	// 申请新建
	static create(params: Apply.ApplyInfo) {
		return http.post(PORT1 + "/corp-api/v1/sys/manage/apply/new", params);
	}
	// 删除申请
	static delete(params: Apply.ReqUuid) {
		return http.get(PORT1 + "/corp-api/v1/sys/manage/apply/delete", params);
	}
	// 撤回申请
	static revoke(params: Apply.ReqUuid) {
		return http.get(PORT1 + "/corp-api/v1/sys/manage/apply/revoke", params);
	}
	//修改列表
	static updateInList(params: Apply.ApplyEditListReq) {
		return http.post(PORT1 + "/corp-api/v1/sys/manage/edit/list", params);
	}
}
/** 审批接口 */
export class ApprovalService {
	//审批列表
	static getList(params: Approval.ApprovalListReq) {
		let { offset, length } = params;
		return http.post<ResPage<Approval.ApprovalListInfo>>(`/corp-api/v1/sys/manage/approval/list/${offset}/${length}`, params);
	}
	//审批
	static approval(params: Approval.ApprovalReq) {
		return http.post(PORT1 + "/corp-api/v1/sys/manage/approval", params);
	}
	//审批已阅
	static read(params: Approval.ReadReq) {
		return http.post(PORT1 + "/corp-api/v1/sys/manage/approval/read", params);
	}
	//获取默认的审批流程
	static getDefaultApproval(params: Approval.TypeDefaultReq) {
		return http.get<Approval.ApprovalDefResp>(PORT1 + "/corp-api/v1/sys/manage/approval/def", params, {
			showLoading: false
		});
	}
	//审批/抄送未读数量
	static getApprovalCount() {
		return http.get<Approval.ApprovalListUnReadResp>(
			PORT1 + "/corp-api/v1/sys/manage/approval/cc/unread",
			{},
			{
				showLoading: false
			}
		);
	}
}
/** 台账接口 */
export class LedgerService {
	// 台账列表
	static getList(params: Ledger.LedgerListReq) {
		const { offset, length } = params;
		return http.post<ResPage<Ledger.LedgerListInfo>>(`/corp-api/v1/sys/manage/ledger/list/${offset}/${length}`, params);
	}
	// 台账删除
	static delete(params: Apply.ReqUuid) {
		return http.get(PORT1 + "/corp-api/v1/sys/manage/ledger/delete", params);
	}
}
