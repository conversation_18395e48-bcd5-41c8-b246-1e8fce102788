import { Member, MemberChangeLogOptions } from "../interface/member";
import { PORT1 } from "@/api/config/servicePort";
import { ResPage } from "@/api/interface/index";

import http from "@/api";

/**组织架构成员相关接口 */
export class MemberService {
	/**获取部门下的用户列表 */
	static getListInDepart = (params: Member.ReqListInDepartParam) => {
		const { offset, length } = params;
		return http.post<ResPage<Member.DetailInDepartList>>(
			PORT1 + `/user-api/v1/user/corp/depart/list/${offset}/${length}`,
			params
		);
	};
	/** 按职位筛选用户列表 */
	static getListByPosition = (params: Member.ReqListByPositionParam) => {
		return http.post<ResPage<Member.DetailInDepartList>>(PORT1 + `/user-api/v1/user/corp/position/list`, params);
	};

	/**获取用户薪资信息列表 */
	static getListForSalary = (params: Member.ReqListInDepartParam) => {
		const { offset, length } = params;
		return http.post<Member.CorpUsersWithSalary>(PORT1 + `/user-api/v1/user/corp/salary/list/${offset}/${length}`, params);
	};

	/**启禁用 */
	static switchEnable = (params: Member.ReqEnableParam) => {
		return http.post(PORT1 + `/user-api/v1/user/corp/enable`, params);
	};

	/**获取用户详情 */
	static getDetail = (params: Member.ReqDetailParam) => {
		return http.get<Member.Detail>(PORT1 + `/user-api/v1/user/corp/detail`, params, {
			ignoreCanceler: true
		});
	};

	/**新建成员 */
	static add = (params: Member.ReqAddParam) => {
		return http.post(PORT1 + `/user-api/v1/user/corp/new`, params);
	};

	/**成员离职 */
	static leave = (params: Member.ReqLeaveParam) => {
		return http.post(PORT1 + `/user-api/v1/user/corp/leave`, params);
	};

	/**更新 */
	static edit = (params: Member.ReqEditParam) => {
		return http.post(PORT1 + `/user-api/v1/user/corp/update`, params);
	};

	/**获取离职成员列表 */
	static getLeaveList = (params: Member.ReqLeaveMemberListParam) => {
		const { offset, length } = params;
		return http.post<ResPage<Member.DetailInLeaveList>>(PORT1 + `/user-api/v1/user/corp/leave/list/${offset}/${length}`, params);
	};

	/**获取简单成员列表 */
	static getSimpleList = (params: Member.ReqSimpleListParam) => {
		const offset = params.offset ?? 0;
		const length = params.length ?? 20;
		return http.post<Member.ResSimpleList>(PORT1 + `/user-api/v1/user/corp/search/${offset}/${length}`, params, {
			showLoading: false
		});
	};
}

/**
 *用户变更记录接口服务
 */
export class MemberChangeLogService {
	/**新建变更记录 */
	static add = (params: MemberChangeLogOptions.ReqAddParam) => {
		return http.post(PORT1 + `/user-api/v1/user/operate/record/new`, params);
	};

	/**获取变更记录列表 */
	static getList = (params: MemberChangeLogOptions.ReqListParam) => {
		const { offset, length } = params;
		return http.post<ResPage<MemberChangeLogOptions.Detail>>(
			PORT1 + `/user-api/v1/user/operate/record/${offset}/${length}`,
			params
		);
	};
}
