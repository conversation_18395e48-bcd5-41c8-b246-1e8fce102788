/** 船舶汇报 */
export namespace ShipReport {
	export interface ReqUuid {
		uuid: string;
	}
	/**
	 * ShipReport
	 */
	export interface ShipReport {
		/**
		 * 顶层甲板图片
		 */
		bridgeImg?: string[];
		/**
		 * 机舱图片
		 */
		cabinImg?: string[];
		/**
		 * 创建时间
		 */
		createdAt?: string;
		/**
		 * 甲板图片
		 */
		deckImg?: string[];
		/**
		 * 文字说明
		 */
		description?: string;
		/**
		 * id
		 */
		id?: number;
		/**
		 * 是否已读
		 */
		isRead?: number;
		/**
		 * 生活区图片
		 */
		lifeImg?: string[];
		/**
		 * 消防救生设备图片
		 */
		lifesavingImg?: string[];
		/**
		 * 报告人姓名
		 */
		reporterName?: string;
		/**
		 * 船舶ID
		 */
		shipId?: number;
		/**
		 * 船舶名称
		 */
		shipName?: string;
		/**
		 * 报告类型 1:日常报告 2:隐患排查 3:卫生检查 4:维护保养 5:事故报告 6:作业报告
		 */
		type?: number;
		/**
		 * 更新时间
		 */
		updatedAt?: string;
		/**
		 * uuid
		 */
		uuid?: string;
		/**
		 * 施工图片
		 */
		workImg?: string[];
	}

	/**
	 * ShipReportListReq
	 */
	export interface ShipReportListReq {
		/**
		 * 创建时间
		 */
		createdAt?: string[];
		/**
		 * 记录数
		 */
		length?: number;
		/**
		 * 开始的记录数
		 */
		offset?: number;
		/**
		 * 报告人姓名
		 */
		reporterName?: string;
		/**
		 * 船舶名称
		 */
		shipName?: string;
		/**
		 * 报告类型 1:日常报告 2:隐患排查 3:卫生检查 4:维护保养 5:事故报告 6:作业报告
		 */
		type?: number;
	}

	/**
	 * ShipGroup
	 */
	export interface ShipGroup {
		reports?: ShipReport[];
		shipId?: number;
		shipName?: string;
		total?: number;
	}

	/**
	 * ShipReportOption
	 */
	export interface Option {
		phone?: string;
		userId?: number;
	}
}
