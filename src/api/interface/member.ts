// 组织架构成员相关

import { YN } from "@/enums/global-enums";

export namespace Member {
	/**
	 * 部门成员列表入参
	 */
	export interface ReqListInDepartParam {
		/**
		 * 部门ID
		 */
		departId?: number;
		/**
		 * 手机号/姓名
		 */
		field?: string;
		/**
		 * 记录数
		 */
		length?: number;
		/**
		 * 开始的记录数
		 */
		offset?: number;
		/**
		 * 职务ID
		 */
		postionId?: number;
		/**
		 * true未分组查询
		 */
		unGroup?: boolean;
	}

	/**
	 * 职务成员列表入参
	 * CorpUsersByPositionReq
	 */
	export interface ReqListByPositionParam {
		/**
		 * 职务Ids
		 */
		positionIds?: number[];
	}
	/**
	 * 部门成员详情（部门列表中）
	 */
	export interface DetailInDepartList {
		/**
		 * 头像
		 */
		avatar: string;
		/**
		 * 联系电话
		 */
		contactMobile: string;
		/**
		 * 创建时间
		 */
		createdAt?: string;
		/**
		 * 删除时间
		 */
		deletedAt?: string;
		/**
		 * 部门
		 */
		departs: CorpUserDepart[];
		/**
		 * 1启用 2禁用
		 */
		enabled: number;
		/**
		 * id
		 */
		id?: number;
		/**
		 * 成员名称
		 */
		name: string;
		/**
		 * 职务
		 */
		positions: CorpUserPosition[];
		/**
		 * 权限组
		 */
		roles: CorpUserRole[];
		/**
		 * 更新时间
		 */
		updatedAt?: string;
		/**
		 * uuid
		 */
		uuid?: string;
	}
	/**
	 * CorpUsersWithSalary
	 */
	export interface CorpUsersWithSalary {
		list: CorpUserWithSalary[];
		total: number;
	}

	/**
	 * CorpUserWithSalary
	 */
	export interface CorpUserWithSalary {
		/**
		 * 通讯补贴
		 */
		communicationSubsidy: string;
		/**
		 * 联系电话
		 */
		contactMobile: string;
		/**
		 * 创建时间
		 */
		createdAt?: string;
		/**
		 * 删除时间
		 */
		deletedAt?: string;
		/**
		 * 入职时间
		 */
		enterTime: string;
		/**
		 * id
		 */
		id?: number;
		/**
		 * 离职时间
		 */
		leaveTime: string;
		/**
		 * 成员名称
		 */
		name: string;
		/**
		 * 缴纳项目
		 */
		payItems: string[];
		/**
		 * 工资
		 */
		salary: string;
		/**
		 * 工资分摊岗位 [公司, 部门or船舶, 职务]
		 */
		salaryPosition: [string, string, string] | null;
		/**
		 * 副卡工人姓名
		 */
		secondCardName: string;
		/**
		 * 副卡工资
		 */
		secondCardSalary: string;
		/**
		 * 工龄工资
		 */
		senioritySalary: string;
		/**
		 * 更新时间
		 */
		updatedAt?: string;
		/**
		 * 用户ID
		 */
		userId: number;
		/**
		 * uuid
		 */
		uuid?: string;
	}
	/**获取成员详情入参 */
	export interface ReqDetailParam {
		uuid: string;
	}

	/**
	 * 部门成员详情（详情页）
	 */
	export interface Detail extends DetailInDepartList {
		/**
		 * 入职时间
		 */
		enterTime: string;
		/**
		 * 上次登录时间
		 */
		lastLogin: string;
		/**
		 * 登录手机号
		 */
		mainMobile: string;
		/**
		 * 微信号
		 */
		wechat: string;
		/**
		 * 邮箱
		 */
		email: string;
		/**
		 * 身份证
		 */
		idCard?: string;
		/**
		 * 籍贯
		 */
		nativePlace?: string;
		/**
		 * 备注
		 */
		remark?: string;
		/**
		 * 头像
		 */
		acatar?: string;
		/**
		 * 身份证反面
		 */
		idCardBack?: string;
		/**
		 * 身份证正面
		 */
		idCardFront?: string;
		/**
		 * 身份证地址
		 */
		idCardAddress?: string;
		/**
		 * 银行卡
		 */
		bandCard?: string;
		/**
		 * 银行卡正面
		 */
		bankCardFront?: string;
		/**
		 * 银行卡反面
		 */
		bankCardBack?: string;
		/**
		 * 工资
		 */
		salary?: string;
		/**
		 * 工龄工资
		 */
		senioritySalary?: string;
		/**
		 * 合同类型
		 */
		contractType?: ContractType;
		/**
		 * 合同起止时间
		 */
		contractTime?: Array<String>;
		/**
		 * 合同年限
		 */
		contractPeriod?: string;
		/**
		 * 民族
		 */
		nation?: string;
		/**
		 * 紧急联系人
		 */
		emergencyContact?: string;
		/**
		 * 紧急联系人手机
		 */
		emergencyMobile?: string;
	}

	/**
	 * 用户所属部门数组（部门列表中）
	 */
	export interface CorpUserDepart {
		depart: string;
		id: number;
	}

	/**
	 * 用户职务数组（部门列表中）
	 */
	export interface CorpUserPosition {
		id: number;
		position: string;
	}

	/**
	 * 用户角色数据（部门列表中）
	 */
	export interface CorpUserRole {
		id: number;
		role: string;
	}

	/**成员启禁用入参 */
	export interface ReqEnableParam {
		/**
		 * 1开启 2关闭
		 */
		enabled?: number;
		uuid: string;
	}

	/**
	 * 新建成员入参
	 */
	export interface ReqAddParam {
		/**
		 * 企业ID
		 */
		corpId?: number;
		/**
		 * 姓名
		 */
		name?: string;
		/**
		 * 登录手机号
		 */
		mainMobile?: string;
		/**
		 * 权限组
		 */
		roleIds?: number[];
		/**
		 * 部门
		 */
		departIds?: number[];
		/**
		 * 职务
		 */
		posIds?: number[];
		/**
		 * 入职时间
		 */
		enterTime?: string;
		/**
		 * 头像
		 */
		acatar?: string;
		/**
		 * 身份证
		 */
		idCard?: string;
		/**
		 * 身份证反面
		 */
		idCardBack?: string;
		/**
		 * 身份证正面
		 */
		idCardFront?: string;
		/**
		 * 身份证地址
		 */
		idCardAddress?: string;
		/**
		 * 银行卡
		 */
		bandCard?: string;
		/**
		 * 银行卡正面
		 */
		bankCardFront?: string;
		/**
		 * 银行卡反面
		 */
		bankCardBack?: string;
		/**
		 * 工资
		 */
		salary?: string;
		/**
		 * 工龄工资
		 */
		senioritySalary?: string;
		/**
		 * 合同类型
		 */
		contractType?: ContractType;
		/**
		 * 合同起止时间
		 */
		contractTime?: Array<String>;
		/**
		 * 合同年限
		 */
		contractPeriod?: string;
		/**
		 * 民族
		 */
		nation?: string;
		/**
		 * 紧急联系人
		 */
		emergencyContact?: string;
		/**
		 * 紧急联系人手机
		 */
		emergencyMobile?: string;
		/**
		 * 邮箱
		 */
		email?: string;
		/**
		 * 微信号
		 */
		wechat?: string;
		/**
		 * 籍贯
		 */
		nativePlace?: string;
		/**
		 * 备注
		 */
		remark?: string;
		/**
		 * 启用禁用账号
		 */
		enabled?: YN;
	}

	/**
	 * 成员离职入参
	 */
	export interface ReqLeaveParam {
		/**
		 * 离职时间
		 */
		leaveTime?: string;
		/**
		 * 备注
		 */
		remark?: string;
		/**
		 * 企业用户uuid
		 */
		uuid?: string;
	}

	/**
	 * 成员更新入参
	 */
	export interface ReqEditParam {
		/**
		 * 头像
		 */
		avatar?: string;
		/**
		 * 联系电话
		 */
		contactMobile?: string;
		/**
		 * 邮箱
		 */
		email?: string;
		/**
		 * 入职时间
		 */
		enterTime?: string;
		/**
		 * 成员名称
		 */
		name?: string;
		/**
		 * 企业用户uuid
		 */
		uuid?: string;
		/**
		 * 微信号
		 */
		wechat?: string;
		/**
		 * 身份证
		 */
		idCard?: string;
		/**
		 * 籍贯
		 */
		nativePlace?: string;
		/**
		 * 备注
		 */
		remark?: string;
		/**
		 * 头像
		 */
		acatar?: string;
		/**
		 * 身份证反面
		 */
		idCardBack?: string;
		/**
		 * 身份证正面
		 */
		idCardFront?: string;
		/**
		 * 身份证地址
		 */
		idCardAddress?: string;
		/**
		 * 银行卡
		 */
		bandCard?: string;
		/**
		 * 银行卡正面
		 */
		bankCardFront?: string;
		/**
		 * 银行卡反面
		 */
		bankCardBack?: string;
		/**
		 * 工资
		 */
		salary?: string;
		/**
		 * 工龄工资
		 */
		senioritySalary?: string;
		/**
		 * 合同类型
		 */
		contractType?: ContractType;
		/**
		 * 合同起止时间
		 */
		contractTime?: Array<String>;
		/**
		 * 合同年限
		 */
		contractPeriod?: string;
		/**
		 * 民族
		 */
		nation?: string;
		/**
		 * 紧急联系人
		 */
		emergencyContact?: string;
		/**
		 * 紧急联系人手机
		 */
		emergencyMobile?: string;
		/**
		 * 权限组
		 */
		roleIds?: number[];
		/**
		 * 部门
		 */
		departIds?: number[];
		/**
		 * 职务
		 */
		posIds?: number[];
	}

	/**
	 * 离职成员列表入参
	 */
	export interface ReqLeaveMemberListParam {
		/**
		 * 部门名称
		 */
		departName?: string;
		/**
		 * 手机号/姓名
		 */
		field?: string;
		/**
		 * 记录数
		 */
		length?: number;
		/**
		 * 开始的记录数
		 */
		offset?: number;
		/**
		 * 职务名称
		 */
		posName?: string;
	}

	/**
	 * 离职成员详情
	 */
	export interface DetailInLeaveList {
		/**
		 * 头像
		 */
		avatar: string;
		/**
		 * 联系电话
		 */
		contactMobile: string;
		/**
		 * 创建时间
		 */
		createdAt?: string;
		/**
		 * 删除时间
		 */
		deletedAt?: string;
		/**
		 * 部门
		 */
		departs: string;
		/**
		 * 邮箱
		 */
		email: string;
		/**
		 * 入职时间
		 */
		enterTime: string;
		/**
		 * id
		 */
		id?: number;
		/**
		 * 离职时间
		 */
		leaveTime: string;
		/**
		 * 成员名称
		 */
		name: string;
		/**
		 * 职务
		 */
		positions: string;
		/**
		 * 备注
		 */
		remark: string;
		/**
		 * 权限组
		 */
		roles: string;
		/**
		 * 更新时间
		 */
		updatedAt?: string;
		/**
		 * uuid
		 */
		uuid?: string;
		/**
		 * 微信号
		 */
		wechat: string;
	}

	/**获取成员列表入参 */
	export interface ReqSimpleListParam {
		/**
		 * 姓名/联系手机号
		 */
		field?: string;
		/**
		 * 记录数
		 */
		length?: number;
		/**
		 * 开始的记录数
		 */
		offset?: number;
		/**
		 * 角色名称
		 */
		roleName?: string;
	}

	/**
	 * 简单成员列表返回
	 */
	export interface ResSimpleList {
		list: SimpleDetail[];
	}

	/**
	 * 简单成员详情
	 */
	export interface SimpleDetail {
		/**
		 * 头像
		 */
		avatar: string;
		/**
		 * 联系电话
		 */
		contactMobile: string;
		/**
		 * 创建时间
		 */
		createdAt?: string;
		/**
		 * 删除时间
		 */
		deletedAt?: string;
		/**
		 * id
		 */
		id?: number;
		/**
		 * 成员名称
		 */
		name: string;
		/**
		 * 更新时间
		 */
		updatedAt?: string;
		/**
		 * uuid
		 */
		uuid?: string;
	}
}

/**成员信息变更记录 */
export namespace MemberChangeLogOptions {
	/**
	 * 新建变更记录入参
	 */
	export interface ReqAddParam {
		/**
		 * 用户联系电话
		 */
		contactMobile: string;
		/**
		 * 具体内容
		 */
		content: string;
		/**
		 * 记录类型:USER-用户 CERT-证书 INS-保险
		 */
		type: "USER" | "CERT" | "INS";
		/**
		 * 变更的用户ID
		 */
		userId: number;
		/**
		 * 用户名称
		 */
		userName: string;
	}

	/**
	 * 变更记录列表入参
	 */
	export interface ReqListParam {
		/**
		 * 时间起止
		 */
		createdAt?: string[];
		/**
		 * 用户姓名/手机号
		 */
		field?: string;
		/**
		 * 记录数
		 */
		length?: number;
		/**
		 * 开始的记录数
		 */
		offset?: number;
		/**
		 * 操作人姓名/手机号/企业
		 */
		operator?: string;
		/**
		 * USER-用户/CERT-证书/INS-保险
		 */
		type?: string;
		/**
		 * 用户ID
		 */
		userId?: number;
	}

	/**
	 * 变更记录详情
	 */
	export interface Detail {
		/**
		 * 用户联系电话
		 */
		contactMobile: string;
		/**
		 * 具体内容
		 */
		content: string;
		createdAt: string;
		id: number;
		/**
		 * 操作人
		 */
		operator: string;
		/**
		 * 操作人企业
		 */
		operatorCorp: string;
		/**
		 * 操作人手机号
		 */
		operatorMobile: string;
		/**
		 * USER-用户/CERT-证书/INS-保险
		 */
		type: string;
		/**
		 * 变更的用户ID
		 */
		userId?: number;
		/**
		 * 用户名称
		 */
		userName?: string;
	}
}

/**合同类型
 * 1劳动合同 2劳务合同 3退休返聘
 */
export enum ContractType {
	劳动合同 = 1,
	劳务合同 = 2,
	退休返聘 = 3
}

/** 缴纳项目
 * 五险 公积金
 */
export enum PayItem {
	/** 五险 */
	"Insurances" = "五险",
	/** 公积金 */
	"Fund" = "公积金"
}

/**工资分摊岗位
 * 1临时工 2工人 3船员 4办公人员 5网科部
 */
export enum SalaryPositionType {
	/** 临时工 */
	"TEMPORARY" = 1,
	/** 工人 */
	"WORKMAN" = 2,
	/** 船员 */
	"SHIPMAN" = 3,
	/** 办公人员 */
	"OFFICER" = 4,
	/** 网科部 */
	"INTERNETMAN" = 5
}
/** 工资分摊岗位 可选项 */
export const PositionOptions = [
	{
		label: "临时工",
		value: SalaryPositionType.TEMPORARY
	},
	{
		label: "工人",
		value: SalaryPositionType.WORKMAN
	},
	{
		label: "船员",
		value: SalaryPositionType.SHIPMAN
	},
	{
		label: "办公人员",
		value: SalaryPositionType.OFFICER
	},
	{
		label: "网科部",
		value: SalaryPositionType.INTERNETMAN
	}
];
