// 体系管理
import { YN } from "@/enums/global-enums";
/** 申请 */
export namespace Apply {
	export interface ReqUuid {
		uuid: string;
	}
	/**
	 * 详情入参
	 */
	export interface DetailReq {
		uuid: string;
		isApplyAgain?: YN;
	}
	/**
	 * 申请详情
	 * ApplyDetailResp
	 */
	export interface Detail {
		/**
		 * 申请时间
		 */
		applyTime: string;
		/**
		 * 审批/抄送信息
		 */
		approvals: ApplyApprovalInfo[];
		/**
		 * 审批类型 5-审批 10-二级审批 15-三级审批
		 */
		approvalType: number;
		/**
		 * 申请内容
		 */
		content: string;
		/**
		 * 申请人企业用户id
		 */
		corpUserId: number;
		/**
		 * 申请人企业用户名称
		 */
		corpUserName: string;
		/**
		 * 申请Id
		 */
		id: number;
		/**
		 * 是否保存为默认 1-是 2-否
		 */
		isSaveAsDef: number;
		/**
		 * 关键词
		 */
		keyword: string;
		/**
		 * 船舶信息
		 */
		shipIds: number[];
		/**
		 * 时间标签
		 */
		timeLabel: string;
		/**
		 * 时间标签类型1-年月 2-年月日
		 */
		timeLabelType: number;
		/**
		 * 申请类型 名称前缀如C071 没有前缀用UK-01
		 */
		type: ApplyType;
		/**
		 * 申请类型名称
		 */
		typeName: string;
		/**
		 * 申请uuid
		 */
		uuid: string;
	}
	/**
	 * 审批流
	 * ApplyApprovalInfo
	 */
	export interface ApplyApprovalInfo {
		/**
		 * 审批意见
		 */
		advice?: string;
		/**
		 * 审批时间
		 */
		approvalTime?: string;
		/**
		 * 审批/抄送人ID
		 */
		corpUserId?: number;
		/**
		 * 审批/抄送人名称
		 */
		corpUserName?: string;
		/**
		 * 审批状态 1-待审批 2-同意 3-不同意
		 */
		status?: number;
		/**
		 * 5-审批 10-二级审批 15-三级审批 25-抄送
		 */
		type: number;
	}

	export enum Status {
		/** 1-未提交 */
		UNSUBMITTED = 1,
		/** 2-审批中 */
		APPROVING = 2,
		/** 3-不同意 */
		DISAGREE = 3,
		/** 4-同意 */
		AGREE = 4
	}
	/**
	 * 申请列表请求参数
	 * ApplyListReq
	 */
	export interface ApplyListReq {
		/**
		 * 申请时间
		 */
		applyTime?: string[];
		/**
		 * 流程所属部门名称
		 */
		departName?: number;
		/**
		 * 是否已阅 1-是 2-否
		 */
		isRead?: YN;
		/**
		 * 记录数
		 */
		length?: number;
		/**
		 * 开始的记录数
		 */
		offset?: number;
		/**
		 * 排序字段
		 */
		sortField?: string[];
		/**
		 * 1-未提交 2-审批中 3-不同意 4-同意
		 */
		status?: Status;
		/**
		 * 申请名称
		 */
		typeName?: string;
	}

	/**
	 * ApplyListInfo
	 * 申请列表项
	 */
	export interface ApplyListInfo {
		/**
		 * 申请时间
		 */
		applyTime: string;
		/**
		 * 部门信息
		 */
		departs: IdentInfo[];
		/**
		 * 申请Id
		 */
		id: number;
		/**
		 * 关键词
		 */
		keyword: string;
		/**
		 * 1-未提交 2-审批中 3-不同意 4-同意
		 */
		status: Status;
		/**
		 * 时间标签
		 */
		timeLabel: string;
		/**
		 * 时间标签类型 1-年月 2-年月日
		 */
		timeLabelType: TimeLabelType;
		/**
		 * 申请类型名称
		 */
		typeName: string;
		/**
		 * 申请uuid
		 */
		uuid: string;
	}
	export enum SaveType {
		/** 1-正常提交 */
		SUBMIT = 1,
		/** 2-保存草稿 */
		DRAFT = 2
	}
	export enum DepartType {
		/** 1-离岸部门 */
		OFFSHORE = 1,
		/** 2-岸基部门 */
		ONSHORE = 2
	}
	/**
	 * ApplyInfo
	 * 申请信息表单
	 */
	export interface ApplyInfo {
		/**
		 * 审批/抄送信息
		 */
		approvals?: ApplyApprovalInfo[];
		/**
		 * 申请内容
		 */
		content?: string;
		/**
		 * 部门ids
		 */
		departIds?: number[];
		/**
		 * 部门类型 1-离岸部门 2-岸基部门
		 */
		departTypes?: DepartType[];
		/**
		 * 是否保存为默认 1-是 2-否
		 */
		isSaveAsDef?: YN;
		/**
		 * 关键词
		 */
		keyword?: string;
		/**
		 * 保存类型 1-提交 2-草稿
		 */
		saveType?: SaveType;
		/**
		 * 船舶ids
		 */
		shipIds?: number[];
		/**
		 * 时间标签
		 */
		timeLabel?: string;
		/**
		 * 时间标签类型1-年月 2-年月日
		 */
		timeLabelType: TimeLabelType;
		/**
		 * 申请类型 名称前缀如C071 没有前缀用UK-01
		 */
		type?: ApplyType;
		/**
		 * 申请类型名称
		 */
		typeName?: string;
	}

	export interface ApplyUpdateInfo extends ApplyInfo {
		uuid: string;
	}
	/**
	 * 操作类型
	 */
	export enum OperateType {
		/** 1-删除 */
		DELETE = 1,
		/** 2-撤回 */
		RECALL = 2
	}
	/**
	 * 列表编辑关键词-时间标签 入参
	 * ApplyEditListReq
	 */
	export interface ApplyEditListReq {
		/**
		 * 关键词
		 */
		keyword?: string;
		/**
		 * 时间标签
		 */
		timeLabel?: string;
		/**
		 * 时间标签类型1-年月 2-年月日
		 */
		timeLabelType?: number;
		/**
		 * uuid
		 */
		uuid: string;
	}
}
/** 审批 */
export namespace Approval {
	/**
	 * 列表类型 1-审批 2-抄送
	 */
	export enum ListType {
		/** 1-审批 */
		APPROVAL = 1,
		/** 2-抄送 */
		COPY = 2
	}
	/**
	 * 审批状态
	 *
	 */
	export enum ApprovalStatus {
		/** 1-待审批 */
		WAITING = 1,
		/** 2-同意 */
		AGREE = 2,
		/** 3-不同意 */
		DISAGREE = 3
	}
	/**
	 * 审批列表请求参数
	 * ApprovalListReq
	 */
	export interface ApprovalListReq {
		/**
		 * 申请时间
		 */
		applyTime?: string[];
		/**
		 * 审核/抄送时间
		 */
		approvalTime?: string[];
		/**
		 * 是否已阅 1-是 2-否
		 */
		isRead?: YN;
		/**
		 * 记录数
		 */
		length?: number;
		/**
		 * 审批or抄送 1-审批 2-抄送
		 */
		listType?: ListType;
		/**
		 * 开始的记录数
		 */
		offset?: number;
		/**
		 * 流程所属职务名称
		 */
		positionName?: string;
		/**
		 * 排序字段
		 */
		sortField?: string[];
		/**
		 * 1-待审批 2-同意 3-不同意
		 */
		status?: ApprovalStatus;
		/**
		 * 申请类型名称
		 */
		typeName?: string;
	}
	/**
	 * ApprovalListInfo
	 */
	export interface ApprovalListInfo {
		/**
		 * 申请时间
		 */
		applyTime: string;
		/**
		 * 审批时间
		 */
		approvalTime: string;
		/**
		 * 申请人企业用户Id
		 */
		corpUserId: number;
		/**
		 * 申请人企业用户名称
		 */
		corpUserName: string;
		/**
		 * 关键词
		 */
		keyword: string;
		/**
		 * 申请人职务
		 */
		position: IdentInfo[];
		/**
		 * 状态 1-待审批 2-同意 3-不同意
		 */
		status: ApprovalStatus;
		/**
		 * 时间标签
		 */
		timeLabel: string;
		/**
		 * 时间标签类型 1-年月 2-年月日
		 */
		timeLabelType: TimeLabelType;
		/**
		 * 申请类型名称
		 */
		typeName: string;
	}
	export interface ReqUuid {
		uuid: string;
	}
	/**
	 * 审批详情
	 * ApprovalDetailResp
	 */
	export interface ApprovalDetail {
		/**
		 * 申请时间
		 */
		applyTime?: string;
		/**
		 * 审批信息列表
		 */
		approvals: ApprovalInfo[];
		/**
		 * 审批类型 5-审批 10-二级审批 15-三级审批
		 */
		approvalType: ApprovalType;
		/**
		 * 申请内容
		 */
		content?: string;
		/**
		 * 申请人企业用户id
		 */
		corpUserId?: number;
		/**
		 * 申请人企业用户名称
		 */
		corpUserName?: string;
		/**
		 * 申请Id
		 */
		id?: number;
		/**
		 * 关键词
		 */
		keyword?: string;
		/**
		 * 船舶信息
		 */
		ships?: IdentInfo[];
		/**
		 * 申请类型 名称前缀如C071 没有前缀用UK-01
		 */
		type?: ApplyType;
		/**
		 * 申请类型名称
		 */
		typeName?: string;
		/**
		 * 申请uuid
		 */
		uuid?: string;
	}

	/**
	 * ApprovalInfo
	 */
	export interface ApprovalInfo {
		/**
		 * 审批意见
		 */
		advice: string;
		/**
		 * 审批时间
		 */
		approvalTime: string;
		/**
		 * 审批/抄送人ID
		 */
		corpUserId: number;
		/**
		 * 审批/抄送人名称
		 */
		corpUserName: string;
		/**
		 * 审批状态 1-待审批 2-同意 3-不同意
		 */
		status: ApprovalStatus;
		/**
		 * 5-审批 10-二级审批 15-三级审批 25-抄送
		 */
		type: ApprovalType;
	}

	/**
	 * 审批请求参数
	 * ApprovalReq
	 */
	export interface ApprovalReq {
		/**
		 * 审批意见
		 */
		advice?: string;
		/**
		 * 审批类型 5-审批 10-二级审批 15-三级审批
		 */
		approvalType?: ApprovalType;
		/**
		 * 审批状态 2-同意 3-不同意
		 */
		status?: ApprovalStatus;
		/**
		 * 申请信息的uuid
		 */
		uuid?: string;
	}
	/**
	 * 审批已阅
	 * ApprovalReadReq
	 */
	export interface ReadReq {
		/**
		 * 申请Id
		 */
		applyId?: number;
		/**
		 * 审批类型 5-审批 10-二级审批 15-三级审批
		 */
		approvalType?: ApprovalType;
	}
	/**
	 * type默认配置请求参数
	 * TypeDefaultReq
	 */
	export interface TypeDefaultReq {
		/**
		 * 申请类型
		 */
		applyType: ApplyType;
	}
	/**
	 * ApprovalDefResp
	 */
	export interface ApprovalDefResp {
		/**
		 * 申请类型
		 */
		applyType: ApplyType;
		/**
		 * 审批/抄送人列表
		 */
		approvals: ApprovalDefInfo[];
		/**
		 * id
		 */
		id: number;
	}

	/**
	 * ApprovalDefInfo
	 */
	export interface ApprovalDefInfo {
		/**
		 * 审批/抄送人Id
		 */
		corpUserId: number;
		/**
		 * 审批/抄送人名称
		 */
		corpUserName: string;
		/**
		 * 审批类型 5-审批 10-二级审批 15-三级审批 25-抄送
		 */
		type: ApprovalType;
	}
	/**
	 * ApprovalListUnReadResp
	 * 审批未读数量
	 */
	export interface ApprovalListUnReadResp {
		/**
		 * 审批未读数量
		 */
		approvalCount: number;
		/**
		 * 抄送未读数量
		 */
		ccCount: number;
	}
}
/** 台账 */
export namespace Ledger {
	/**
	 * LedgerListReq
	 * 台账列表请求参数
	 */
	export interface LedgerListReq {
		/**
		 * 创建人名称
		 */
		corpUserName?: string;
		/**
		 * 流程所属部门名称
		 */
		departName?: string;
		/**
		 * 记录数
		 */
		length?: number;
		/**
		 * 开始的记录数
		 */
		offset?: number;
		/**
		 * 通过时间
		 */
		passTime?: string[];
		/**
		 * 船舶Id
		 */
		shipId?: number;
		/**
		 * 排序字段
		 */
		sortField?: string[];
		/**
		 * 申请类型名称
		 */
		typeName?: string;
		/**
		 * 类别 是否为其他类型 1-是 2-否
		 */
		ledgerType?: YN;
	}

	/**
	 * LedgerListInfo
	 * 台账列表项
	 */
	export interface LedgerListInfo {
		/**
		 * 申请人企业用户Id
		 */
		corpUserId: number;
		/**
		 * 申请人企业用户名称
		 */
		corpUserName: string;
		/**
		 * 申请人部门信息
		 */
		departs: IdentInfo[];
		/**
		 * 关键词
		 */
		keyword: string;
		/**
		 * 通过时间
		 */
		passTime: string;
		/**
		 * 申请人职务信息
		 */
		positions: IdentInfo[];
		/**
		 * 时间标签
		 */
		timeLabel: string;
		/**
		 * 时间标签类型 1-年月 2-年月日
		 */
		timeLabelType: TimeLabelType;
		/**
		 * 申请类型名称
		 */
		typeName: string;
		/**
		 * 申请类型
		 */
		type: ApplyType;
	}
	/**
	 * LedgerDetailResp
	 */
	export interface LedgerDetailResp {
		/**
		 * 申请时间
		 */
		applyTime?: string;
		/**
		 * 审批信息列表
		 */
		approvals: ApprovalInfo[];
		/**
		 * 申请内容
		 */
		content?: string;
		/**
		 * 申请人企业用户id
		 */
		corpUserId?: number;
		/**
		 * 申请人企业用户名称
		 */
		corpUserName?: string;
		/**
		 * 申请Id
		 */
		id?: number;
		/**
		 * 关键词
		 */
		keyword?: string;
		/**
		 * 船舶信息
		 */
		ships?: IdentInfo[];
		/**
		 * 申请类型 名称前缀如C071 没有前缀用UK-01
		 */
		type?: ApplyType;
		/**
		 * 申请类型名称
		 */
		typeName?: string;
		/**
		 * 申请uuid
		 */
		uuid?: string;
	}

	/**
	 * ApprovalInfo
	 */
	export interface ApprovalInfo {
		/**
		 * 审批意见
		 */
		advice: string;
		/**
		 * 审批时间
		 */
		approvalTime: string;
		/**
		 * 审批/抄送人ID
		 */
		corpUserId: number;
		/**
		 * 审批/抄送人名称
		 */
		corpUserName: string;
		/**
		 * 审批状态 1-待审批 2-同意 3-不同意
		 */
		status: Approval.ApprovalStatus;
		/**
		 * 5-审批 10-二级审批 15-三级审批 25-抄送
		 */
		type: ApprovalType;
	}
}

/** 通用type */
/**申请类别*/
export type ApplyType =
	| "B03-1"
	| "B06-5"
	| "B07-1"
	| "B10-1"
	| "B10-2"
	| "B10-3"
	| "B10-4"
	| "B10-5"
	| "B10-6"
	| "B11-2"
	| "B11-3"
	| "B11-4"
	| "B12-1"
	| "C07-1";

/**
 * 船舶
 * IdentInfo
 */
export interface IdentInfo {
	/**
	 * id
	 */
	id: number;
	/**
	 * 名称
	 */
	name: string;
}
/**
 * 审批类型 5-审批 10-二级审批 15-三级审批 25-抄送
 */
export enum ApprovalType {
	/** 5-审批 */
	APPROVAL = 5,
	/** 10-二级审批 */
	SECOND_APPROVAL = 10,
	/** 15-三级审批 */
	THIRD_APPROVAL = 15,
	/** 25-抄送 */
	COPY = 25
}
export enum ApprovalText {
	"审批" = 5,
	"二级审批" = 10,
	"三级审批" = 15,
	"抄送" = 25
}

/**
 * timeLabelType 1-年月 2-年月日
 */
export enum TimeLabelType {
	/** 1-年月 */
	MONTH = 1,
	/** 2-年月日 */
	DAY = 2
}
