import { YN } from "@/enums/global-enums";
/** 摄像头相关模型 */
export namespace Camera {
	/**
	 * 摄像头列表请求参数模型
	 */
	export interface CameraListReq {
		/**
		 * 记录数
		 */
		length?: number;
		/**
		 * 摄像头名称
		 */
		name?: string;
		/**
		 * 开始的记录数
		 */
		offset?: number;
		/**
		 * 是否分组
		 * 1-分组 2-未分组
		 */
		shipGroupState?: YN;
		/**
		 * 船舶ID
		 */
		shipId?: number;
		/**
		 * 是否开启智能预警
		 */
		isOpenAlgRecognition?: YN;
	}

	/**
	 * 摄像头列表响应参数模型
	 */
	export interface CameraListResp {
		list?: ShipCameraListObj[];
		total: number;
	}

	/**
	 * ShipCameraListObj
	 */
	export interface ShipCameraListObj {
		cameraList: CameraListObj[];
		/**
		 * 船舶名称
		 */
		shipName: string;

		/**
		 * 船舶Id
		 */
		shipId: number;
		/**
		 * 船舶Uuid
		 */
		shipUuid: string;
	}

	/**
	 * CameraListObj
	 */
	export interface CameraListObj {
		/**
		 * 视频抓拍状态: 1-已开启 2-未开启
		 */
		captureState: number;
		id: number;
		/**
		 * 预览画面
		 */
		img: string;
		/**
		 * 摄像头状态 1-在线 2-离线
		 */
		lineState: number;
		/**
		 * 摄像头名称
		 */
		name: string;
		uuid: string;
		imgGetTime: string;
		/**
		 * 是否智能监控中
		 */
		isSmartMonitoring: boolean;
	}

	/**
	 * 历史抓拍请求参数模型
	 */
	export interface SnapshotListReq {
		/**
		 * 日期 如：2016-01-02
		 */
		date?: string;
		uuid: string;
	}

	/**
	 * 历史抓拍响应参数模型
	 */
	export interface SnapshotListResp {
		list?: ShipCameraCaptureListObj[];
	}

	/**
	 * ShipCameraCaptureListObj
	 */
	export interface ShipCameraCaptureListObj {
		/**
		 * 照片
		 */
		img: string;
	}
	/**
	 * ShipCameraWarningListReq
	 */
	export interface ShipCameraWarningListReq {
		/**
		 * 摄像头名称
		 */
		cameraName?: string;
		/**
		 * 摄像头uuid
		 */
		cameraUuid?: string;
		/**
		 * 预警时间段
		 */
		createdTimes?: string[];
		/**
		 * 记录数
		 */
		length?: number;
		/**
		 * 开始的记录数
		 */
		offset?: number;
		/**
		 * 预警类型
		 */
		type?: number;
		/**
		 * 预警日期
		 */
		warningDate?: string;
	}

	/**
	 * ShipCameraWarningListResp
	 */
	export interface ShipCameraWarningListResp {
		list?: ShipCameraWarningObj[];
		total: number;
	}

	/**
	 * 预警类别
	 */
	export enum RecognitionType {
		/** 1 疲劳驾驶 */
		FatigueDriving = 1,
		/** 2 人员离岗 */
		PersonnelOffPost = 2,
		/** 3 使用手机 */
		UseMobilePhone = 3,
		/** 4 人员吸烟 */
		PersonnelSmoking = 4
	}
	/**
	 * 预警类别名称
	 */
	export enum RecognitionText {
		/** 疲劳驾驶 */
		"疲劳驾驶" = 1,
		/** 人员离岗 */
		"人员离岗" = 2,
		/** 使用手机 */
		"使用手机" = 3,
		/** 人员吸烟 */
		"人员吸烟" = 4
	}

	/**
	 * ShipCameraWarningObj
	 */
	export interface ShipCameraWarningObj {
		/**
		 * 预警推送
		 */
		alertContent: string[];
		/**
		 * 摄像头名称
		 */
		cameraName: string;
		id: number;
		/**
		 * 摄像头类型
		 */
		type: RecognitionType;
		/**
		 * 图像url
		 */
		urls: string[];
		/**
		 * 预警时间
		 */
		createdTime: string;
	}

	/**
	 * 摄像头详情请求参数模型
	 */
	export interface CameraDetailReq {
		/**
		 * 平台 1-PC 2-MB
		 */
		platform?: number;
		uuid: string;
	}

	/**
	 * 摄像头详情响应参数模型
	 */
	export interface CameraDetailResp {
		accessToken: string;
		/**
		 * 直播模板
		 */
		liveTemp: string;
		/**
		 * 直播地址
		 */
		liveUrl: string;
		/**
		 * 回放模板
		 */
		playbackTemp: string;
		/**
		 * 回放地址
		 */
		playbackUrl: string;
		uuid: string;
	}

	/**
	 * 摄像头状态请求参数模型
	 */
	export interface CameraStateReq {
		/**
		 * 摄像头ID列表
		 */
		ids?: number[];
	}

	/**
	 * 摄像头状态响应参数模型
	 */
	export interface CameraStateResp {
		list?: ShipCameraStateListObj[];
	}

	/**
	 * ShipCameraStateListObj
	 */
	export interface ShipCameraStateListObj {
		id: number;
		/**
		 * 摄像头状态 1-在线 2-离线
		 */
		lineState: number;
	}

	/**
	 * 摄像头编辑请求参数模型
	 */
	export interface CameraEditReq {
		/**
		 * 摄像头名称
		 */
		name?: string;
		/**
		 * 船舶ID
		 */
		shipId?: number;
		uuid: string;
	}

	/**
	 * 船舶下拉列表
	 */
	export interface ShipDropdownListResp {
		list: ShipDropdownInfo[];
	}

	/**
	 * 船舶下拉信息
	 */
	export interface ShipDropdownInfo {
		id: number;
		name: string;
	}
	/**
	 * 摄像头统计
	 */
	export interface ShipCameraStatResp {
		offlineNum: number;
		onlineNum: number;
	}
	/**
	 * 预警人员列表请求
	 */
	export interface PersonListReq {
		length: string;
		offset: string;
	}
	/**
	 * 预警人员列表
	 */
	/**
	 * ShipCameraWarningUserListReq
	 */
	export interface ShipCameraWarningUserListReq {
		/**
		 * 摄像头ID
		 */
		camera_ids?: number[];
		/**
		 * 记录数
		 */
		length?: number;
		/**
		 * 开始的记录数
		 */
		offset?: number;
	}
	/**
	 * ShipCameraWarningUserListResp
	 */
	export interface ShipCameraWarningUserListResp {
		list: WarningUserInfo[];
		/**
		 * 总数
		 */
		total: number;
	}

	/**
	 * WarningUserInfo
	 */
	export interface WarningUserInfo {
		cameraId: number;
		corpUserIds: number[];
	}

	export interface ShipCameraWarningUserUpdateReq {
		list: WarningUserInfo[];
	}
}
