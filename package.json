{"name": "<PERSON><PERSON><PERSON><PERSON>", "private": true, "version": "1.12.0", "description": "大船师后台管理系统", "author": "Ningbo Yedu Information Technology Co., Ltd.", "scripts": {"dev": "vite", "prod": "vite --mode production", "test": "vite --mode test", "onlinetest": "vite --mode onlinetest", "release": "vite --mode release", "serve": "vite", "build:dev": "vue-tsc --noEmit && vite build --mode development", "build:test": "vue-tsc --noEmit && vite build --mode test", "build:release": "vue-tsc --noEmit && vite build --mode release", "build:prod": "vue-tsc --noEmit && vite build --mode production", "preview": "vite preview", "lint:eslint": "eslint --fix --ext .js,.ts,.vue ./src", "lint:prettier": "prettier --write --loglevel warn \"src/**/*.{js,ts,json,tsx,css,less,scss,vue,html,md}\"", "lint:stylelint": "stylelint --cache --fix \"**/*.{vue,less,postcss,css,scss}\" --cache --cache-location node_modules/.cache/stylelint/", "lint:lint-staged": "lint-staged", "prepare": "husky install", "commit": "git pull && git add -A && git-cz && git push"}, "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "@element-plus/icons-vue": "^1.1.4", "@fullcalendar/core": "^6.1.4", "@fullcalendar/daygrid": "^6.1.4", "@fullcalendar/interaction": "^6.1.4", "@fullcalendar/moment": "^6.1.4", "@fullcalendar/resource": "^6.1.4", "@fullcalendar/resource-timeline": "^6.1.4", "@fullcalendar/vue3": "^6.1.4", "@kjgl77/datav-vue3": "^1.6.1", "@vue-office/excel": "^1.4.0", "@vueuse/core": "^10.3.0", "@vueuse/router": "^10.3.0", "@wangeditor/editor": "^5.1.12", "@wangeditor/editor-for-vue": "^5.1.12", "@zumer/snapdom": "^1.8.0", "ali-oss": "^6.17.1", "axios": "^0.27.2", "date-fns": "^2.29.3", "docx-preview": "^0.1.18", "echarts": "^5.3.0", "echarts-liquidfill": "^3.1.0", "element-plus": "^2.5.3", "exceljs": "^4.4.0", "ezuikit-js": "^7.6.4", "file-saver": "^2.0.5", "js-md5": "^0.7.3", "luckyexcel": "^1.0.1", "mitt": "^3.0.0", "nprogress": "^0.2.0", "pinia": "^2.0.12", "pinia-plugin-persistedstate": "^1.6.1", "qs": "^6.11.0", "tippy.js": "^6.3.7", "v-scale-screen": "^2.2.0", "vue": "^3.2.45", "vue-demi": "^0.13.11", "vue-i18n": "^9.1.9", "vue-pdf-embed": "^1.1.6", "vue-router": "^4.0.12", "vue3-pdfjs": "^0.1.6", "vuedraggable": "^4.1.0", "xlsx": "^0.18.5", "xlsx-js-style": "^1.2.0"}, "devDependencies": {"@commitlint/cli": "^17.0.1", "@commitlint/config-conventional": "^17.0.0", "@types/ali-oss": "^6.16.7", "@types/file-saver": "^2.0.5", "@types/node": "^17.0.31", "@typescript-eslint/eslint-plugin": "^5.22.0", "@typescript-eslint/parser": "^5.22.0", "@vitejs/plugin-vue": "^4.0.0", "@vitejs/plugin-vue-jsx": "^1.3.10", "autoprefixer": "^10.4.7", "commitizen": "^4.2.4", "cropperjs": "^1.5.13", "cz-git": "^1.3.2", "driver.js": "^0.9.8", "eslint": "^8.14.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-vue": "^8.7.1", "husky": "^8.0.1", "lint-staged": "^12.4.2", "npm": "^6.14.10", "postcss": "^8.4.14", "postcss-html": "^1.4.1", "prettier": "^2.6.2", "rollup-plugin-visualizer": "^5.5.4", "sass": "^1.49.7", "script-loader": "^0.7.2", "standard-version": "^9.5.0", "stylelint": "^14.8.5", "stylelint-config-html": "^1.0.0", "stylelint-config-prettier": "^9.0.3", "stylelint-config-recess-order": "^3.0.0", "stylelint-config-recommended-scss": "^6.0.0", "stylelint-config-recommended-vue": "^1.4.0", "stylelint-config-standard": "^25.0.0", "stylelint-config-standard-scss": "^3.0.0", "typescript": "^4.9.3", "unplugin-auto-import": "^0.6.0", "unplugin-vue-components": "^0.17.18", "vite": "^4.1.3", "vite-plugin-cdn-import": "^0.3.5", "vite-plugin-compression": "^0.5.1", "vite-plugin-eslint": "^1.8.1", "vite-plugin-html": "^3.2.0", "vite-plugin-require-transform": "^1.0.21", "vite-plugin-svg-icons": "^2.0.1", "vite-plugin-vue-setup-extend": "^0.4.0", "vue-tsc": "^1.0.11"}, "config": {"commitizen": {"path": "node_modules/cz-git"}}}